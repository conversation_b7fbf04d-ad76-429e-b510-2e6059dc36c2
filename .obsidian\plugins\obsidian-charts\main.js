/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source visit the plugins github repository (https://github.com/phibr0/obsidian-dictionary)
*/

var ob=Object.create;var Hi=Object.defineProperty,ab=Object.defineProperties,lb=Object.getOwnPropertyDescriptor,cb=Object.getOwnPropertyDescriptors,hb=Object.getOwnPropertyNames,Us=Object.getOwnPropertySymbols,ub=Object.getPrototypeOf,Yo=Object.prototype.hasOwnProperty,Vc=Object.prototype.propertyIsEnumerable;var Yc=(n,t,e)=>t in n?Hi(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e,Nn=(n,t)=>{for(var e in t||(t={}))Yo.call(t,e)&&Yc(n,e,t[e]);if(Us)for(var e of Us(t))Vc.call(t,e)&&Yc(n,e,t[e]);return n},_i=(n,t)=>ab(n,cb(t)),Xc=n=>Hi(n,"__esModule",{value:!0});var qc=(n,t)=>{var e={};for(var i in n)Yo.call(n,i)&&t.indexOf(i)<0&&(e[i]=n[i]);if(n!=null&&Us)for(var i of Us(n))t.indexOf(i)<0&&Vc.call(n,i)&&(e[i]=n[i]);return e};var Xo=(n,t)=>()=>(t||n((t={exports:{}}).exports,t),t.exports),fb=(n,t)=>{Xc(n);for(var e in t)Hi(n,e,{get:t[e],enumerable:!0})},db=(n,t,e)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of hb(t))!Yo.call(n,i)&&i!=="default"&&Hi(n,i,{get:()=>t[i],enumerable:!(e=lb(t,i))||e.enumerable});return n},ze=n=>db(Xc(Hi(n!=null?ob(ub(n)):{},"default",n&&n.__esModule&&"default"in n?{get:()=>n.default,enumerable:!0}:{value:n,enumerable:!0})),n);var Ku=Xo((qa,Ga)=>{(function(n,t){typeof qa=="object"&&typeof Ga!="undefined"?Ga.exports=t():typeof define=="function"&&define.amd?define(t):(n=typeof globalThis!="undefined"?globalThis:n||self,n.chroma=t())})(qa,function(){"use strict";for(var n=function(u,d,b){return d===void 0&&(d=0),b===void 0&&(b=1),u<d?d:u>b?b:u},t=n,e=function(u){u._clipped=!1,u._unclipped=u.slice(0);for(var d=0;d<=3;d++)d<3?((u[d]<0||u[d]>255)&&(u._clipped=!0),u[d]=t(u[d],0,255)):d===3&&(u[d]=t(u[d],0,1));return u},i={},s=0,r=["Boolean","Number","String","Function","Array","Date","RegExp","Undefined","Null"];s<r.length;s+=1){var o=r[s];i["[object "+o+"]"]=o.toLowerCase()}var a=function(u){return i[Object.prototype.toString.call(u)]||"object"},l=a,c=function(u,d){return d===void 0&&(d=null),u.length>=3?Array.prototype.slice.call(u):l(u[0])=="object"&&d?d.split("").filter(function(b){return u[0][b]!==void 0}).map(function(b){return u[0][b]}):u[0]},h=a,f=function(u){if(u.length<2)return null;var d=u.length-1;return h(u[d])=="string"?u[d].toLowerCase():null},g=Math.PI,p={clip_rgb:e,limit:n,type:a,unpack:c,last:f,PI:g,TWOPI:g*2,PITHIRD:g/3,DEG2RAD:g/180,RAD2DEG:180/g},m={format:{},autodetect:[]},y=p.last,S=p.clip_rgb,M=p.type,C=m,F=function(){for(var d=[],b=arguments.length;b--;)d[b]=arguments[b];var _=this;if(M(d[0])==="object"&&d[0].constructor&&d[0].constructor===this.constructor)return d[0];var P=y(d),T=!1;if(!P){T=!0,C.sorted||(C.autodetect=C.autodetect.sort(function(B,q){return q.p-B.p}),C.sorted=!0);for(var w=0,A=C.autodetect;w<A.length;w+=1){var L=A[w];if(P=L.test.apply(L,d),P)break}}if(C.format[P]){var j=C.format[P].apply(null,T?d:d.slice(0,-1));_._rgb=S(j)}else throw new Error("unknown format: "+d);_._rgb.length===3&&_._rgb.push(1)};F.prototype.toString=function(){return M(this.hex)=="function"?this.hex():"["+this._rgb.join(",")+"]"};var D=F,I=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(I.Color,[null].concat(u)))};I.Color=D,I.version="2.4.2";var $=I,N=p.unpack,G=Math.max,U=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=N(u,"rgb"),_=b[0],P=b[1],T=b[2];_=_/255,P=P/255,T=T/255;var w=1-G(_,G(P,T)),A=w<1?1/(1-w):0,L=(1-_-w)*A,j=(1-P-w)*A,B=(1-T-w)*A;return[L,j,B,w]},it=U,lt=p.unpack,rt=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=lt(u,"cmyk");var b=u[0],_=u[1],P=u[2],T=u[3],w=u.length>4?u[4]:1;return T===1?[0,0,0,w]:[b>=1?0:255*(1-b)*(1-T),_>=1?0:255*(1-_)*(1-T),P>=1?0:255*(1-P)*(1-T),w]},Pt=rt,zt=$,et=D,Et=m,St=p.unpack,re=p.type,ye=it;et.prototype.cmyk=function(){return ye(this._rgb)},zt.cmyk=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(et,[null].concat(u,["cmyk"])))},Et.format.cmyk=Pt,Et.autodetect.push({p:2,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=St(u,"cmyk"),re(u)==="array"&&u.length===4)return"cmyk"}});var ot=p.unpack,Lt=p.last,Bt=function(u){return Math.round(u*100)/100},Ut=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=ot(u,"hsla"),_=Lt(u)||"lsa";return b[0]=Bt(b[0]||0),b[1]=Bt(b[1]*100)+"%",b[2]=Bt(b[2]*100)+"%",_==="hsla"||b.length>3&&b[3]<1?(b[3]=b.length>3?b[3]:1,_="hsla"):b.length=3,_+"("+b.join(",")+")"},Qt=Ut,k=p.unpack,v=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=k(u,"rgba");var b=u[0],_=u[1],P=u[2];b/=255,_/=255,P/=255;var T=Math.min(b,_,P),w=Math.max(b,_,P),A=(w+T)/2,L,j;return w===T?(L=0,j=Number.NaN):L=A<.5?(w-T)/(w+T):(w-T)/(2-w-T),b==w?j=(_-P)/(w-T):_==w?j=2+(P-b)/(w-T):P==w&&(j=4+(b-_)/(w-T)),j*=60,j<0&&(j+=360),u.length>3&&u[3]!==void 0?[j,L,A,u[3]]:[j,L,A]},x=v,R=p.unpack,E=p.last,O=Qt,Z=x,V=Math.round,J=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=R(u,"rgba"),_=E(u)||"rgb";return _.substr(0,3)=="hsl"?O(Z(b),_):(b[0]=V(b[0]),b[1]=V(b[1]),b[2]=V(b[2]),(_==="rgba"||b.length>3&&b[3]<1)&&(b[3]=b.length>3?b[3]:1,_="rgba"),_+"("+b.slice(0,_==="rgb"?3:4).join(",")+")")},tt=J,xt=p.unpack,Ht=Math.round,Ot=function(){for(var u,d=[],b=arguments.length;b--;)d[b]=arguments[b];d=xt(d,"hsl");var _=d[0],P=d[1],T=d[2],w,A,L;if(P===0)w=A=L=T*255;else{var j=[0,0,0],B=[0,0,0],q=T<.5?T*(1+P):T+P-T*P,H=2*T-q,Q=_/360;j[0]=Q+1/3,j[1]=Q,j[2]=Q-1/3;for(var K=0;K<3;K++)j[K]<0&&(j[K]+=1),j[K]>1&&(j[K]-=1),6*j[K]<1?B[K]=H+(q-H)*6*j[K]:2*j[K]<1?B[K]=q:3*j[K]<2?B[K]=H+(q-H)*(2/3-j[K])*6:B[K]=H;u=[Ht(B[0]*255),Ht(B[1]*255),Ht(B[2]*255)],w=u[0],A=u[1],L=u[2]}return d.length>3?[w,A,L,d[3]]:[w,A,L,1]},Yt=Ot,Xt=Yt,ue=m,ke=/^rgb\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*\)$/,xe=/^rgba\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*,\s*([01]|[01]?\.\d+)\)$/,Rn=/^rgb\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/,un=/^rgba\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,Ln=/^hsl\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/,je=/^hsla\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,$i=Math.round,Fn=function(u){u=u.toLowerCase().trim();var d;if(ue.format.named)try{return ue.format.named(u)}catch(K){}if(d=u.match(ke)){for(var b=d.slice(1,4),_=0;_<3;_++)b[_]=+b[_];return b[3]=1,b}if(d=u.match(xe)){for(var P=d.slice(1,5),T=0;T<4;T++)P[T]=+P[T];return P}if(d=u.match(Rn)){for(var w=d.slice(1,4),A=0;A<3;A++)w[A]=$i(w[A]*2.55);return w[3]=1,w}if(d=u.match(un)){for(var L=d.slice(1,5),j=0;j<3;j++)L[j]=$i(L[j]*2.55);return L[3]=+L[3],L}if(d=u.match(Ln)){var B=d.slice(1,4);B[1]*=.01,B[2]*=.01;var q=Xt(B);return q[3]=1,q}if(d=u.match(je)){var H=d.slice(1,4);H[1]*=.01,H[2]*=.01;var Q=Xt(H);return Q[3]=+d[4],Q}};Fn.test=function(u){return ke.test(u)||xe.test(u)||Rn.test(u)||un.test(u)||Ln.test(u)||je.test(u)};var oi=Fn,$s=$,ji=D,ai=m,qe=p.type,In=tt,$n=oi;ji.prototype.css=function(u){return In(this._rgb,u)},$s.css=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(ji,[null].concat(u,["css"])))},ai.format.css=$n,ai.autodetect.push({p:5,test:function(u){for(var d=[],b=arguments.length-1;b-- >0;)d[b]=arguments[b+1];if(!d.length&&qe(u)==="string"&&$n.test(u))return"css"}});var li=D,js=$,fn=m,ce=p.unpack;fn.format.gl=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=ce(u,"rgba");return b[0]*=255,b[1]*=255,b[2]*=255,b},js.gl=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(li,[null].concat(u,["gl"])))},li.prototype.gl=function(){var u=this._rgb;return[u[0]/255,u[1]/255,u[2]/255,u[3]]};var bt=p.unpack,te=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=bt(u,"rgb"),_=b[0],P=b[1],T=b[2],w=Math.min(_,P,T),A=Math.max(_,P,T),L=A-w,j=L*100/255,B=w/(255-L)*100,q;return L===0?q=Number.NaN:(_===A&&(q=(P-T)/L),P===A&&(q=2+(T-_)/L),T===A&&(q=4+(_-P)/L),q*=60,q<0&&(q+=360)),[q,j,B]},dn=te,pe=p.unpack,zs=Math.floor,vd=function(){for(var u,d,b,_,P,T,w=[],A=arguments.length;A--;)w[A]=arguments[A];w=pe(w,"hcg");var L=w[0],j=w[1],B=w[2],q,H,Q;B=B*255;var K=j*255;if(j===0)q=H=Q=B;else{L===360&&(L=0),L>360&&(L-=360),L<0&&(L+=360),L/=60;var ct=zs(L),mt=L-ct,_t=B*(1-j),Mt=_t+K*(1-mt),oe=_t+K*mt,ne=_t+K;switch(ct){case 0:u=[ne,oe,_t],q=u[0],H=u[1],Q=u[2];break;case 1:d=[Mt,ne,_t],q=d[0],H=d[1],Q=d[2];break;case 2:b=[_t,ne,oe],q=b[0],H=b[1],Q=b[2];break;case 3:_=[_t,Mt,ne],q=_[0],H=_[1],Q=_[2];break;case 4:P=[oe,_t,ne],q=P[0],H=P[1],Q=P[2];break;case 5:T=[ne,_t,Mt],q=T[0],H=T[1],Q=T[2];break}}return[q,H,Q,w.length>3?w[3]:1]},_d=vd,yd=p.unpack,xd=p.type,wd=$,Al=D,Rl=m,kd=dn;Al.prototype.hcg=function(){return kd(this._rgb)},wd.hcg=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(Al,[null].concat(u,["hcg"])))},Rl.format.hcg=_d,Rl.autodetect.push({p:1,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=yd(u,"hcg"),xd(u)==="array"&&u.length===3)return"hcg"}});var Sd=p.unpack,Md=p.last,Bs=Math.round,Cd=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=Sd(u,"rgba"),_=b[0],P=b[1],T=b[2],w=b[3],A=Md(u)||"auto";w===void 0&&(w=1),A==="auto"&&(A=w<1?"rgba":"rgb"),_=Bs(_),P=Bs(P),T=Bs(T);var L=_<<16|P<<8|T,j="000000"+L.toString(16);j=j.substr(j.length-6);var B="0"+Bs(w*255).toString(16);switch(B=B.substr(B.length-2),A.toLowerCase()){case"rgba":return"#"+j+B;case"argb":return"#"+B+j;default:return"#"+j}},Ll=Cd,Pd=/^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,Td=/^#?([A-Fa-f0-9]{8}|[A-Fa-f0-9]{4})$/,Dd=function(u){if(u.match(Pd)){(u.length===4||u.length===7)&&(u=u.substr(1)),u.length===3&&(u=u.split(""),u=u[0]+u[0]+u[1]+u[1]+u[2]+u[2]);var d=parseInt(u,16),b=d>>16,_=d>>8&255,P=d&255;return[b,_,P,1]}if(u.match(Td)){(u.length===5||u.length===9)&&(u=u.substr(1)),u.length===4&&(u=u.split(""),u=u[0]+u[0]+u[1]+u[1]+u[2]+u[2]+u[3]+u[3]);var T=parseInt(u,16),w=T>>24&255,A=T>>16&255,L=T>>8&255,j=Math.round((T&255)/255*100)/100;return[w,A,L,j]}throw new Error("unknown hex color: "+u)},Fl=Dd,Ed=$,Il=D,Od=p.type,$l=m,Ad=Ll;Il.prototype.hex=function(u){return Ad(this._rgb,u)},Ed.hex=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(Il,[null].concat(u,["hex"])))},$l.format.hex=Fl,$l.autodetect.push({p:4,test:function(u){for(var d=[],b=arguments.length-1;b-- >0;)d[b]=arguments[b+1];if(!d.length&&Od(u)==="string"&&[3,4,5,6,7,8,9].indexOf(u.length)>=0)return"hex"}});var Rd=p.unpack,jl=p.TWOPI,Ld=Math.min,Fd=Math.sqrt,Id=Math.acos,$d=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=Rd(u,"rgb"),_=b[0],P=b[1],T=b[2];_/=255,P/=255,T/=255;var w,A=Ld(_,P,T),L=(_+P+T)/3,j=L>0?1-A/L:0;return j===0?w=NaN:(w=(_-P+(_-T))/2,w/=Fd((_-P)*(_-P)+(_-T)*(P-T)),w=Id(w),T>P&&(w=jl-w),w/=jl),[w*360,j,L]},jd=$d,zd=p.unpack,vo=p.limit,ci=p.TWOPI,_o=p.PITHIRD,hi=Math.cos,Bd=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=zd(u,"hsi");var b=u[0],_=u[1],P=u[2],T,w,A;return isNaN(b)&&(b=0),isNaN(_)&&(_=0),b>360&&(b-=360),b<0&&(b+=360),b/=360,b<1/3?(A=(1-_)/3,T=(1+_*hi(ci*b)/hi(_o-ci*b))/3,w=1-(A+T)):b<2/3?(b-=1/3,T=(1-_)/3,w=(1+_*hi(ci*b)/hi(_o-ci*b))/3,A=1-(T+w)):(b-=2/3,w=(1-_)/3,A=(1+_*hi(ci*b)/hi(_o-ci*b))/3,T=1-(w+A)),T=vo(P*T*3),w=vo(P*w*3),A=vo(P*A*3),[T*255,w*255,A*255,u.length>3?u[3]:1]},Nd=Bd,Hd=p.unpack,Wd=p.type,Vd=$,zl=D,Bl=m,Yd=jd;zl.prototype.hsi=function(){return Yd(this._rgb)},Vd.hsi=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(zl,[null].concat(u,["hsi"])))},Bl.format.hsi=Nd,Bl.autodetect.push({p:2,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=Hd(u,"hsi"),Wd(u)==="array"&&u.length===3)return"hsi"}});var Xd=p.unpack,qd=p.type,Gd=$,Nl=D,Hl=m,Ud=x;Nl.prototype.hsl=function(){return Ud(this._rgb)},Gd.hsl=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(Nl,[null].concat(u,["hsl"])))},Hl.format.hsl=Yt,Hl.autodetect.push({p:2,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=Xd(u,"hsl"),qd(u)==="array"&&u.length===3)return"hsl"}});var Kd=p.unpack,Zd=Math.min,Jd=Math.max,Qd=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=Kd(u,"rgb");var b=u[0],_=u[1],P=u[2],T=Zd(b,_,P),w=Jd(b,_,P),A=w-T,L,j,B;return B=w/255,w===0?(L=Number.NaN,j=0):(j=A/w,b===w&&(L=(_-P)/A),_===w&&(L=2+(P-b)/A),P===w&&(L=4+(b-_)/A),L*=60,L<0&&(L+=360)),[L,j,B]},tp=Qd,ep=p.unpack,np=Math.floor,ip=function(){for(var u,d,b,_,P,T,w=[],A=arguments.length;A--;)w[A]=arguments[A];w=ep(w,"hsv");var L=w[0],j=w[1],B=w[2],q,H,Q;if(B*=255,j===0)q=H=Q=B;else{L===360&&(L=0),L>360&&(L-=360),L<0&&(L+=360),L/=60;var K=np(L),ct=L-K,mt=B*(1-j),_t=B*(1-j*ct),Mt=B*(1-j*(1-ct));switch(K){case 0:u=[B,Mt,mt],q=u[0],H=u[1],Q=u[2];break;case 1:d=[_t,B,mt],q=d[0],H=d[1],Q=d[2];break;case 2:b=[mt,B,Mt],q=b[0],H=b[1],Q=b[2];break;case 3:_=[mt,_t,B],q=_[0],H=_[1],Q=_[2];break;case 4:P=[Mt,mt,B],q=P[0],H=P[1],Q=P[2];break;case 5:T=[B,mt,_t],q=T[0],H=T[1],Q=T[2];break}}return[q,H,Q,w.length>3?w[3]:1]},sp=ip,rp=p.unpack,op=p.type,ap=$,Wl=D,Vl=m,lp=tp;Wl.prototype.hsv=function(){return lp(this._rgb)},ap.hsv=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(Wl,[null].concat(u,["hsv"])))},Vl.format.hsv=sp,Vl.autodetect.push({p:2,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=rp(u,"hsv"),op(u)==="array"&&u.length===3)return"hsv"}});var Ns={Kn:18,Xn:.95047,Yn:1,Zn:1.08883,t0:.137931034,t1:.206896552,t2:.12841855,t3:.008856452},ui=Ns,cp=p.unpack,Yl=Math.pow,hp=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=cp(u,"rgb"),_=b[0],P=b[1],T=b[2],w=up(_,P,T),A=w[0],L=w[1],j=w[2],B=116*L-16;return[B<0?0:B,500*(A-L),200*(L-j)]},yo=function(u){return(u/=255)<=.04045?u/12.92:Yl((u+.055)/1.055,2.4)},xo=function(u){return u>ui.t3?Yl(u,1/3):u/ui.t2+ui.t0},up=function(u,d,b){u=yo(u),d=yo(d),b=yo(b);var _=xo((.4124564*u+.3575761*d+.1804375*b)/ui.Xn),P=xo((.2126729*u+.7151522*d+.072175*b)/ui.Yn),T=xo((.0193339*u+.119192*d+.9503041*b)/ui.Zn);return[_,P,T]},Xl=hp,fi=Ns,fp=p.unpack,dp=Math.pow,pp=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=fp(u,"lab");var b=u[0],_=u[1],P=u[2],T,w,A,L,j,B;return w=(b+16)/116,T=isNaN(_)?w:w+_/500,A=isNaN(P)?w:w-P/200,w=fi.Yn*ko(w),T=fi.Xn*ko(T),A=fi.Zn*ko(A),L=wo(3.2404542*T-1.5371385*w-.4985314*A),j=wo(-.969266*T+1.8760108*w+.041556*A),B=wo(.0556434*T-.2040259*w+1.0572252*A),[L,j,B,u.length>3?u[3]:1]},wo=function(u){return 255*(u<=.00304?12.92*u:1.055*dp(u,1/2.4)-.055)},ko=function(u){return u>fi.t1?u*u*u:fi.t2*(u-fi.t0)},ql=pp,gp=p.unpack,mp=p.type,bp=$,Gl=D,Ul=m,vp=Xl;Gl.prototype.lab=function(){return vp(this._rgb)},bp.lab=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(Gl,[null].concat(u,["lab"])))},Ul.format.lab=ql,Ul.autodetect.push({p:2,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=gp(u,"lab"),mp(u)==="array"&&u.length===3)return"lab"}});var _p=p.unpack,yp=p.RAD2DEG,xp=Math.sqrt,wp=Math.atan2,kp=Math.round,Sp=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=_p(u,"lab"),_=b[0],P=b[1],T=b[2],w=xp(P*P+T*T),A=(wp(T,P)*yp+360)%360;return kp(w*1e4)===0&&(A=Number.NaN),[_,w,A]},Kl=Sp,Mp=p.unpack,Cp=Xl,Pp=Kl,Tp=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=Mp(u,"rgb"),_=b[0],P=b[1],T=b[2],w=Cp(_,P,T),A=w[0],L=w[1],j=w[2];return Pp(A,L,j)},Dp=Tp,Ep=p.unpack,Op=p.DEG2RAD,Ap=Math.sin,Rp=Math.cos,Lp=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=Ep(u,"lch"),_=b[0],P=b[1],T=b[2];return isNaN(T)&&(T=0),T=T*Op,[_,Rp(T)*P,Ap(T)*P]},Zl=Lp,Fp=p.unpack,Ip=Zl,$p=ql,jp=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=Fp(u,"lch");var b=u[0],_=u[1],P=u[2],T=Ip(b,_,P),w=T[0],A=T[1],L=T[2],j=$p(w,A,L),B=j[0],q=j[1],H=j[2];return[B,q,H,u.length>3?u[3]:1]},Jl=jp,zp=p.unpack,Bp=Jl,Np=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=zp(u,"hcl").reverse();return Bp.apply(void 0,b)},Hp=Np,Wp=p.unpack,Vp=p.type,Ql=$,Hs=D,So=m,tc=Dp;Hs.prototype.lch=function(){return tc(this._rgb)},Hs.prototype.hcl=function(){return tc(this._rgb).reverse()},Ql.lch=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(Hs,[null].concat(u,["lch"])))},Ql.hcl=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(Hs,[null].concat(u,["hcl"])))},So.format.lch=Jl,So.format.hcl=Hp,["lch","hcl"].forEach(function(u){return So.autodetect.push({p:2,test:function(){for(var d=[],b=arguments.length;b--;)d[b]=arguments[b];if(d=Wp(d,u),Vp(d)==="array"&&d.length===3)return u}})});var Yp={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflower:"#6495ed",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",laserlemon:"#ffff54",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrod:"#fafad2",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",maroon2:"#7f0000",maroon3:"#b03060",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",purple2:"#7f007f",purple3:"#a020f0",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},ec=Yp,Xp=D,nc=m,qp=p.type,zi=ec,Gp=Fl,Up=Ll;Xp.prototype.name=function(){for(var u=Up(this._rgb,"rgb"),d=0,b=Object.keys(zi);d<b.length;d+=1){var _=b[d];if(zi[_]===u)return _.toLowerCase()}return u},nc.format.named=function(u){if(u=u.toLowerCase(),zi[u])return Gp(zi[u]);throw new Error("unknown color name: "+u)},nc.autodetect.push({p:5,test:function(u){for(var d=[],b=arguments.length-1;b-- >0;)d[b]=arguments[b+1];if(!d.length&&qp(u)==="string"&&zi[u.toLowerCase()])return"named"}});var Kp=p.unpack,Zp=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=Kp(u,"rgb"),_=b[0],P=b[1],T=b[2];return(_<<16)+(P<<8)+T},Jp=Zp,Qp=p.type,tg=function(u){if(Qp(u)=="number"&&u>=0&&u<=16777215){var d=u>>16,b=u>>8&255,_=u&255;return[d,b,_,1]}throw new Error("unknown num color: "+u)},eg=tg,ng=$,ic=D,sc=m,ig=p.type,sg=Jp;ic.prototype.num=function(){return sg(this._rgb)},ng.num=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(ic,[null].concat(u,["num"])))},sc.format.num=eg,sc.autodetect.push({p:5,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u.length===1&&ig(u[0])==="number"&&u[0]>=0&&u[0]<=16777215)return"num"}});var rg=$,Mo=D,rc=m,oc=p.unpack,ac=p.type,lc=Math.round;Mo.prototype.rgb=function(u){return u===void 0&&(u=!0),u===!1?this._rgb.slice(0,3):this._rgb.slice(0,3).map(lc)},Mo.prototype.rgba=function(u){return u===void 0&&(u=!0),this._rgb.slice(0,4).map(function(d,b){return b<3?u===!1?d:lc(d):d})},rg.rgb=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(Mo,[null].concat(u,["rgb"])))},rc.format.rgb=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=oc(u,"rgba");return b[3]===void 0&&(b[3]=1),b},rc.autodetect.push({p:3,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=oc(u,"rgba"),ac(u)==="array"&&(u.length===3||u.length===4&&ac(u[3])=="number"&&u[3]>=0&&u[3]<=1))return"rgb"}});var Ws=Math.log,og=function(u){var d=u/100,b,_,P;return d<66?(b=255,_=d<6?0:-155.25485562709179-.44596950469579133*(_=d-2)+104.49216199393888*Ws(_),P=d<20?0:-254.76935184120902+.8274096064007395*(P=d-10)+115.67994401066147*Ws(P)):(b=351.97690566805693+.114206453784165*(b=d-55)-40.25366309332127*Ws(b),_=325.4494125711974+.07943456536662342*(_=d-50)-28.0852963507957*Ws(_),P=255),[b,_,P,1]},cc=og,ag=cc,lg=p.unpack,cg=Math.round,hg=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];for(var b=lg(u,"rgb"),_=b[0],P=b[2],T=1e3,w=4e4,A=.4,L;w-T>A;){L=(w+T)*.5;var j=ag(L);j[2]/j[0]>=P/_?w=L:T=L}return cg(L)},ug=hg,Co=$,Vs=D,Po=m,fg=ug;Vs.prototype.temp=Vs.prototype.kelvin=Vs.prototype.temperature=function(){return fg(this._rgb)},Co.temp=Co.kelvin=Co.temperature=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(Vs,[null].concat(u,["temp"])))},Po.format.temp=Po.format.kelvin=Po.format.temperature=cc;var dg=p.unpack,To=Math.cbrt,pg=Math.pow,gg=Math.sign,mg=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=dg(u,"rgb"),_=b[0],P=b[1],T=b[2],w=[Do(_/255),Do(P/255),Do(T/255)],A=w[0],L=w[1],j=w[2],B=To(.4122214708*A+.5363325363*L+.0514459929*j),q=To(.2119034982*A+.6806995451*L+.1073969566*j),H=To(.0883024619*A+.2817188376*L+.6299787005*j);return[.2104542553*B+.793617785*q-.0040720468*H,1.9779984951*B-2.428592205*q+.4505937099*H,.0259040371*B+.7827717662*q-.808675766*H]},hc=mg;function Do(u){var d=Math.abs(u);return d<.04045?u/12.92:(gg(u)||1)*pg((d+.055)/1.055,2.4)}var bg=p.unpack,Ys=Math.pow,vg=Math.sign,_g=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=bg(u,"lab");var b=u[0],_=u[1],P=u[2],T=Ys(b+.3963377774*_+.2158037573*P,3),w=Ys(b-.1055613458*_-.0638541728*P,3),A=Ys(b-.0894841775*_-1.291485548*P,3);return[255*Eo(4.0767416621*T-3.3077115913*w+.2309699292*A),255*Eo(-1.2684380046*T+2.6097574011*w-.3413193965*A),255*Eo(-.0041960863*T-.7034186147*w+1.707614701*A),u.length>3?u[3]:1]},uc=_g;function Eo(u){var d=Math.abs(u);return d>.0031308?(vg(u)||1)*(1.055*Ys(d,1/2.4)-.055):u*12.92}var yg=p.unpack,xg=p.type,wg=$,fc=D,dc=m,kg=hc;fc.prototype.oklab=function(){return kg(this._rgb)},wg.oklab=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(fc,[null].concat(u,["oklab"])))},dc.format.oklab=uc,dc.autodetect.push({p:3,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=yg(u,"oklab"),xg(u)==="array"&&u.length===3)return"oklab"}});var Sg=p.unpack,Mg=hc,Cg=Kl,Pg=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var b=Sg(u,"rgb"),_=b[0],P=b[1],T=b[2],w=Mg(_,P,T),A=w[0],L=w[1],j=w[2];return Cg(A,L,j)},Tg=Pg,Dg=p.unpack,Eg=Zl,Og=uc,Ag=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=Dg(u,"lch");var b=u[0],_=u[1],P=u[2],T=Eg(b,_,P),w=T[0],A=T[1],L=T[2],j=Og(w,A,L),B=j[0],q=j[1],H=j[2];return[B,q,H,u.length>3?u[3]:1]},Rg=Ag,Lg=p.unpack,Fg=p.type,Ig=$,pc=D,gc=m,$g=Tg;pc.prototype.oklch=function(){return $g(this._rgb)},Ig.oklch=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(pc,[null].concat(u,["oklch"])))},gc.format.oklch=Rg,gc.autodetect.push({p:3,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=Lg(u,"oklch"),Fg(u)==="array"&&u.length===3)return"oklch"}});var mc=D,jg=p.type;mc.prototype.alpha=function(u,d){return d===void 0&&(d=!1),u!==void 0&&jg(u)==="number"?d?(this._rgb[3]=u,this):new mc([this._rgb[0],this._rgb[1],this._rgb[2],u],"rgb"):this._rgb[3]};var zg=D;zg.prototype.clipped=function(){return this._rgb._clipped||!1};var jn=D,Bg=Ns;jn.prototype.darken=function(u){u===void 0&&(u=1);var d=this,b=d.lab();return b[0]-=Bg.Kn*u,new jn(b,"lab").alpha(d.alpha(),!0)},jn.prototype.brighten=function(u){return u===void 0&&(u=1),this.darken(-u)},jn.prototype.darker=jn.prototype.darken,jn.prototype.brighter=jn.prototype.brighten;var Ng=D;Ng.prototype.get=function(u){var d=u.split("."),b=d[0],_=d[1],P=this[b]();if(_){var T=b.indexOf(_)-(b.substr(0,2)==="ok"?2:0);if(T>-1)return P[T];throw new Error("unknown channel "+_+" in mode "+b)}else return P};var di=D,Hg=p.type,Wg=Math.pow,Vg=1e-7,Yg=20;di.prototype.luminance=function(u){if(u!==void 0&&Hg(u)==="number"){if(u===0)return new di([0,0,0,this._rgb[3]],"rgb");if(u===1)return new di([255,255,255,this._rgb[3]],"rgb");var d=this.luminance(),b="rgb",_=Yg,P=function(w,A){var L=w.interpolate(A,.5,b),j=L.luminance();return Math.abs(u-j)<Vg||!_--?L:j>u?P(w,L):P(L,A)},T=(d>u?P(new di([0,0,0]),this):P(this,new di([255,255,255]))).rgb();return new di(T.concat([this._rgb[3]]))}return Xg.apply(void 0,this._rgb.slice(0,3))};var Xg=function(u,d,b){return u=Oo(u),d=Oo(d),b=Oo(b),.2126*u+.7152*d+.0722*b},Oo=function(u){return u/=255,u<=.03928?u/12.92:Wg((u+.055)/1.055,2.4)},we={},bc=D,vc=p.type,Xs=we,_c=function(u,d,b){b===void 0&&(b=.5);for(var _=[],P=arguments.length-3;P-- >0;)_[P]=arguments[P+3];var T=_[0]||"lrgb";if(!Xs[T]&&!_.length&&(T=Object.keys(Xs)[0]),!Xs[T])throw new Error("interpolation mode "+T+" is not defined");return vc(u)!=="object"&&(u=new bc(u)),vc(d)!=="object"&&(d=new bc(d)),Xs[T](u,d,b).alpha(u.alpha()+b*(d.alpha()-u.alpha()))},yc=D,qg=_c;yc.prototype.mix=yc.prototype.interpolate=function(u,d){d===void 0&&(d=.5);for(var b=[],_=arguments.length-2;_-- >0;)b[_]=arguments[_+2];return qg.apply(void 0,[this,u,d].concat(b))};var xc=D;xc.prototype.premultiply=function(u){u===void 0&&(u=!1);var d=this._rgb,b=d[3];return u?(this._rgb=[d[0]*b,d[1]*b,d[2]*b,b],this):new xc([d[0]*b,d[1]*b,d[2]*b,b],"rgb")};var Ao=D,Gg=Ns;Ao.prototype.saturate=function(u){u===void 0&&(u=1);var d=this,b=d.lch();return b[1]+=Gg.Kn*u,b[1]<0&&(b[1]=0),new Ao(b,"lch").alpha(d.alpha(),!0)},Ao.prototype.desaturate=function(u){return u===void 0&&(u=1),this.saturate(-u)};var wc=D,kc=p.type;wc.prototype.set=function(u,d,b){b===void 0&&(b=!1);var _=u.split("."),P=_[0],T=_[1],w=this[P]();if(T){var A=P.indexOf(T)-(P.substr(0,2)==="ok"?2:0);if(A>-1){if(kc(d)=="string")switch(d.charAt(0)){case"+":w[A]+=+d;break;case"-":w[A]+=+d;break;case"*":w[A]*=+d.substr(1);break;case"/":w[A]/=+d.substr(1);break;default:w[A]=+d}else if(kc(d)==="number")w[A]=d;else throw new Error("unsupported value for Color.set");var L=new wc(w,P);return b?(this._rgb=L._rgb,this):L}throw new Error("unknown channel "+T+" in mode "+P)}else return w};var Ug=D,Kg=function(u,d,b){var _=u._rgb,P=d._rgb;return new Ug(_[0]+b*(P[0]-_[0]),_[1]+b*(P[1]-_[1]),_[2]+b*(P[2]-_[2]),"rgb")};we.rgb=Kg;var Zg=D,Ro=Math.sqrt,pi=Math.pow,Jg=function(u,d,b){var _=u._rgb,P=_[0],T=_[1],w=_[2],A=d._rgb,L=A[0],j=A[1],B=A[2];return new Zg(Ro(pi(P,2)*(1-b)+pi(L,2)*b),Ro(pi(T,2)*(1-b)+pi(j,2)*b),Ro(pi(w,2)*(1-b)+pi(B,2)*b),"rgb")};we.lrgb=Jg;var Qg=D,tm=function(u,d,b){var _=u.lab(),P=d.lab();return new Qg(_[0]+b*(P[0]-_[0]),_[1]+b*(P[1]-_[1]),_[2]+b*(P[2]-_[2]),"lab")};we.lab=tm;var Sc=D,gi=function(u,d,b,_){var P,T,w,A;_==="hsl"?(w=u.hsl(),A=d.hsl()):_==="hsv"?(w=u.hsv(),A=d.hsv()):_==="hcg"?(w=u.hcg(),A=d.hcg()):_==="hsi"?(w=u.hsi(),A=d.hsi()):_==="lch"||_==="hcl"?(_="hcl",w=u.hcl(),A=d.hcl()):_==="oklch"&&(w=u.oklch().reverse(),A=d.oklch().reverse());var L,j,B,q,H,Q;(_.substr(0,1)==="h"||_==="oklch")&&(P=w,L=P[0],B=P[1],H=P[2],T=A,j=T[0],q=T[1],Q=T[2]);var K,ct,mt,_t;return!isNaN(L)&&!isNaN(j)?(j>L&&j-L>180?_t=j-(L+360):j<L&&L-j>180?_t=j+360-L:_t=j-L,ct=L+b*_t):isNaN(L)?isNaN(j)?ct=Number.NaN:(ct=j,(H==1||H==0)&&_!="hsv"&&(K=q)):(ct=L,(Q==1||Q==0)&&_!="hsv"&&(K=B)),K===void 0&&(K=B+b*(q-B)),mt=H+b*(Q-H),_==="oklch"?new Sc([mt,K,ct],_):new Sc([ct,K,mt],_)},em=gi,Mc=function(u,d,b){return em(u,d,b,"lch")};we.lch=Mc,we.hcl=Mc;var nm=D,im=function(u,d,b){var _=u.num(),P=d.num();return new nm(_+b*(P-_),"num")};we.num=im;var sm=gi,rm=function(u,d,b){return sm(u,d,b,"hcg")};we.hcg=rm;var om=gi,am=function(u,d,b){return om(u,d,b,"hsi")};we.hsi=am;var lm=gi,cm=function(u,d,b){return lm(u,d,b,"hsl")};we.hsl=cm;var hm=gi,um=function(u,d,b){return hm(u,d,b,"hsv")};we.hsv=um;var fm=D,dm=function(u,d,b){var _=u.oklab(),P=d.oklab();return new fm(_[0]+b*(P[0]-_[0]),_[1]+b*(P[1]-_[1]),_[2]+b*(P[2]-_[2]),"oklab")};we.oklab=dm;var pm=gi,gm=function(u,d,b){return pm(u,d,b,"oklch")};we.oklch=gm;var Lo=D,mm=p.clip_rgb,Fo=Math.pow,Io=Math.sqrt,$o=Math.PI,Cc=Math.cos,Pc=Math.sin,bm=Math.atan2,vm=function(u,d,b){d===void 0&&(d="lrgb"),b===void 0&&(b=null);var _=u.length;b||(b=Array.from(new Array(_)).map(function(){return 1}));var P=_/b.reduce(function(ct,mt){return ct+mt});if(b.forEach(function(ct,mt){b[mt]*=P}),u=u.map(function(ct){return new Lo(ct)}),d==="lrgb")return _m(u,b);for(var T=u.shift(),w=T.get(d),A=[],L=0,j=0,B=0;B<w.length;B++)if(w[B]=(w[B]||0)*b[0],A.push(isNaN(w[B])?0:b[0]),d.charAt(B)==="h"&&!isNaN(w[B])){var q=w[B]/180*$o;L+=Cc(q)*b[0],j+=Pc(q)*b[0]}var H=T.alpha()*b[0];u.forEach(function(ct,mt){var _t=ct.get(d);H+=ct.alpha()*b[mt+1];for(var Mt=0;Mt<w.length;Mt++)if(!isNaN(_t[Mt]))if(A[Mt]+=b[mt+1],d.charAt(Mt)==="h"){var oe=_t[Mt]/180*$o;L+=Cc(oe)*b[mt+1],j+=Pc(oe)*b[mt+1]}else w[Mt]+=_t[Mt]*b[mt+1]});for(var Q=0;Q<w.length;Q++)if(d.charAt(Q)==="h"){for(var K=bm(j/A[Q],L/A[Q])/$o*180;K<0;)K+=360;for(;K>=360;)K-=360;w[Q]=K}else w[Q]=w[Q]/A[Q];return H/=_,new Lo(w,d).alpha(H>.99999?1:H,!0)},_m=function(u,d){for(var b=u.length,_=[0,0,0,0],P=0;P<u.length;P++){var T=u[P],w=d[P]/b,A=T._rgb;_[0]+=Fo(A[0],2)*w,_[1]+=Fo(A[1],2)*w,_[2]+=Fo(A[2],2)*w,_[3]+=A[3]*w}return _[0]=Io(_[0]),_[1]=Io(_[1]),_[2]=Io(_[2]),_[3]>.9999999&&(_[3]=1),new Lo(mm(_))},Pe=$,mi=p.type,ym=Math.pow,jo=function(u){var d="rgb",b=Pe("#ccc"),_=0,P=[0,1],T=[],w=[0,0],A=!1,L=[],j=!1,B=0,q=1,H=!1,Q={},K=!0,ct=1,mt=function(W){if(W=W||["#fff","#000"],W&&mi(W)==="string"&&Pe.brewer&&Pe.brewer[W.toLowerCase()]&&(W=Pe.brewer[W.toLowerCase()]),mi(W)==="array"){W.length===1&&(W=[W[0],W[0]]),W=W.slice(0);for(var st=0;st<W.length;st++)W[st]=Pe(W[st]);T.length=0;for(var dt=0;dt<W.length;dt++)T.push(dt/(W.length-1))}return ge(),L=W},_t=function(W){if(A!=null){for(var st=A.length-1,dt=0;dt<st&&W>=A[dt];)dt++;return dt-1}return 0},Mt=function(W){return W},oe=function(W){return W},ne=function(W,st){var dt,ht;if(st==null&&(st=!1),isNaN(W)||W===null)return b;if(st)ht=W;else if(A&&A.length>2){var ae=_t(W);ht=ae/(A.length-2)}else q!==B?ht=(W-B)/(q-B):ht=1;ht=oe(ht),st||(ht=Mt(ht)),ct!==1&&(ht=ym(ht,ct)),ht=w[0]+ht*(1-w[0]-w[1]),ht=Math.min(1,Math.max(0,ht));var $t=Math.floor(ht*1e4);if(K&&Q[$t])dt=Q[$t];else{if(mi(L)==="array")for(var yt=0;yt<T.length;yt++){var Tt=T[yt];if(ht<=Tt){dt=L[yt];break}if(ht>=Tt&&yt===T.length-1){dt=L[yt];break}if(ht>Tt&&ht<T[yt+1]){ht=(ht-Tt)/(T[yt+1]-Tt),dt=Pe.interpolate(L[yt],L[yt+1],ht,d);break}}else mi(L)==="function"&&(dt=L(ht));K&&(Q[$t]=dt)}return dt},ge=function(){return Q={}};mt(u);var vt=function(W){var st=Pe(ne(W));return j&&st[j]?st[j]():st};return vt.classes=function(W){if(W!=null){if(mi(W)==="array")A=W,P=[W[0],W[W.length-1]];else{var st=Pe.analyze(P);W===0?A=[st.min,st.max]:A=Pe.limits(st,"e",W)}return vt}return A},vt.domain=function(W){if(!arguments.length)return P;B=W[0],q=W[W.length-1],T=[];var st=L.length;if(W.length===st&&B!==q)for(var dt=0,ht=Array.from(W);dt<ht.length;dt+=1){var ae=ht[dt];T.push((ae-B)/(q-B))}else{for(var $t=0;$t<st;$t++)T.push($t/(st-1));if(W.length>2){var yt=W.map(function(Dt,At){return At/(W.length-1)}),Tt=W.map(function(Dt){return(Dt-B)/(q-B)});Tt.every(function(Dt,At){return yt[At]===Dt})||(oe=function(Dt){if(Dt<=0||Dt>=1)return Dt;for(var At=0;Dt>=Tt[At+1];)At++;var De=(Dt-Tt[At])/(Tt[At+1]-Tt[At]),mn=yt[At]+De*(yt[At+1]-yt[At]);return mn})}}return P=[B,q],vt},vt.mode=function(W){return arguments.length?(d=W,ge(),vt):d},vt.range=function(W,st){return mt(W),vt},vt.out=function(W){return j=W,vt},vt.spread=function(W){return arguments.length?(_=W,vt):_},vt.correctLightness=function(W){return W==null&&(W=!0),H=W,ge(),H?Mt=function(st){for(var dt=ne(0,!0).lab()[0],ht=ne(1,!0).lab()[0],ae=dt>ht,$t=ne(st,!0).lab()[0],yt=dt+(ht-dt)*st,Tt=$t-yt,Dt=0,At=1,De=20;Math.abs(Tt)>.01&&De-- >0;)(function(){return ae&&(Tt*=-1),Tt<0?(Dt=st,st+=(At-st)*.5):(At=st,st+=(Dt-st)*.5),$t=ne(st,!0).lab()[0],Tt=$t-yt})();return st}:Mt=function(st){return st},vt},vt.padding=function(W){return W!=null?(mi(W)==="number"&&(W=[W,W]),w=W,vt):w},vt.colors=function(W,st){arguments.length<2&&(st="hex");var dt=[];if(arguments.length===0)dt=L.slice(0);else if(W===1)dt=[vt(.5)];else if(W>1){var ht=P[0],ae=P[1]-ht;dt=xm(0,W,!1).map(function(At){return vt(ht+At/(W-1)*ae)})}else{u=[];var $t=[];if(A&&A.length>2)for(var yt=1,Tt=A.length,Dt=1<=Tt;Dt?yt<Tt:yt>Tt;Dt?yt++:yt--)$t.push((A[yt-1]+A[yt])*.5);else $t=P;dt=$t.map(function(At){return vt(At)})}return Pe[st]&&(dt=dt.map(function(At){return At[st]()})),dt},vt.cache=function(W){return W!=null?(K=W,vt):K},vt.gamma=function(W){return W!=null?(ct=W,vt):ct},vt.nodata=function(W){return W!=null?(b=Pe(W),vt):b},vt};function xm(u,d,b){for(var _=[],P=u<d,T=b?P?d+1:d-1:d,w=u;P?w<T:w>T;P?w++:w--)_.push(w);return _}var Bi=D,wm=jo,km=function(u){for(var d=[1,1],b=1;b<u;b++){for(var _=[1],P=1;P<=d.length;P++)_[P]=(d[P]||0)+d[P-1];d=_}return d},Sm=function(u){var d,b,_,P,T,w,A;if(u=u.map(function(H){return new Bi(H)}),u.length===2)d=u.map(function(H){return H.lab()}),T=d[0],w=d[1],P=function(H){var Q=[0,1,2].map(function(K){return T[K]+H*(w[K]-T[K])});return new Bi(Q,"lab")};else if(u.length===3)b=u.map(function(H){return H.lab()}),T=b[0],w=b[1],A=b[2],P=function(H){var Q=[0,1,2].map(function(K){return(1-H)*(1-H)*T[K]+2*(1-H)*H*w[K]+H*H*A[K]});return new Bi(Q,"lab")};else if(u.length===4){var L;_=u.map(function(H){return H.lab()}),T=_[0],w=_[1],A=_[2],L=_[3],P=function(H){var Q=[0,1,2].map(function(K){return(1-H)*(1-H)*(1-H)*T[K]+3*(1-H)*(1-H)*H*w[K]+3*(1-H)*H*H*A[K]+H*H*H*L[K]});return new Bi(Q,"lab")}}else if(u.length>=5){var j,B,q;j=u.map(function(H){return H.lab()}),q=u.length-1,B=km(q),P=function(H){var Q=1-H,K=[0,1,2].map(function(ct){return j.reduce(function(mt,_t,Mt){return mt+B[Mt]*Math.pow(Q,q-Mt)*Math.pow(H,Mt)*_t[ct]},0)});return new Bi(K,"lab")}}else throw new RangeError("No point in running bezier with only one color.");return P},Mm=function(u){var d=Sm(u);return d.scale=function(){return wm(d)},d},zo=$,Te=function(u,d,b){if(!Te[b])throw new Error("unknown blend mode "+b);return Te[b](u,d)},pn=function(u){return function(d,b){var _=zo(b).rgb(),P=zo(d).rgb();return zo.rgb(u(_,P))}},gn=function(u){return function(d,b){var _=[];return _[0]=u(d[0],b[0]),_[1]=u(d[1],b[1]),_[2]=u(d[2],b[2]),_}},Cm=function(u){return u},Pm=function(u,d){return u*d/255},Tm=function(u,d){return u>d?d:u},Dm=function(u,d){return u>d?u:d},Em=function(u,d){return 255*(1-(1-u/255)*(1-d/255))},Om=function(u,d){return d<128?2*u*d/255:255*(1-2*(1-u/255)*(1-d/255))},Am=function(u,d){return 255*(1-(1-d/255)/(u/255))},Rm=function(u,d){return u===255?255:(u=255*(d/255)/(1-u/255),u>255?255:u)};Te.normal=pn(gn(Cm)),Te.multiply=pn(gn(Pm)),Te.screen=pn(gn(Em)),Te.overlay=pn(gn(Om)),Te.darken=pn(gn(Tm)),Te.lighten=pn(gn(Dm)),Te.dodge=pn(gn(Rm)),Te.burn=pn(gn(Am));for(var Lm=Te,Bo=p.type,Fm=p.clip_rgb,Im=p.TWOPI,$m=Math.pow,jm=Math.sin,zm=Math.cos,Tc=$,Bm=function(u,d,b,_,P){u===void 0&&(u=300),d===void 0&&(d=-1.5),b===void 0&&(b=1),_===void 0&&(_=1),P===void 0&&(P=[0,1]);var T=0,w;Bo(P)==="array"?w=P[1]-P[0]:(w=0,P=[P,P]);var A=function(L){var j=Im*((u+120)/360+d*L),B=$m(P[0]+w*L,_),q=T!==0?b[0]+L*T:b,H=q*B*(1-B)/2,Q=zm(j),K=jm(j),ct=B+H*(-.14861*Q+1.78277*K),mt=B+H*(-.29227*Q-.90649*K),_t=B+H*(1.97294*Q);return Tc(Fm([ct*255,mt*255,_t*255,1]))};return A.start=function(L){return L==null?u:(u=L,A)},A.rotations=function(L){return L==null?d:(d=L,A)},A.gamma=function(L){return L==null?_:(_=L,A)},A.hue=function(L){return L==null?b:(b=L,Bo(b)==="array"?(T=b[1]-b[0],T===0&&(b=b[1])):T=0,A)},A.lightness=function(L){return L==null?P:(Bo(L)==="array"?(P=L,w=L[1]-L[0]):(P=[L,L],w=0),A)},A.scale=function(){return Tc.scale(A)},A.hue(b),A},Nm=D,Hm="0123456789abcdef",Wm=Math.floor,Vm=Math.random,Ym=function(){for(var u="#",d=0;d<6;d++)u+=Hm.charAt(Wm(Vm()*16));return new Nm(u,"hex")},No=a,Dc=Math.log,Xm=Math.pow,qm=Math.floor,Gm=Math.abs,Ec=function(u,d){d===void 0&&(d=null);var b={min:Number.MAX_VALUE,max:Number.MAX_VALUE*-1,sum:0,values:[],count:0};return No(u)==="object"&&(u=Object.values(u)),u.forEach(function(_){d&&No(_)==="object"&&(_=_[d]),_!=null&&!isNaN(_)&&(b.values.push(_),b.sum+=_,_<b.min&&(b.min=_),_>b.max&&(b.max=_),b.count+=1)}),b.domain=[b.min,b.max],b.limits=function(_,P){return Oc(b,_,P)},b},Oc=function(u,d,b){d===void 0&&(d="equal"),b===void 0&&(b=7),No(u)=="array"&&(u=Ec(u));var _=u.min,P=u.max,T=u.values.sort(function(Wo,Vo){return Wo-Vo});if(b===1)return[_,P];var w=[];if(d.substr(0,1)==="c"&&(w.push(_),w.push(P)),d.substr(0,1)==="e"){w.push(_);for(var A=1;A<b;A++)w.push(_+A/b*(P-_));w.push(P)}else if(d.substr(0,1)==="l"){if(_<=0)throw new Error("Logarithmic scales are only possible for values > 0");var L=Math.LOG10E*Dc(_),j=Math.LOG10E*Dc(P);w.push(_);for(var B=1;B<b;B++)w.push(Xm(10,L+B/b*(j-L)));w.push(P)}else if(d.substr(0,1)==="q"){w.push(_);for(var q=1;q<b;q++){var H=(T.length-1)*q/b,Q=qm(H);if(Q===H)w.push(T[Q]);else{var K=H-Q;w.push(T[Q]*(1-K)+T[Q+1]*K)}}w.push(P)}else if(d.substr(0,1)==="k"){var ct,mt=T.length,_t=new Array(mt),Mt=new Array(b),oe=!0,ne=0,ge=null;ge=[],ge.push(_);for(var vt=1;vt<b;vt++)ge.push(_+vt/b*(P-_));for(ge.push(P);oe;){for(var W=0;W<b;W++)Mt[W]=0;for(var st=0;st<mt;st++)for(var dt=T[st],ht=Number.MAX_VALUE,ae=void 0,$t=0;$t<b;$t++){var yt=Gm(ge[$t]-dt);yt<ht&&(ht=yt,ae=$t),Mt[ae]++,_t[st]=ae}for(var Tt=new Array(b),Dt=0;Dt<b;Dt++)Tt[Dt]=null;for(var At=0;At<mt;At++)ct=_t[At],Tt[ct]===null?Tt[ct]=T[At]:Tt[ct]+=T[At];for(var De=0;De<b;De++)Tt[De]*=1/Mt[De];oe=!1;for(var mn=0;mn<b;mn++)if(Tt[mn]!==ge[mn]){oe=!0;break}ge=Tt,ne++,ne>200&&(oe=!1)}for(var bn={},bi=0;bi<b;bi++)bn[bi]=[];for(var vi=0;vi<mt;vi++)ct=_t[vi],bn[ct].push(T[vi]);for(var Ue=[],zn=0;zn<b;zn++)Ue.push(bn[zn][0]),Ue.push(bn[zn][bn[zn].length-1]);Ue=Ue.sort(function(Wo,Vo){return Wo-Vo}),w.push(Ue[0]);for(var Ni=1;Ni<Ue.length;Ni+=2){var Bn=Ue[Ni];!isNaN(Bn)&&w.indexOf(Bn)===-1&&w.push(Bn)}}return w},Ac={analyze:Ec,limits:Oc},Rc=D,Um=function(u,d){u=new Rc(u),d=new Rc(d);var b=u.luminance(),_=d.luminance();return b>_?(b+.05)/(_+.05):(_+.05)/(b+.05)},Lc=D,Ge=Math.sqrt,Kt=Math.pow,Km=Math.min,Zm=Math.max,Fc=Math.atan2,Ic=Math.abs,qs=Math.cos,$c=Math.sin,Jm=Math.exp,jc=Math.PI,Qm=function(u,d,b,_,P){b===void 0&&(b=1),_===void 0&&(_=1),P===void 0&&(P=1);var T=function(Bn){return 360*Bn/(2*jc)},w=function(Bn){return 2*jc*Bn/360};u=new Lc(u),d=new Lc(d);var A=Array.from(u.lab()),L=A[0],j=A[1],B=A[2],q=Array.from(d.lab()),H=q[0],Q=q[1],K=q[2],ct=(L+H)/2,mt=Ge(Kt(j,2)+Kt(B,2)),_t=Ge(Kt(Q,2)+Kt(K,2)),Mt=(mt+_t)/2,oe=.5*(1-Ge(Kt(Mt,7)/(Kt(Mt,7)+Kt(25,7)))),ne=j*(1+oe),ge=Q*(1+oe),vt=Ge(Kt(ne,2)+Kt(B,2)),W=Ge(Kt(ge,2)+Kt(K,2)),st=(vt+W)/2,dt=T(Fc(B,ne)),ht=T(Fc(K,ge)),ae=dt>=0?dt:dt+360,$t=ht>=0?ht:ht+360,yt=Ic(ae-$t)>180?(ae+$t+360)/2:(ae+$t)/2,Tt=1-.17*qs(w(yt-30))+.24*qs(w(2*yt))+.32*qs(w(3*yt+6))-.2*qs(w(4*yt-63)),Dt=$t-ae;Dt=Ic(Dt)<=180?Dt:$t<=ae?Dt+360:Dt-360,Dt=2*Ge(vt*W)*$c(w(Dt)/2);var At=H-L,De=W-vt,mn=1+.015*Kt(ct-50,2)/Ge(20+Kt(ct-50,2)),bn=1+.045*st,bi=1+.015*st*Tt,vi=30*Jm(-Kt((yt-275)/25,2)),Ue=2*Ge(Kt(st,7)/(Kt(st,7)+Kt(25,7))),zn=-Ue*$c(2*w(vi)),Ni=Ge(Kt(At/(b*mn),2)+Kt(De/(_*bn),2)+Kt(Dt/(P*bi),2)+zn*(De/(_*bn))*(Dt/(P*bi)));return Zm(0,Km(100,Ni))},zc=D,tb=function(u,d,b){b===void 0&&(b="lab"),u=new zc(u),d=new zc(d);var _=u.get(b),P=d.get(b),T=0;for(var w in _){var A=(_[w]||0)-(P[w]||0);T+=A*A}return Math.sqrt(T)},eb=D,nb=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];try{return new(Function.prototype.bind.apply(eb,[null].concat(u))),!0}catch(b){return!1}},Bc=$,Nc=jo,ib={cool:function(){return Nc([Bc.hsl(180,1,.9),Bc.hsl(250,.7,.4)])},hot:function(){return Nc(["#000","#f00","#ff0","#fff"]).mode("rgb")}},Gs={OrRd:["#fff7ec","#fee8c8","#fdd49e","#fdbb84","#fc8d59","#ef6548","#d7301f","#b30000","#7f0000"],PuBu:["#fff7fb","#ece7f2","#d0d1e6","#a6bddb","#74a9cf","#3690c0","#0570b0","#045a8d","#023858"],BuPu:["#f7fcfd","#e0ecf4","#bfd3e6","#9ebcda","#8c96c6","#8c6bb1","#88419d","#810f7c","#4d004b"],Oranges:["#fff5eb","#fee6ce","#fdd0a2","#fdae6b","#fd8d3c","#f16913","#d94801","#a63603","#7f2704"],BuGn:["#f7fcfd","#e5f5f9","#ccece6","#99d8c9","#66c2a4","#41ae76","#238b45","#006d2c","#00441b"],YlOrBr:["#ffffe5","#fff7bc","#fee391","#fec44f","#fe9929","#ec7014","#cc4c02","#993404","#662506"],YlGn:["#ffffe5","#f7fcb9","#d9f0a3","#addd8e","#78c679","#41ab5d","#238443","#006837","#004529"],Reds:["#fff5f0","#fee0d2","#fcbba1","#fc9272","#fb6a4a","#ef3b2c","#cb181d","#a50f15","#67000d"],RdPu:["#fff7f3","#fde0dd","#fcc5c0","#fa9fb5","#f768a1","#dd3497","#ae017e","#7a0177","#49006a"],Greens:["#f7fcf5","#e5f5e0","#c7e9c0","#a1d99b","#74c476","#41ab5d","#238b45","#006d2c","#00441b"],YlGnBu:["#ffffd9","#edf8b1","#c7e9b4","#7fcdbb","#41b6c4","#1d91c0","#225ea8","#253494","#081d58"],Purples:["#fcfbfd","#efedf5","#dadaeb","#bcbddc","#9e9ac8","#807dba","#6a51a3","#54278f","#3f007d"],GnBu:["#f7fcf0","#e0f3db","#ccebc5","#a8ddb5","#7bccc4","#4eb3d3","#2b8cbe","#0868ac","#084081"],Greys:["#ffffff","#f0f0f0","#d9d9d9","#bdbdbd","#969696","#737373","#525252","#252525","#000000"],YlOrRd:["#ffffcc","#ffeda0","#fed976","#feb24c","#fd8d3c","#fc4e2a","#e31a1c","#bd0026","#800026"],PuRd:["#f7f4f9","#e7e1ef","#d4b9da","#c994c7","#df65b0","#e7298a","#ce1256","#980043","#67001f"],Blues:["#f7fbff","#deebf7","#c6dbef","#9ecae1","#6baed6","#4292c6","#2171b5","#08519c","#08306b"],PuBuGn:["#fff7fb","#ece2f0","#d0d1e6","#a6bddb","#67a9cf","#3690c0","#02818a","#016c59","#014636"],Viridis:["#440154","#482777","#3f4a8a","#31678e","#26838f","#1f9d8a","#6cce5a","#b6de2b","#fee825"],Spectral:["#9e0142","#d53e4f","#f46d43","#fdae61","#fee08b","#ffffbf","#e6f598","#abdda4","#66c2a5","#3288bd","#5e4fa2"],RdYlGn:["#a50026","#d73027","#f46d43","#fdae61","#fee08b","#ffffbf","#d9ef8b","#a6d96a","#66bd63","#1a9850","#006837"],RdBu:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#f7f7f7","#d1e5f0","#92c5de","#4393c3","#2166ac","#053061"],PiYG:["#8e0152","#c51b7d","#de77ae","#f1b6da","#fde0ef","#f7f7f7","#e6f5d0","#b8e186","#7fbc41","#4d9221","#276419"],PRGn:["#40004b","#762a83","#9970ab","#c2a5cf","#e7d4e8","#f7f7f7","#d9f0d3","#a6dba0","#5aae61","#1b7837","#00441b"],RdYlBu:["#a50026","#d73027","#f46d43","#fdae61","#fee090","#ffffbf","#e0f3f8","#abd9e9","#74add1","#4575b4","#313695"],BrBG:["#543005","#8c510a","#bf812d","#dfc27d","#f6e8c3","#f5f5f5","#c7eae5","#80cdc1","#35978f","#01665e","#003c30"],RdGy:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#ffffff","#e0e0e0","#bababa","#878787","#4d4d4d","#1a1a1a"],PuOr:["#7f3b08","#b35806","#e08214","#fdb863","#fee0b6","#f7f7f7","#d8daeb","#b2abd2","#8073ac","#542788","#2d004b"],Set2:["#66c2a5","#fc8d62","#8da0cb","#e78ac3","#a6d854","#ffd92f","#e5c494","#b3b3b3"],Accent:["#7fc97f","#beaed4","#fdc086","#ffff99","#386cb0","#f0027f","#bf5b17","#666666"],Set1:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999"],Set3:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd","#ccebc5","#ffed6f"],Dark2:["#1b9e77","#d95f02","#7570b3","#e7298a","#66a61e","#e6ab02","#a6761d","#666666"],Paired:["#a6cee3","#1f78b4","#b2df8a","#33a02c","#fb9a99","#e31a1c","#fdbf6f","#ff7f00","#cab2d6","#6a3d9a","#ffff99","#b15928"],Pastel2:["#b3e2cd","#fdcdac","#cbd5e8","#f4cae4","#e6f5c9","#fff2ae","#f1e2cc","#cccccc"],Pastel1:["#fbb4ae","#b3cde3","#ccebc5","#decbe4","#fed9a6","#ffffcc","#e5d8bd","#fddaec","#f2f2f2"]},Ho=0,Hc=Object.keys(Gs);Ho<Hc.length;Ho+=1){var Wc=Hc[Ho];Gs[Wc.toLowerCase()]=Gs[Wc]}var sb=Gs,ee=$;ee.average=vm,ee.bezier=Mm,ee.blend=Lm,ee.cubehelix=Bm,ee.mix=ee.interpolate=_c,ee.random=Ym,ee.scale=jo,ee.analyze=Ac.analyze,ee.contrast=Um,ee.deltaE=Qm,ee.distance=tb,ee.limits=Ac.limits,ee.valid=nb,ee.scales=ib,ee.colors=ec,ee.brewer=sb;var rb=ee;return rb})});var tf=Xo((qr,Qu)=>{(function(n,t){typeof qr=="object"&&typeof Qu!="undefined"?t(qr):typeof define=="function"&&define.amd?define("@ts-stack/markdown",["exports"],t):(n=typeof globalThis!="undefined"?globalThis:n||self,t((n["ts-stack"]=n["ts-stack"]||{},n["ts-stack"].markdown={})))})(qr,function(n){"use strict";var t=function(){function k(v,x){x===void 0&&(x=""),this.source=v.source,this.flags=x}return k.prototype.setGroup=function(v,x){var R=typeof x=="string"?x:x.source;return R=R.replace(/(^|[^\[])\^/g,"$1"),this.source=this.source.replace(v,R),this},k.prototype.getRegexp=function(){return new RegExp(this.source,this.flags)},k}();var e=/[&<>"']/,i=/[&<>"']/g,s={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},r=/[<>"']|&(?!#?\w+;)/,o=/[<>"']|&(?!#?\w+;)/g;function a(k,v){if(v){if(e.test(k))return k.replace(i,function(x){return s[x]})}else if(r.test(k))return k.replace(o,function(x){return s[x]});return k}function l(k){return k.replace(/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi,function(v,x){return x=x.toLowerCase(),x==="colon"?":":x.charAt(0)==="#"?x.charAt(1)==="x"?String.fromCharCode(parseInt(x.substring(2),16)):String.fromCharCode(+x.substring(1)):""})}n.TokenType=void 0,function(k){k[k.space=1]="space",k[k.text=2]="text",k[k.paragraph=3]="paragraph",k[k.heading=4]="heading",k[k.listStart=5]="listStart",k[k.listEnd=6]="listEnd",k[k.looseItemStart=7]="looseItemStart",k[k.looseItemEnd=8]="looseItemEnd",k[k.listItemStart=9]="listItemStart",k[k.listItemEnd=10]="listItemEnd",k[k.blockquoteStart=11]="blockquoteStart",k[k.blockquoteEnd=12]="blockquoteEnd",k[k.code=13]="code",k[k.table=14]="table",k[k.html=15]="html",k[k.hr=16]="hr"}(n.TokenType||(n.TokenType={}));var c=function(){function k(){this.gfm=!0,this.tables=!0,this.breaks=!1,this.pedantic=!1,this.sanitize=!1,this.mangle=!0,this.smartLists=!1,this.silent=!1,this.langPrefix="lang-",this.smartypants=!1,this.headerPrefix="",this.xhtml=!1,this.escape=a,this.unescape=l}return k}();var h=function(k,v){return h=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(x,R){x.__proto__=R}||function(x,R){for(var E in R)Object.prototype.hasOwnProperty.call(R,E)&&(x[E]=R[E])},h(k,v)};function f(k,v){if(typeof v!="function"&&v!==null)throw new TypeError("Class extends value "+String(v)+" is not a constructor or null");h(k,v);function x(){this.constructor=k}k.prototype=v===null?Object.create(v):(x.prototype=v.prototype,new x)}var g=function(){return g=Object.assign||function(v){for(var x,R=1,E=arguments.length;R<E;R++){x=arguments[R];for(var O in x)Object.prototype.hasOwnProperty.call(x,O)&&(v[O]=x[O])}return v},g.apply(this,arguments)};function p(k,v){var x={};for(var R in k)Object.prototype.hasOwnProperty.call(k,R)&&v.indexOf(R)<0&&(x[R]=k[R]);if(k!=null&&typeof Object.getOwnPropertySymbols=="function")for(var E=0,R=Object.getOwnPropertySymbols(k);E<R.length;E++)v.indexOf(R[E])<0&&Object.prototype.propertyIsEnumerable.call(k,R[E])&&(x[R[E]]=k[R[E]]);return x}function m(k,v,x,R){var E=arguments.length,O=E<3?v:R===null?R=Object.getOwnPropertyDescriptor(v,x):R,Z;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")O=Reflect.decorate(k,v,x,R);else for(var V=k.length-1;V>=0;V--)(Z=k[V])&&(O=(E<3?Z(O):E>3?Z(v,x,O):Z(v,x))||O);return E>3&&O&&Object.defineProperty(v,x,O),O}function y(k,v){return function(x,R){v(x,R,k)}}function S(k,v){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(k,v)}function M(k,v,x,R){function E(O){return O instanceof x?O:new x(function(Z){Z(O)})}return new(x||(x=Promise))(function(O,Z){function V(xt){try{tt(R.next(xt))}catch(Ht){Z(Ht)}}function J(xt){try{tt(R.throw(xt))}catch(Ht){Z(Ht)}}function tt(xt){xt.done?O(xt.value):E(xt.value).then(V,J)}tt((R=R.apply(k,v||[])).next())})}function C(k,v){var x={label:0,sent:function(){if(O[0]&1)throw O[1];return O[1]},trys:[],ops:[]},R,E,O,Z;return Z={next:V(0),throw:V(1),return:V(2)},typeof Symbol=="function"&&(Z[Symbol.iterator]=function(){return this}),Z;function V(tt){return function(xt){return J([tt,xt])}}function J(tt){if(R)throw new TypeError("Generator is already executing.");for(;x;)try{if(R=1,E&&(O=tt[0]&2?E.return:tt[0]?E.throw||((O=E.return)&&O.call(E),0):E.next)&&!(O=O.call(E,tt[1])).done)return O;switch(E=0,O&&(tt=[tt[0]&2,O.value]),tt[0]){case 0:case 1:O=tt;break;case 4:return x.label++,{value:tt[1],done:!1};case 5:x.label++,E=tt[1],tt=[0];continue;case 7:tt=x.ops.pop(),x.trys.pop();continue;default:if(O=x.trys,!(O=O.length>0&&O[O.length-1])&&(tt[0]===6||tt[0]===2)){x=0;continue}if(tt[0]===3&&(!O||tt[1]>O[0]&&tt[1]<O[3])){x.label=tt[1];break}if(tt[0]===6&&x.label<O[1]){x.label=O[1],O=tt;break}if(O&&x.label<O[2]){x.label=O[2],x.ops.push(tt);break}O[2]&&x.ops.pop(),x.trys.pop();continue}tt=v.call(k,x)}catch(xt){tt=[6,xt],E=0}finally{R=O=0}if(tt[0]&5)throw tt[1];return{value:tt[0]?tt[1]:void 0,done:!0}}}var F=Object.create?function(k,v,x,R){R===void 0&&(R=x),Object.defineProperty(k,R,{enumerable:!0,get:function(){return v[x]}})}:function(k,v,x,R){R===void 0&&(R=x),k[R]=v[x]};function D(k,v){for(var x in k)x!=="default"&&!Object.prototype.hasOwnProperty.call(v,x)&&F(v,k,x)}function I(k){var v=typeof Symbol=="function"&&Symbol.iterator,x=v&&k[v],R=0;if(x)return x.call(k);if(k&&typeof k.length=="number")return{next:function(){return k&&R>=k.length&&(k=void 0),{value:k&&k[R++],done:!k}}};throw new TypeError(v?"Object is not iterable.":"Symbol.iterator is not defined.")}function $(k,v){var x=typeof Symbol=="function"&&k[Symbol.iterator];if(!x)return k;var R=x.call(k),E,O=[],Z;try{for(;(v===void 0||v-- >0)&&!(E=R.next()).done;)O.push(E.value)}catch(V){Z={error:V}}finally{try{E&&!E.done&&(x=R.return)&&x.call(R)}finally{if(Z)throw Z.error}}return O}function N(){for(var k=[],v=0;v<arguments.length;v++)k=k.concat($(arguments[v]));return k}function G(){for(var k=0,v=0,x=arguments.length;v<x;v++)k+=arguments[v].length;for(var R=Array(k),E=0,v=0;v<x;v++)for(var O=arguments[v],Z=0,V=O.length;Z<V;Z++,E++)R[E]=O[Z];return R}function U(k,v){for(var x=0,R=v.length,E=k.length;x<R;x++,E++)k[E]=v[x];return k}function it(k){return this instanceof it?(this.v=k,this):new it(k)}function lt(k,v,x){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var R=x.apply(k,v||[]),E,O=[];return E={},Z("next"),Z("throw"),Z("return"),E[Symbol.asyncIterator]=function(){return this},E;function Z(Ot){R[Ot]&&(E[Ot]=function(Yt){return new Promise(function(Xt,ue){O.push([Ot,Yt,Xt,ue])>1||V(Ot,Yt)})})}function V(Ot,Yt){try{J(R[Ot](Yt))}catch(Xt){Ht(O[0][3],Xt)}}function J(Ot){Ot.value instanceof it?Promise.resolve(Ot.value.v).then(tt,xt):Ht(O[0][2],Ot)}function tt(Ot){V("next",Ot)}function xt(Ot){V("throw",Ot)}function Ht(Ot,Yt){Ot(Yt),O.shift(),O.length&&V(O[0][0],O[0][1])}}function rt(k){var v,x;return v={},R("next"),R("throw",function(E){throw E}),R("return"),v[Symbol.iterator]=function(){return this},v;function R(E,O){v[E]=k[E]?function(Z){return(x=!x)?{value:it(k[E](Z)),done:E==="return"}:O?O(Z):Z}:O}}function Pt(k){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var v=k[Symbol.asyncIterator],x;return v?v.call(k):(k=typeof I=="function"?I(k):k[Symbol.iterator](),x={},R("next"),R("throw"),R("return"),x[Symbol.asyncIterator]=function(){return this},x);function R(O){x[O]=k[O]&&function(Z){return new Promise(function(V,J){Z=k[O](Z),E(V,J,Z.done,Z.value)})}}function E(O,Z,V,J){Promise.resolve(J).then(function(tt){O({value:tt,done:V})},Z)}}function zt(k,v){return Object.defineProperty?Object.defineProperty(k,"raw",{value:v}):k.raw=v,k}var et=Object.create?function(k,v){Object.defineProperty(k,"default",{enumerable:!0,value:v})}:function(k,v){k.default=v};function Et(k){if(k&&k.__esModule)return k;var v={};if(k!=null)for(var x in k)x!=="default"&&Object.prototype.hasOwnProperty.call(k,x)&&F(v,k,x);return et(v,k),v}function St(k){return k&&k.__esModule?k:{default:k}}function re(k,v){if(!v.has(k))throw new TypeError("attempted to get private field on non-instance");return v.get(k)}function ye(k,v,x){if(!v.has(k))throw new TypeError("attempted to set private field on non-instance");return v.set(k,x),x}var ot=function(){function k(v){this.options=v||Ut.options}return k.prototype.code=function(v,x,R,E){if(this.options.highlight){var O=this.options.highlight(v,x);O!=null&&O!==v&&(R=!0,v=O)}var Z=R?v:this.options.escape(v,!0);if(!x)return`
<pre><code>`+Z+`
</code></pre>
`;var V=this.options.langPrefix+this.options.escape(x,!0);return`
<pre><code class="`+V+'">'+Z+`
</code></pre>
`},k.prototype.blockquote=function(v){return`<blockquote>
`+v+`</blockquote>
`},k.prototype.html=function(v){return v},k.prototype.heading=function(v,x,R){var E=this.options.headerPrefix+R.toLowerCase().replace(/[^\w]+/g,"-");return"<h"+x+' id="'+E+'">'+v+"</h"+x+`>
`},k.prototype.hr=function(){return this.options.xhtml?`<hr/>
`:`<hr>
`},k.prototype.list=function(v,x){var R=x?"ol":"ul";return`
<`+R+`>
`+v+"</"+R+`>
`},k.prototype.listitem=function(v){return"<li>"+v+`</li>
`},k.prototype.paragraph=function(v){return"<p>"+v+`</p>
`},k.prototype.table=function(v,x){return`
<table>
<thead>
`+v+`</thead>
<tbody>
`+x+`</tbody>
</table>
`},k.prototype.tablerow=function(v){return`<tr>
`+v+`</tr>
`},k.prototype.tablecell=function(v,x){var R=x.header?"th":"td",E=x.align?"<"+R+' style="text-align:'+x.align+'">':"<"+R+">";return E+v+"</"+R+`>
`},k.prototype.strong=function(v){return"<strong>"+v+"</strong>"},k.prototype.em=function(v){return"<em>"+v+"</em>"},k.prototype.codespan=function(v){return"<code>"+v+"</code>"},k.prototype.br=function(){return this.options.xhtml?"<br/>":"<br>"},k.prototype.del=function(v){return"<del>"+v+"</del>"},k.prototype.link=function(v,x,R){if(this.options.sanitize){var E=void 0;try{E=decodeURIComponent(this.options.unescape(v)).replace(/[^\w:]/g,"").toLowerCase()}catch(Z){return R}if(E.indexOf("javascript:")===0||E.indexOf("vbscript:")===0||E.indexOf("data:")===0)return R}var O='<a href="'+v+'"';return x&&(O+=' title="'+x+'"'),O+=">"+R+"</a>",O},k.prototype.image=function(v,x,R){var E='<img src="'+v+'" alt="'+R+'"';return x&&(E+=' title="'+x+'"'),E+=this.options.xhtml?"/>":">",E},k.prototype.text=function(v){return v},k}();var Lt=function(){function k(v,x,R,E){if(R===void 0&&(R=Ut.options),this.staticThis=v,this.links=x,this.options=R,this.renderer=E||this.options.renderer||new ot(this.options),!this.links)throw new Error("InlineLexer requires 'links' parameter.");this.setRules()}return k.output=function(v,x,R){var E=new this(this,x,R);return E.output(v)},k.getRulesBase=function(){if(this.rulesBase)return this.rulesBase;var v={escape:/^\\([\\`*{}\[\]()#+\-.!_>])/,autolink:/^<([^ <>]+(@|:\/)[^ <>]+)>/,tag:/^<!--[\s\S]*?-->|^<\/?\w+(?:"[^"]*"|'[^']*'|[^<'">])*?>/,link:/^!?\[(inside)\]\(href\)/,reflink:/^!?\[(inside)\]\s*\[([^\]]*)\]/,nolink:/^!?\[((?:\[[^\]]*\]|[^\[\]])*)\]/,strong:/^__([\s\S]+?)__(?!_)|^\*\*([\s\S]+?)\*\*(?!\*)/,em:/^\b_((?:[^_]|__)+?)_\b|^\*((?:\*\*|[\s\S])+?)\*(?!\*)/,code:/^(`+)([\s\S]*?[^`])\1(?!`)/,br:/^ {2,}\n(?!\s*$)/,text:/^[\s\S]+?(?=[\\<!\[_*`]| {2,}\n|$)/,_inside:/(?:\[[^\]]*\]|[^\[\]]|\](?=[^\[]*\]))*/,_href:/\s*<?([\s\S]*?)>?(?:\s+['"]([\s\S]*?)['"])?\s*/};return v.link=new t(v.link).setGroup("inside",v._inside).setGroup("href",v._href).getRegexp(),v.reflink=new t(v.reflink).setGroup("inside",v._inside).getRegexp(),this.rulesBase=v},k.getRulesPedantic=function(){return this.rulesPedantic?this.rulesPedantic:this.rulesPedantic=Object.assign(Object.assign({},this.getRulesBase()),{strong:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,em:/^_(?=\S)([\s\S]*?\S)_(?!_)|^\*(?=\S)([\s\S]*?\S)\*(?!\*)/})},k.getRulesGfm=function(){if(this.rulesGfm)return this.rulesGfm;var v=this.getRulesBase(),x=new t(v.escape).setGroup("])","~|])").getRegexp(),R=new t(v.text).setGroup("]|","~]|").setGroup("|","|https?://|").getRegexp();return this.rulesGfm=Object.assign(Object.assign({},v),{escape:x,url:/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,del:/^~~(?=\S)([\s\S]*?\S)~~/,text:R})},k.getRulesBreaks=function(){if(this.rulesBreaks)return this.rulesBreaks;var v=this.getRulesGfm(),x=this.getRulesGfm();return this.rulesBreaks=Object.assign(Object.assign({},x),{br:new t(v.br).setGroup("{2,}","*").getRegexp(),text:new t(x.text).setGroup("{2,}","*").getRegexp()})},k.prototype.setRules=function(){this.options.gfm?this.options.breaks?this.rules=this.staticThis.getRulesBreaks():this.rules=this.staticThis.getRulesGfm():this.options.pedantic?this.rules=this.staticThis.getRulesPedantic():this.rules=this.staticThis.getRulesBase(),this.hasRulesGfm=this.rules.url!==void 0},k.prototype.output=function(v){v=v;for(var x,R="";v;){if(x=this.rules.escape.exec(v)){v=v.substring(x[0].length),R+=x[1];continue}if(x=this.rules.autolink.exec(v)){var E=void 0,O=void 0;v=v.substring(x[0].length),x[2]==="@"?(E=this.options.escape(x[1].charAt(6)===":"?this.mangle(x[1].substring(7)):this.mangle(x[1])),O=this.mangle("mailto:")+E):(E=this.options.escape(x[1]),O=E),R+=this.renderer.link(O,null,E);continue}if(!this.inLink&&this.hasRulesGfm&&(x=this.rules.url.exec(v))){var E=void 0,O=void 0;v=v.substring(x[0].length),E=this.options.escape(x[1]),O=E,R+=this.renderer.link(O,null,E);continue}if(x=this.rules.tag.exec(v)){!this.inLink&&/^<a /i.test(x[0])?this.inLink=!0:this.inLink&&/^<\/a>/i.test(x[0])&&(this.inLink=!1),v=v.substring(x[0].length),R+=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(x[0]):this.options.escape(x[0]):x[0];continue}if(x=this.rules.link.exec(v)){v=v.substring(x[0].length),this.inLink=!0,R+=this.outputLink(x,{href:x[2],title:x[3]}),this.inLink=!1;continue}if((x=this.rules.reflink.exec(v))||(x=this.rules.nolink.exec(v))){v=v.substring(x[0].length);var Z=(x[2]||x[1]).replace(/\s+/g," "),V=this.links[Z.toLowerCase()];if(!V||!V.href){R+=x[0].charAt(0),v=x[0].substring(1)+v;continue}this.inLink=!0,R+=this.outputLink(x,V),this.inLink=!1;continue}if(x=this.rules.strong.exec(v)){v=v.substring(x[0].length),R+=this.renderer.strong(this.output(x[2]||x[1]));continue}if(x=this.rules.em.exec(v)){v=v.substring(x[0].length),R+=this.renderer.em(this.output(x[2]||x[1]));continue}if(x=this.rules.code.exec(v)){v=v.substring(x[0].length),R+=this.renderer.codespan(this.options.escape(x[2].trim(),!0));continue}if(x=this.rules.br.exec(v)){v=v.substring(x[0].length),R+=this.renderer.br();continue}if(this.hasRulesGfm&&(x=this.rules.del.exec(v))){v=v.substring(x[0].length),R+=this.renderer.del(this.output(x[1]));continue}if(x=this.rules.text.exec(v)){v=v.substring(x[0].length),R+=this.renderer.text(this.options.escape(this.smartypants(x[0])));continue}if(v)throw new Error("Infinite loop on byte: "+v.charCodeAt(0))}return R},k.prototype.outputLink=function(v,x){var R=this.options.escape(x.href),E=x.title?this.options.escape(x.title):null;return v[0].charAt(0)!=="!"?this.renderer.link(R,E,this.output(v[1])):this.renderer.image(R,E,this.options.escape(v[1]))},k.prototype.smartypants=function(v){return this.options.smartypants?v.replace(/---/g,"\u2014").replace(/--/g,"\u2013").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1\u2018").replace(/'/g,"\u2019").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1\u201C").replace(/"/g,"\u201D").replace(/\.{3}/g,"\u2026"):v},k.prototype.mangle=function(v){if(!this.options.mangle)return v;for(var x="",R=v.length,E=0;E<R;E++){var O=void 0;Math.random()>.5&&(O="x"+v.charCodeAt(E).toString(16)),x+="&#"+O+";"}return x},k}();Lt.rulesBase=null,Lt.rulesPedantic=null,Lt.rulesGfm=null,Lt.rulesBreaks=null;var Bt=function(){function k(v){this.simpleRenderers=[],this.line=0,this.tokens=[],this.token=null,this.options=v||Ut.options,this.renderer=this.options.renderer||new ot(this.options)}return k.parse=function(v,x,R){var E=new this(R);return E.parse(x,v)},k.prototype.parse=function(v,x){this.inlineLexer=new Lt(Lt,v,this.options,this.renderer),this.tokens=x.reverse();for(var R="";this.next();)R+=this.tok();return R},k.prototype.debug=function(v,x){this.inlineLexer=new Lt(Lt,v,this.options,this.renderer),this.tokens=x.reverse();for(var R="";this.next();){var E=this.tok();this.token.line=this.line+=E.split(`
`).length-1,R+=E}return R},k.prototype.next=function(){return this.token=this.tokens.pop()},k.prototype.getNextElement=function(){return this.tokens[this.tokens.length-1]},k.prototype.parseText=function(){for(var v=this.token.text,x;(x=this.getNextElement())&&x.type==n.TokenType.text;)v+=`
`+this.next().text;return this.inlineLexer.output(v)},k.prototype.tok=function(){var v,x;switch(this.token.type){case n.TokenType.space:return"";case n.TokenType.paragraph:return this.renderer.paragraph(this.inlineLexer.output(this.token.text));case n.TokenType.text:return this.options.isNoP?this.parseText():this.renderer.paragraph(this.parseText());case n.TokenType.heading:return this.renderer.heading(this.inlineLexer.output(this.token.text),this.token.depth,this.token.text);case n.TokenType.listStart:{for(var R="",E=this.token.ordered;this.next().type!=n.TokenType.listEnd;)R+=this.tok();return this.renderer.list(R,E)}case n.TokenType.listItemStart:{for(var R="";this.next().type!=n.TokenType.listItemEnd;)R+=this.token.type==n.TokenType.text?this.parseText():this.tok();return this.renderer.listitem(R)}case n.TokenType.looseItemStart:{for(var R="";this.next().type!=n.TokenType.listItemEnd;)R+=this.tok();return this.renderer.listitem(R)}case n.TokenType.code:return this.renderer.code(this.token.text,this.token.lang,this.token.escaped,this.token.meta);case n.TokenType.table:{var O="",R="",Z=void 0;Z="";for(var V=0;V<this.token.header.length;V++){var J={header:!0,align:this.token.align[V]},tt=this.inlineLexer.output(this.token.header[V]);Z+=this.renderer.tablecell(tt,J)}O+=this.renderer.tablerow(Z);try{for(var xt=I(this.token.cells),Ht=xt.next();!Ht.done;Ht=xt.next()){var Ot=Ht.value;Z="";for(var Yt=0;Yt<Ot.length;Yt++)Z+=this.renderer.tablecell(this.inlineLexer.output(Ot[Yt]),{header:!1,align:this.token.align[Yt]});R+=this.renderer.tablerow(Z)}}catch(xe){v={error:xe}}finally{try{Ht&&!Ht.done&&(x=xt.return)&&x.call(xt)}finally{if(v)throw v.error}}return this.renderer.table(O,R)}case n.TokenType.blockquoteStart:{for(var R="";this.next().type!=n.TokenType.blockquoteEnd;)R+=this.tok();return this.renderer.blockquote(R)}case n.TokenType.hr:return this.renderer.hr();case n.TokenType.html:{var Xt=!this.token.pre&&!this.options.pedantic?this.inlineLexer.output(this.token.text):this.token.text;return this.renderer.html(Xt)}default:{if(this.simpleRenderers.length){for(var V=0;V<this.simpleRenderers.length;V++)if(this.token.type=="simpleRule"+(V+1))return this.simpleRenderers[V].call(this.renderer,this.token.execArr)}var ue='Token with "'+this.token.type+'" type was not found.';if(this.options.silent)console.log(ue);else throw new Error(ue)}}},k}();var Ut=function(){function k(){}return k.setOptions=function(v){return Object.assign(this.options,v),this},k.setBlockRule=function(v,x){return x===void 0&&(x=function(){return""}),Qt.simpleRules.push(v),this.simpleRenderers.push(x),this},k.parse=function(v,x){x===void 0&&(x=this.options);try{var R=this.callBlockLexer(v,x),E=R.tokens,O=R.links;return this.callParser(E,O,x)}catch(Z){return this.callMe(Z)}},k.debug=function(v,x){x===void 0&&(x=this.options);var R=this.callBlockLexer(v,x),E=R.tokens,O=R.links,Z=E.slice(),V=new Bt(x);V.simpleRenderers=this.simpleRenderers;var J=V.debug(O,E);return Z=Z.map(function(tt){tt.type=n.TokenType[tt.type]||tt.type;var xt=tt.line;return delete tt.line,xt?Object.assign({line:xt},tt):tt}),{tokens:Z,links:O,result:J}},k.callBlockLexer=function(v,x){if(v===void 0&&(v=""),typeof v!="string")throw new Error("Expected that the 'src' parameter would have a 'string' type, got '"+typeof v+"'");return v=v.replace(/\r\n|\r/g,`
`).replace(/\t/g,"    ").replace(/\u00a0/g," ").replace(/\u2424/g,`
`).replace(/^ +$/gm,""),Qt.lex(v,x,!0)},k.callParser=function(v,x,R){if(this.simpleRenderers.length){var E=new Bt(R);return E.simpleRenderers=this.simpleRenderers,E.parse(x,v)}else return Bt.parse(v,x,R)},k.callMe=function(v){if(v.message+=`
Please report this to https://github.com/ts-stack/markdown`,this.options.silent)return"<p>An error occured:</p><pre>"+this.options.escape(v.message+"",!0)+"</pre>";throw v},k}();Ut.options=new c,Ut.simpleRenderers=[];var Qt=function(){function k(v,x){this.staticThis=v,this.links={},this.tokens=[],this.options=x||Ut.options,this.setRules()}return k.lex=function(v,x,R,E){var O=new this(this,x);return O.getTokens(v,R,E)},k.getRulesBase=function(){if(this.rulesBase)return this.rulesBase;var v={newline:/^\n+/,code:/^( {4}[^\n]+\n*)+/,hr:/^( *[-*_]){3,} *(?:\n+|$)/,heading:/^ *(#{1,6}) *([^\n]+?) *#* *(?:\n+|$)/,lheading:/^([^\n]+)\n *(=|-){2,} *(?:\n+|$)/,blockquote:/^( *>[^\n]+(\n[^\n]+)*\n*)+/,list:/^( *)(bull) [\s\S]+?(?:hr|def|\n{2,}(?! )(?!\1bull )\n*|\s*$)/,html:/^ *(?:comment *(?:\n|\s*$)|closed *(?:\n{2,}|\s*$)|closing *(?:\n{2,}|\s*$))/,def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +["(]([^\n]+)[")])? *(?:\n+|$)/,paragraph:/^((?:[^\n]+\n?(?!hr|heading|lheading|blockquote|tag|def))+)\n*/,text:/^[^\n]+/,bullet:/(?:[*+-]|\d+\.)/,item:/^( *)(bull) [^\n]*(?:\n(?!\1bull )[^\n]*)*/};v.item=new t(v.item,"gm").setGroup(/bull/g,v.bullet).getRegexp(),v.list=new t(v.list).setGroup(/bull/g,v.bullet).setGroup("hr","\\n+(?=\\1?(?:[-*_] *){3,}(?:\\n+|$))").setGroup("def","\\n+(?="+v.def.source+")").getRegexp();var x="(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:/|[^\\w\\s@]*@)\\b";return v.html=new t(v.html).setGroup("comment",/<!--[\s\S]*?-->/).setGroup("closed",/<(tag)[\s\S]+?<\/\1>/).setGroup("closing",/<tag(?:"[^"]*"|'[^']*'|[^'">])*?>/).setGroup(/tag/g,x).getRegexp(),v.paragraph=new t(v.paragraph).setGroup("hr",v.hr).setGroup("heading",v.heading).setGroup("lheading",v.lheading).setGroup("blockquote",v.blockquote).setGroup("tag","<"+x).setGroup("def",v.def).getRegexp(),this.rulesBase=v},k.getRulesGfm=function(){if(this.rulesGfm)return this.rulesGfm;var v=this.getRulesBase(),x=Object.assign(Object.assign({},v),{fences:/^ *(`{3,}|~{3,})[ \.]*((\S+)? *[^\n]*)\n([\s\S]*?)\s*\1 *(?:\n+|$)/,paragraph:/^/,heading:/^ *(#{1,6}) +([^\n]+?) *#* *(?:\n+|$)/}),R=x.fences.source.replace("\\1","\\2"),E=v.list.source.replace("\\1","\\3");return x.paragraph=new t(v.paragraph).setGroup("(?!","(?!"+R+"|"+E+"|").getRegexp(),this.rulesGfm=x},k.getRulesTable=function(){return this.rulesTables?this.rulesTables:this.rulesTables=Object.assign(Object.assign({},this.getRulesGfm()),{nptable:/^ *(\S.*\|.*)\n *([-:]+ *\|[-| :]*)\n((?:.*\|.*(?:\n|$))*)\n*/,table:/^ *\|(.+)\n *\|( *[-:]+[-| :]*)\n((?: *\|.*(?:\n|$))*)\n*/})},k.prototype.setRules=function(){this.options.gfm?this.options.tables?this.rules=this.staticThis.getRulesTable():this.rules=this.staticThis.getRulesGfm():this.rules=this.staticThis.getRulesBase(),this.hasRulesGfm=this.rules.fences!==void 0,this.hasRulesTables=this.rules.table!==void 0},k.prototype.getTokens=function(v,x,R){var E=v,O;t:for(;E;){if((O=this.rules.newline.exec(E))&&(E=E.substring(O[0].length),O[0].length>1&&this.tokens.push({type:n.TokenType.space})),O=this.rules.code.exec(E)){E=E.substring(O[0].length);var Z=O[0].replace(/^ {4}/gm,"");this.tokens.push({type:n.TokenType.code,text:this.options.pedantic?Z:Z.replace(/\n+$/,"")});continue}if(this.hasRulesGfm&&(O=this.rules.fences.exec(E))){E=E.substring(O[0].length),this.tokens.push({type:n.TokenType.code,meta:O[2],lang:O[3],text:O[4]||""});continue}if(O=this.rules.heading.exec(E)){E=E.substring(O[0].length),this.tokens.push({type:n.TokenType.heading,depth:O[1].length,text:O[2]});continue}if(x&&this.hasRulesTables&&(O=this.rules.nptable.exec(E))){E=E.substring(O[0].length);for(var V={type:n.TokenType.table,header:O[1].replace(/^ *| *\| *$/g,"").split(/ *\| */),align:O[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:[]},J=0;J<V.align.length;J++)/^ *-+: *$/.test(V.align[J])?V.align[J]="right":/^ *:-+: *$/.test(V.align[J])?V.align[J]="center":/^ *:-+ *$/.test(V.align[J])?V.align[J]="left":V.align[J]=null;for(var tt=O[3].replace(/\n$/,"").split(`
`),J=0;J<tt.length;J++)V.cells[J]=tt[J].split(/ *\| */);this.tokens.push(V);continue}if(O=this.rules.lheading.exec(E)){E=E.substring(O[0].length),this.tokens.push({type:n.TokenType.heading,depth:O[2]==="="?1:2,text:O[1]});continue}if(O=this.rules.hr.exec(E)){E=E.substring(O[0].length),this.tokens.push({type:n.TokenType.hr});continue}if(O=this.rules.blockquote.exec(E)){E=E.substring(O[0].length),this.tokens.push({type:n.TokenType.blockquoteStart});var xt=O[0].replace(/^ *> ?/gm,"");this.getTokens(xt),this.tokens.push({type:n.TokenType.blockquoteEnd});continue}if(O=this.rules.list.exec(E)){E=E.substring(O[0].length);var Ht=O[2];this.tokens.push({type:n.TokenType.listStart,ordered:Ht.length>1});for(var xt=O[0].match(this.rules.item),Ot=xt.length,Yt=!1,Xt=void 0,ue=void 0,ke=void 0,J=0;J<Ot;J++){var V=xt[J];Xt=V.length,V=V.replace(/^ *([*+-]|\d+\.) +/,""),V.indexOf(`
 `)!==-1&&(Xt-=V.length,V=this.options.pedantic?V.replace(/^ {1,4}/gm,""):V.replace(new RegExp("^ {1,"+Xt+"}","gm"),"")),this.options.smartLists&&J!==Ot-1&&(ue=this.staticThis.getRulesBase().bullet.exec(xt[J+1])[0],Ht!==ue&&!(Ht.length>1&&ue.length>1)&&(E=xt.slice(J+1).join(`
`)+E,J=Ot-1)),ke=Yt||/\n\n(?!\s*$)/.test(V),J!==Ot-1&&(Yt=V.charAt(V.length-1)===`
`,ke||(ke=Yt)),this.tokens.push({type:ke?n.TokenType.looseItemStart:n.TokenType.listItemStart}),this.getTokens(V,!1,R),this.tokens.push({type:n.TokenType.listItemEnd})}this.tokens.push({type:n.TokenType.listEnd});continue}if(O=this.rules.html.exec(E)){E=E.substring(O[0].length);var xe=O[1],Rn=xe==="pre"||xe==="script"||xe==="style";this.tokens.push({type:this.options.sanitize?n.TokenType.paragraph:n.TokenType.html,pre:!this.options.sanitizer&&Rn,text:O[0]});continue}if(x&&(O=this.rules.def.exec(E))){E=E.substring(O[0].length),this.links[O[1].toLowerCase()]={href:O[2],title:O[3]};continue}if(x&&this.hasRulesTables&&(O=this.rules.table.exec(E))){E=E.substring(O[0].length);for(var V={type:n.TokenType.table,header:O[1].replace(/^ *| *\| *$/g,"").split(/ *\| */),align:O[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:[]},J=0;J<V.align.length;J++)/^ *-+: *$/.test(V.align[J])?V.align[J]="right":/^ *:-+: *$/.test(V.align[J])?V.align[J]="center":/^ *:-+ *$/.test(V.align[J])?V.align[J]="left":V.align[J]=null;for(var tt=O[3].replace(/(?: *\| *)?\n$/,"").split(`
`),J=0;J<tt.length;J++)V.cells[J]=tt[J].replace(/^ *\| *| *\| *$/g,"").split(/ *\| */);this.tokens.push(V);continue}if(this.staticThis.simpleRules.length){for(var un=this.staticThis.simpleRules,J=0;J<un.length;J++)if(O=un[J].exec(E)){E=E.substring(O[0].length);var Ln="simpleRule"+(J+1);this.tokens.push({type:Ln,execArr:O});continue t}}if(x&&(O=this.rules.paragraph.exec(E))){E=E.substring(O[0].length),O[1].slice(-1)===`
`?this.tokens.push({type:n.TokenType.paragraph,text:O[1].slice(0,-1)}):this.tokens.push({type:this.tokens.length>0?n.TokenType.paragraph:n.TokenType.text,text:O[1]});continue}if(O=this.rules.text.exec(E)){E=E.substring(O[0].length),this.tokens.push({type:n.TokenType.text,text:O[0]});continue}if(E)throw new Error("Infinite loop on byte: "+E.charCodeAt(0)+(", near text '"+E.slice(0,30)+"...'"))}return{tokens:this.tokens,links:this.links}},k}();Qt.simpleRules=[],Qt.rulesBase=null,Qt.rulesGfm=null,Qt.rulesTables=null,n.BlockLexer=Qt,n.ExtendRegexp=t,n.InlineLexer=Lt,n.Marked=Ut,n.MarkedOptions=c,n.Parser=Bt,n.Renderer=ot,n.escape=a,n.unescape=l,Object.defineProperty(n,"__esModule",{value:!0})})});var ef=Xo(Ei=>{"use strict";var Dy=Ei&&Ei.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,s){i.__proto__=s}||function(i,s){for(var r in s)s.hasOwnProperty(r)&&(i[r]=s[r])},n(t,e)};return function(t,e){n(t,e);function i(){this.constructor=t}t.prototype=e===null?Object.create(e):(i.prototype=e.prototype,new i)}}();Object.defineProperty(Ei,"__esModule",{value:!0});Ei.Extractor=void 0;var Ua=tf(),Ey=function(n){Dy(t,n);function t(e,i){var s=n.call(this)||this;return s.lowercaseKeys=i??!1,s.reset(e),s}return Object.defineProperty(t.prototype,"tables",{get:function(){return this.extractedTables},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"objects",{get:function(){var e=this;return this.extractedTables.map(function(i){return t.tableToObject(i,e.lowercaseKeys)})},enumerable:!1,configurable:!0}),t.prototype.reset=function(e){e===void 0&&(e="rows"),this.mode=e,this.currentRow=[],this.currentTable=[],this.extractedTables=[]},t.prototype.table=function(e,i){return this.extractedTables.push(this.mode==="rows"?this.currentTable:t.transposeTable(this.currentTable)),this.currentTable=[],n.prototype.table.call(this,e,i)},t.prototype.tablerow=function(e){return this.currentTable.push(this.currentRow),this.currentRow=[],n.prototype.tablerow.call(this,e)},t.prototype.tablecell=function(e,i){return this.currentRow.push(e),n.prototype.tablecell.call(this,e,i)},t.transposeTable=function(e){for(var i=[],s=e.length,r=e[0].length,o=0;o<r;o++){i.push([]);for(var a=0;a<s;a++)i[o].push(e[a][o])}return i},t.tableToObject=function(e,i){var s=e.shift().slice(1),r={};return e.forEach(function(o){var a=o.shift(),l={};o.forEach(function(c,h){l[i?s[h].toLowerCase():s[h]]=c}),r[i?a.toLowerCase():a]=l}),r},t.createExtractor=function(e,i,s){var r=new t(i,s);return Ua.Marked.setOptions({renderer:r}),Ua.Marked.parse(e),r},t.extractObject=function(e,i,s){var r=t.extractAllObjects(e,i,s);return r.length>0?r[0]:null},t.extractAllObjects=function(e,i,s){var r=t.createExtractor(e,i,s);return r.objects},t.extractTable=function(e,i,s){var r=t.extractAllTables(e,i,s);return r.length>0?r[0]:null},t.extractAllTables=function(e,i,s){var r=t.createExtractor(e,i,s);return r.tables},t.extract=function(e,i,s){var r=t.createExtractor(e,i,s);return r.objects.length>0?JSON.stringify(r.objects[0]):null},t.extractAll=function(e,i,s){var r=t.createExtractor(e,i,s);return r.objects.map(function(o){return JSON.stringify(o)})},t}(Ua.Renderer);Ei.Extractor=Ey});fb(exports,{default:()=>Ol});var $e=ze(require("obsidian"));function Ee(){}var qo=function(){let n=0;return function(){return n++}}();function ut(n){return n===null||typeof n=="undefined"}function pt(n){if(Array.isArray&&Array.isArray(n))return!0;let t=Object.prototype.toString.call(n);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function at(n){return n!==null&&Object.prototype.toString.call(n)==="[object Object]"}var jt=n=>(typeof n=="number"||n instanceof Number)&&isFinite(+n);function me(n,t){return jt(n)?n:t}function nt(n,t){return typeof n=="undefined"?t:n}var Go=(n,t)=>typeof n=="string"&&n.endsWith("%")?parseFloat(n)/100:n/t,Ks=(n,t)=>typeof n=="string"&&n.endsWith("%")?parseFloat(n)/100*t:+n;function wt(n,t,e){if(n&&typeof n.call=="function")return n.apply(e,t)}function Ct(n,t,e,i){let s,r,o;if(pt(n))if(r=n.length,i)for(s=r-1;s>=0;s--)t.call(e,n[s],s);else for(s=0;s<r;s++)t.call(e,n[s],s);else if(at(n))for(o=Object.keys(n),r=o.length,s=0;s<r;s++)t.call(e,n[o[s]],o[s])}function yi(n,t){let e,i,s,r;if(!n||!t||n.length!==t.length)return!1;for(e=0,i=n.length;e<i;++e)if(s=n[e],r=t[e],s.datasetIndex!==r.datasetIndex||s.index!==r.index)return!1;return!0}function Wi(n){if(pt(n))return n.map(Wi);if(at(n)){let t=Object.create(null),e=Object.keys(n),i=e.length,s=0;for(;s<i;++s)t[e[s]]=Wi(n[e[s]]);return t}return n}function Gc(n){return["__proto__","prototype","constructor"].indexOf(n)===-1}function Uc(n,t,e,i){if(!Gc(n))return;let s=t[n],r=e[n];at(s)&&at(r)?Hn(s,r,i):t[n]=Wi(r)}function Hn(n,t,e){let i=pt(t)?t:[t],s=i.length;if(!at(n))return n;e=e||{};let r=e.merger||Uc;for(let o=0;o<s;++o){if(t=i[o],!at(t))continue;let a=Object.keys(t);for(let l=0,c=a.length;l<c;++l)r(a[l],n,t,e)}return n}function Wn(n,t){return Hn(n,t,{merger:Kc})}function Kc(n,t,e){if(!Gc(n))return;let i=t[n],s=e[n];at(i)&&at(s)?Wn(i,s):Object.prototype.hasOwnProperty.call(t,n)||(t[n]=Wi(s))}var Zc={"":n=>n,x:n=>n.x,y:n=>n.y};function Be(n,t){return(Zc[t]||(Zc[t]=pb(t)))(n)}function pb(n){let t=Jc(n);return e=>{for(let i of t){if(i==="")break;e=e&&e[i]}return e}}function Jc(n){let t=n.split("."),e=[],i="";for(let s of t)i+=s,i.endsWith("\\")?i=i.slice(0,-1)+".":(e.push(i),i="");return e}function Vi(n){return n.charAt(0).toUpperCase()+n.slice(1)}var Nt=n=>typeof n!="undefined",le=n=>typeof n=="function",Zs=(n,t)=>{if(n.size!==t.size)return!1;for(let e of n)if(!t.has(e))return!1;return!0};function Uo(n){return n.type==="mouseup"||n.type==="click"||n.type==="contextmenu"}var gt=Math.PI,kt=2*gt,Qc=kt+gt,Yi=Number.POSITIVE_INFINITY,Xi=gt/180,Rt=gt/2,Se=gt/4,xi=gt*2/3,be=Math.log10,Me=Math.sign;function Js(n){let t=Math.round(n);n=Vn(n,t,n/1e3)?t:n;let e=Math.pow(10,Math.floor(be(n))),i=n/e;return(i<=1?1:i<=2?2:i<=5?5:10)*e}function Ko(n){let t=[],e=Math.sqrt(n),i;for(i=1;i<e;i++)n%i==0&&(t.push(i),t.push(n/i));return e===(e|0)&&t.push(e),t.sort((s,r)=>s-r).pop(),t}function Oe(n){return!isNaN(parseFloat(n))&&isFinite(n)}function Vn(n,t,e){return Math.abs(n-t)<e}function Zo(n,t){let e=Math.round(n);return e-t<=n&&e+t>=n}function Qs(n,t,e){let i,s,r;for(i=0,s=n.length;i<s;i++)r=n[i][e],isNaN(r)||(t.min=Math.min(t.min,r),t.max=Math.max(t.max,r))}function Wt(n){return n*(gt/180)}function Yn(n){return n*(180/gt)}function tr(n){if(!jt(n))return;let t=1,e=0;for(;Math.round(n*t)/t!==n;)t*=10,e++;return e}function er(n,t){let e=t.x-n.x,i=t.y-n.y,s=Math.sqrt(e*e+i*i),r=Math.atan2(i,e);return r<-.5*gt&&(r+=kt),{angle:r,distance:s}}function Ke(n,t){return Math.sqrt(Math.pow(t.x-n.x,2)+Math.pow(t.y-n.y,2))}function th(n,t){return(n-t+Qc)%kt-gt}function fe(n){return(n%kt+kt)%kt}function Xn(n,t,e,i){let s=fe(n),r=fe(t),o=fe(e),a=fe(r-s),l=fe(o-s),c=fe(s-r),h=fe(s-o);return s===r||s===o||i&&r===o||a>l&&c<h}function Zt(n,t,e){return Math.max(t,Math.min(e,n))}function Jo(n){return Zt(n,-32768,32767)}function Ae(n,t,e,i=1e-6){return n>=Math.min(t,e)-i&&n<=Math.max(t,e)+i}function qi(n,t,e){e=e||(o=>n[o]<t);let i=n.length-1,s=0,r;for(;i-s>1;)r=s+i>>1,e(r)?s=r:i=r;return{lo:s,hi:i}}var Re=(n,t,e,i)=>qi(n,e,i?s=>n[s][t]<=e:s=>n[s][t]<e),Qo=(n,t,e)=>qi(n,e,i=>n[i][t]>=e);function ta(n,t,e){let i=0,s=n.length;for(;i<s&&n[i]<t;)i++;for(;s>i&&n[s-1]>e;)s--;return i>0||s<n.length?n.slice(i,s):n}var eh=["push","pop","shift","splice","unshift"];function ea(n,t){if(n._chartjs){n._chartjs.listeners.push(t);return}Object.defineProperty(n,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),eh.forEach(e=>{let i="_onData"+Vi(e),s=n[e];Object.defineProperty(n,e,{configurable:!0,enumerable:!1,value(...r){let o=s.apply(this,r);return n._chartjs.listeners.forEach(a=>{typeof a[i]=="function"&&a[i](...r)}),o}})})}function nr(n,t){let e=n._chartjs;if(!e)return;let i=e.listeners,s=i.indexOf(t);s!==-1&&i.splice(s,1),!(i.length>0)&&(eh.forEach(r=>{delete n[r]}),delete n._chartjs)}function ir(n){let t=new Set,e,i;for(e=0,i=n.length;e<i;++e)t.add(n[e]);return t.size===i?n:Array.from(t)}var sr=function(){return typeof window=="undefined"?function(n){return n()}:window.requestAnimationFrame}();function rr(n,t,e){let i=e||(o=>Array.prototype.slice.call(o)),s=!1,r=[];return function(...o){r=i(o),s||(s=!0,sr.call(window,()=>{s=!1,n.apply(t,r)}))}}function na(n,t){let e;return function(...i){return t?(clearTimeout(e),e=setTimeout(n,t,i)):n.apply(this,i),t}}var Gi=n=>n==="start"?"left":n==="end"?"right":"center",ie=(n,t,e)=>n==="start"?t:n==="end"?e:(t+e)/2,ia=(n,t,e,i)=>n===(i?"left":"right")?e:n==="center"?(t+e)/2:t;function or(n,t,e){let i=t.length,s=0,r=i;if(n._sorted){let{iScale:o,_parsed:a}=n,l=o.axis,{min:c,max:h,minDefined:f,maxDefined:g}=o.getUserBounds();f&&(s=Zt(Math.min(Re(a,o.axis,c).lo,e?i:Re(t,l,o.getPixelForValue(c)).lo),0,i-1)),g?r=Zt(Math.max(Re(a,o.axis,h,!0).hi+1,e?0:Re(t,l,o.getPixelForValue(h),!0).hi+1),s,i)-s:r=i-s}return{start:s,count:r}}function ar(n){let{xScale:t,yScale:e,_scaleRanges:i}=n,s={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!i)return n._scaleRanges=s,!0;let r=i.xmin!==t.min||i.xmax!==t.max||i.ymin!==e.min||i.ymax!==e.max;return Object.assign(i,s),r}var lr=n=>n===0||n===1,nh=(n,t,e)=>-(Math.pow(2,10*(n-=1))*Math.sin((n-t)*kt/e)),ih=(n,t,e)=>Math.pow(2,-10*n)*Math.sin((n-t)*kt/e)+1,qn={linear:n=>n,easeInQuad:n=>n*n,easeOutQuad:n=>-n*(n-2),easeInOutQuad:n=>(n/=.5)<1?.5*n*n:-.5*(--n*(n-2)-1),easeInCubic:n=>n*n*n,easeOutCubic:n=>(n-=1)*n*n+1,easeInOutCubic:n=>(n/=.5)<1?.5*n*n*n:.5*((n-=2)*n*n+2),easeInQuart:n=>n*n*n*n,easeOutQuart:n=>-((n-=1)*n*n*n-1),easeInOutQuart:n=>(n/=.5)<1?.5*n*n*n*n:-.5*((n-=2)*n*n*n-2),easeInQuint:n=>n*n*n*n*n,easeOutQuint:n=>(n-=1)*n*n*n*n+1,easeInOutQuint:n=>(n/=.5)<1?.5*n*n*n*n*n:.5*((n-=2)*n*n*n*n+2),easeInSine:n=>-Math.cos(n*Rt)+1,easeOutSine:n=>Math.sin(n*Rt),easeInOutSine:n=>-.5*(Math.cos(gt*n)-1),easeInExpo:n=>n===0?0:Math.pow(2,10*(n-1)),easeOutExpo:n=>n===1?1:-Math.pow(2,-10*n)+1,easeInOutExpo:n=>lr(n)?n:n<.5?.5*Math.pow(2,10*(n*2-1)):.5*(-Math.pow(2,-10*(n*2-1))+2),easeInCirc:n=>n>=1?n:-(Math.sqrt(1-n*n)-1),easeOutCirc:n=>Math.sqrt(1-(n-=1)*n),easeInOutCirc:n=>(n/=.5)<1?-.5*(Math.sqrt(1-n*n)-1):.5*(Math.sqrt(1-(n-=2)*n)+1),easeInElastic:n=>lr(n)?n:nh(n,.075,.3),easeOutElastic:n=>lr(n)?n:ih(n,.075,.3),easeInOutElastic(n){let t=.1125,e=.45;return lr(n)?n:n<.5?.5*nh(n*2,t,e):.5+.5*ih(n*2-1,t,e)},easeInBack(n){let t=1.70158;return n*n*((t+1)*n-t)},easeOutBack(n){let t=1.70158;return(n-=1)*n*((t+1)*n+t)+1},easeInOutBack(n){let t=1.70158;return(n/=.5)<1?.5*(n*n*(((t*=1.525)+1)*n-t)):.5*((n-=2)*n*(((t*=1.525)+1)*n+t)+2)},easeInBounce:n=>1-qn.easeOutBounce(1-n),easeOutBounce(n){let t=7.5625,e=2.75;return n<1/e?t*n*n:n<2/e?t*(n-=1.5/e)*n+.75:n<2.5/e?t*(n-=2.25/e)*n+.9375:t*(n-=2.625/e)*n+.984375},easeInOutBounce:n=>n<.5?qn.easeInBounce(n*2)*.5:qn.easeOutBounce(n*2-1)*.5+.5};function Ui(n){return n+.5|0}var vn=(n,t,e)=>Math.max(Math.min(n,e),t);function Ki(n){return vn(Ui(n*2.55),0,255)}function _n(n){return vn(Ui(n*255),0,255)}function Ze(n){return vn(Ui(n/2.55)/100,0,1)}function sh(n){return vn(Ui(n*100),0,100)}var Ce={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},sa=[..."0123456789ABCDEF"],gb=n=>sa[n&15],mb=n=>sa[(n&240)>>4]+sa[n&15],cr=n=>(n&240)>>4==(n&15),bb=n=>cr(n.r)&&cr(n.g)&&cr(n.b)&&cr(n.a);function vb(n){var t=n.length,e;return n[0]==="#"&&(t===4||t===5?e={r:255&Ce[n[1]]*17,g:255&Ce[n[2]]*17,b:255&Ce[n[3]]*17,a:t===5?Ce[n[4]]*17:255}:(t===7||t===9)&&(e={r:Ce[n[1]]<<4|Ce[n[2]],g:Ce[n[3]]<<4|Ce[n[4]],b:Ce[n[5]]<<4|Ce[n[6]],a:t===9?Ce[n[7]]<<4|Ce[n[8]]:255})),e}var _b=(n,t)=>n<255?t(n):"";function yb(n){var t=bb(n)?gb:mb;return n?"#"+t(n.r)+t(n.g)+t(n.b)+_b(n.a,t):void 0}var xb=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function rh(n,t,e){let i=t*Math.min(e,1-e),s=(r,o=(r+n/30)%12)=>e-i*Math.max(Math.min(o-3,9-o,1),-1);return[s(0),s(8),s(4)]}function wb(n,t,e){let i=(s,r=(s+n/60)%6)=>e-e*t*Math.max(Math.min(r,4-r,1),0);return[i(5),i(3),i(1)]}function kb(n,t,e){let i=rh(n,1,.5),s;for(t+e>1&&(s=1/(t+e),t*=s,e*=s),s=0;s<3;s++)i[s]*=1-t-e,i[s]+=t;return i}function Sb(n,t,e,i,s){return n===s?(t-e)/i+(t<e?6:0):t===s?(e-n)/i+2:(n-t)/i+4}function ra(n){let t=255,e=n.r/t,i=n.g/t,s=n.b/t,r=Math.max(e,i,s),o=Math.min(e,i,s),a=(r+o)/2,l,c,h;return r!==o&&(h=r-o,c=a>.5?h/(2-r-o):h/(r+o),l=Sb(e,i,s,h,r),l=l*60+.5),[l|0,c||0,a]}function oa(n,t,e,i){return(Array.isArray(t)?n(t[0],t[1],t[2]):n(t,e,i)).map(_n)}function aa(n,t,e){return oa(rh,n,t,e)}function Mb(n,t,e){return oa(kb,n,t,e)}function Cb(n,t,e){return oa(wb,n,t,e)}function oh(n){return(n%360+360)%360}function Pb(n){let t=xb.exec(n),e=255,i;if(!t)return;t[5]!==i&&(e=t[6]?Ki(+t[5]):_n(+t[5]));let s=oh(+t[2]),r=+t[3]/100,o=+t[4]/100;return t[1]==="hwb"?i=Mb(s,r,o):t[1]==="hsv"?i=Cb(s,r,o):i=aa(s,r,o),{r:i[0],g:i[1],b:i[2],a:e}}function Tb(n,t){var e=ra(n);e[0]=oh(e[0]+t),e=aa(e),n.r=e[0],n.g=e[1],n.b=e[2]}function Db(n){if(!n)return;let t=ra(n),e=t[0],i=sh(t[1]),s=sh(t[2]);return n.a<255?`hsla(${e}, ${i}%, ${s}%, ${Ze(n.a)})`:`hsl(${e}, ${i}%, ${s}%)`}var ah={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},lh={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function Eb(){let n={},t=Object.keys(lh),e=Object.keys(ah),i,s,r,o,a;for(i=0;i<t.length;i++){for(o=a=t[i],s=0;s<e.length;s++)r=e[s],a=a.replace(r,ah[r]);r=parseInt(lh[o],16),n[a]=[r>>16&255,r>>8&255,r&255]}return n}var hr;function Ob(n){hr||(hr=Eb(),hr.transparent=[0,0,0,0]);let t=hr[n.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}var Ab=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Rb(n){let t=Ab.exec(n),e=255,i,s,r;if(!!t){if(t[7]!==i){let o=+t[7];e=t[8]?Ki(o):vn(o*255,0,255)}return i=+t[1],s=+t[3],r=+t[5],i=255&(t[2]?Ki(i):vn(i,0,255)),s=255&(t[4]?Ki(s):vn(s,0,255)),r=255&(t[6]?Ki(r):vn(r,0,255)),{r:i,g:s,b:r,a:e}}}function Lb(n){return n&&(n.a<255?`rgba(${n.r}, ${n.g}, ${n.b}, ${Ze(n.a)})`:`rgb(${n.r}, ${n.g}, ${n.b})`)}var la=n=>n<=.0031308?n*12.92:Math.pow(n,1/2.4)*1.055-.055,wi=n=>n<=.04045?n/12.92:Math.pow((n+.055)/1.055,2.4);function Fb(n,t,e){let i=wi(Ze(n.r)),s=wi(Ze(n.g)),r=wi(Ze(n.b));return{r:_n(la(i+e*(wi(Ze(t.r))-i))),g:_n(la(s+e*(wi(Ze(t.g))-s))),b:_n(la(r+e*(wi(Ze(t.b))-r))),a:n.a+e*(t.a-n.a)}}function ur(n,t,e){if(n){let i=ra(n);i[t]=Math.max(0,Math.min(i[t]+i[t]*e,t===0?360:1)),i=aa(i),n.r=i[0],n.g=i[1],n.b=i[2]}}function ch(n,t){return n&&Object.assign(t||{},n)}function hh(n){var t={r:0,g:0,b:0,a:255};return Array.isArray(n)?n.length>=3&&(t={r:n[0],g:n[1],b:n[2],a:255},n.length>3&&(t.a=_n(n[3]))):(t=ch(n,{r:0,g:0,b:0,a:1}),t.a=_n(t.a)),t}function Ib(n){return n.charAt(0)==="r"?Rb(n):Pb(n)}var Zi=class{constructor(t){if(t instanceof Zi)return t;let e=typeof t,i;e==="object"?i=hh(t):e==="string"&&(i=vb(t)||Ob(t)||Ib(t)),this._rgb=i,this._valid=!!i}get valid(){return this._valid}get rgb(){var t=ch(this._rgb);return t&&(t.a=Ze(t.a)),t}set rgb(t){this._rgb=hh(t)}rgbString(){return this._valid?Lb(this._rgb):void 0}hexString(){return this._valid?yb(this._rgb):void 0}hslString(){return this._valid?Db(this._rgb):void 0}mix(t,e){if(t){let i=this.rgb,s=t.rgb,r,o=e===r?.5:e,a=2*o-1,l=i.a-s.a,c=((a*l==-1?a:(a+l)/(1+a*l))+1)/2;r=1-c,i.r=255&c*i.r+r*s.r+.5,i.g=255&c*i.g+r*s.g+.5,i.b=255&c*i.b+r*s.b+.5,i.a=o*i.a+(1-o)*s.a,this.rgb=i}return this}interpolate(t,e){return t&&(this._rgb=Fb(this._rgb,t._rgb,e)),this}clone(){return new Zi(this.rgb)}alpha(t){return this._rgb.a=_n(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=Ui(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return ur(this._rgb,2,t),this}darken(t){return ur(this._rgb,2,-t),this}saturate(t){return ur(this._rgb,1,t),this}desaturate(t){return ur(this._rgb,1,-t),this}rotate(t){return Tb(this._rgb,t),this}};function uh(n){return new Zi(n)}function ca(n){if(n&&typeof n=="object"){let t=n.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Je(n){return ca(n)?n:uh(n)}function Gn(n){return ca(n)?n:uh(n).saturate(.5).darken(.1).hexString()}var yn=Object.create(null),fr=Object.create(null);function Ji(n,t){if(!t)return n;let e=t.split(".");for(let i=0,s=e.length;i<s;++i){let r=e[i];n=n[r]||(n[r]=Object.create(null))}return n}function ha(n,t,e){return typeof t=="string"?Hn(Ji(n,t),e):Hn(Ji(n,""),t)}var fh=class{constructor(t){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=e=>e.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(e,i)=>Gn(i.backgroundColor),this.hoverBorderColor=(e,i)=>Gn(i.borderColor),this.hoverColor=(e,i)=>Gn(i.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t)}set(t,e){return ha(this,t,e)}get(t){return Ji(this,t)}describe(t,e){return ha(fr,t,e)}override(t,e){return ha(yn,t,e)}route(t,e,i,s){let r=Ji(this,t),o=Ji(this,i),a="_"+e;Object.defineProperties(r,{[a]:{value:r[e],writable:!0},[e]:{enumerable:!0,get(){let l=this[a],c=o[s];return at(l)?Object.assign({},c,l):nt(l,c)},set(l){this[a]=l}}})}},ft=new fh({_scriptable:n=>!n.startsWith("on"),_indexable:n=>n!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}});function dh(n){return!n||ut(n.size)||ut(n.family)?null:(n.style?n.style+" ":"")+(n.weight?n.weight+" ":"")+n.size+"px "+n.family}function ki(n,t,e,i,s){let r=t[s];return r||(r=t[s]=n.measureText(s).width,e.push(s)),r>i&&(i=r),i}function ua(n,t,e,i){i=i||{};let s=i.data=i.data||{},r=i.garbageCollect=i.garbageCollect||[];i.font!==t&&(s=i.data={},r=i.garbageCollect=[],i.font=t),n.save(),n.font=t;let o=0,a=e.length,l,c,h,f,g;for(l=0;l<a;l++)if(f=e[l],f!=null&&pt(f)!==!0)o=ki(n,s,r,o,f);else if(pt(f))for(c=0,h=f.length;c<h;c++)g=f[c],g!=null&&!pt(g)&&(o=ki(n,s,r,o,g));n.restore();let p=r.length/2;if(p>e.length){for(l=0;l<p;l++)delete s[r[l]];r.splice(0,p)}return o}function Qe(n,t,e){let i=n.currentDevicePixelRatio,s=e!==0?Math.max(e/2,.5):0;return Math.round((t-s)*i)/i+s}function dr(n,t){t=t||n.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,n.width,n.height),t.restore()}function Qi(n,t,e,i){pr(n,t,e,i,null)}function pr(n,t,e,i,s){let r,o,a,l,c,h,f=t.pointStyle,g=t.rotation,p=t.radius,m=(g||0)*Xi;if(f&&typeof f=="object"&&(r=f.toString(),r==="[object HTMLImageElement]"||r==="[object HTMLCanvasElement]")){n.save(),n.translate(e,i),n.rotate(m),n.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),n.restore();return}if(!(isNaN(p)||p<=0)){switch(n.beginPath(),f){default:s?n.ellipse(e,i,s/2,p,0,0,kt):n.arc(e,i,p,0,kt),n.closePath();break;case"triangle":n.moveTo(e+Math.sin(m)*p,i-Math.cos(m)*p),m+=xi,n.lineTo(e+Math.sin(m)*p,i-Math.cos(m)*p),m+=xi,n.lineTo(e+Math.sin(m)*p,i-Math.cos(m)*p),n.closePath();break;case"rectRounded":c=p*.516,l=p-c,o=Math.cos(m+Se)*l,a=Math.sin(m+Se)*l,n.arc(e-o,i-a,c,m-gt,m-Rt),n.arc(e+a,i-o,c,m-Rt,m),n.arc(e+o,i+a,c,m,m+Rt),n.arc(e-a,i+o,c,m+Rt,m+gt),n.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,h=s?s/2:l,n.rect(e-h,i-l,2*h,2*l);break}m+=Se;case"rectRot":o=Math.cos(m)*p,a=Math.sin(m)*p,n.moveTo(e-o,i-a),n.lineTo(e+a,i-o),n.lineTo(e+o,i+a),n.lineTo(e-a,i+o),n.closePath();break;case"crossRot":m+=Se;case"cross":o=Math.cos(m)*p,a=Math.sin(m)*p,n.moveTo(e-o,i-a),n.lineTo(e+o,i+a),n.moveTo(e+a,i-o),n.lineTo(e-a,i+o);break;case"star":o=Math.cos(m)*p,a=Math.sin(m)*p,n.moveTo(e-o,i-a),n.lineTo(e+o,i+a),n.moveTo(e+a,i-o),n.lineTo(e-a,i+o),m+=Se,o=Math.cos(m)*p,a=Math.sin(m)*p,n.moveTo(e-o,i-a),n.lineTo(e+o,i+a),n.moveTo(e+a,i-o),n.lineTo(e-a,i+o);break;case"line":o=s?s/2:Math.cos(m)*p,a=Math.sin(m)*p,n.moveTo(e-o,i-a),n.lineTo(e+o,i+a);break;case"dash":n.moveTo(e,i),n.lineTo(e+Math.cos(m)*p,i+Math.sin(m)*p);break}n.fill(),t.borderWidth>0&&n.stroke()}}function Un(n,t,e){return e=e||.5,!t||n&&n.x>t.left-e&&n.x<t.right+e&&n.y>t.top-e&&n.y<t.bottom+e}function xn(n,t){n.save(),n.beginPath(),n.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),n.clip()}function wn(n){n.restore()}function fa(n,t,e,i,s){if(!t)return n.lineTo(e.x,e.y);if(s==="middle"){let r=(t.x+e.x)/2;n.lineTo(r,t.y),n.lineTo(r,e.y)}else s==="after"!=!!i?n.lineTo(t.x,e.y):n.lineTo(e.x,t.y);n.lineTo(e.x,e.y)}function da(n,t,e,i){if(!t)return n.lineTo(e.x,e.y);n.bezierCurveTo(i?t.cp1x:t.cp2x,i?t.cp1y:t.cp2y,i?e.cp2x:e.cp1x,i?e.cp2y:e.cp1y,e.x,e.y)}function tn(n,t,e,i,s,r={}){let o=pt(t)?t:[t],a=r.strokeWidth>0&&r.strokeColor!=="",l,c;for(n.save(),n.font=s.string,$b(n,r),l=0;l<o.length;++l)c=o[l],a&&(r.strokeColor&&(n.strokeStyle=r.strokeColor),ut(r.strokeWidth)||(n.lineWidth=r.strokeWidth),n.strokeText(c,e,i,r.maxWidth)),n.fillText(c,e,i,r.maxWidth),jb(n,e,i,c,r),i+=s.lineHeight;n.restore()}function $b(n,t){t.translation&&n.translate(t.translation[0],t.translation[1]),ut(t.rotation)||n.rotate(t.rotation),t.color&&(n.fillStyle=t.color),t.textAlign&&(n.textAlign=t.textAlign),t.textBaseline&&(n.textBaseline=t.textBaseline)}function jb(n,t,e,i,s){if(s.strikethrough||s.underline){let r=n.measureText(i),o=t-r.actualBoundingBoxLeft,a=t+r.actualBoundingBoxRight,l=e-r.actualBoundingBoxAscent,c=e+r.actualBoundingBoxDescent,h=s.strikethrough?(l+c)/2:c;n.strokeStyle=n.fillStyle,n.beginPath(),n.lineWidth=s.decorationWidth||2,n.moveTo(o,h),n.lineTo(a,h),n.stroke()}}function en(n,t){let{x:e,y:i,w:s,h:r,radius:o}=t;n.arc(e+o.topLeft,i+o.topLeft,o.topLeft,-Rt,gt,!0),n.lineTo(e,i+r-o.bottomLeft),n.arc(e+o.bottomLeft,i+r-o.bottomLeft,o.bottomLeft,gt,Rt,!0),n.lineTo(e+s-o.bottomRight,i+r),n.arc(e+s-o.bottomRight,i+r-o.bottomRight,o.bottomRight,Rt,0,!0),n.lineTo(e+s,i+o.topRight),n.arc(e+s-o.topRight,i+o.topRight,o.topRight,0,-Rt,!0),n.lineTo(e+o.topLeft,i)}var zb=new RegExp(/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/),Bb=new RegExp(/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/);function ph(n,t){let e=(""+n).match(zb);if(!e||e[1]==="normal")return t*1.2;switch(n=+e[2],e[3]){case"px":return n;case"%":n/=100;break}return t*n}var Nb=n=>+n||0;function ts(n,t){let e={},i=at(t),s=i?Object.keys(t):t,r=at(n)?i?o=>nt(n[o],n[t[o]]):o=>n[o]:()=>n;for(let o of s)e[o]=Nb(r(o));return e}function gr(n){return ts(n,{top:"y",right:"x",bottom:"y",left:"x"})}function Le(n){return ts(n,["topLeft","topRight","bottomLeft","bottomRight"])}function Vt(n){let t=gr(n);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function Ft(n,t){n=n||{},t=t||ft.font;let e=nt(n.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let i=nt(n.style,t.style);i&&!(""+i).match(Bb)&&(console.warn('Invalid font style specified: "'+i+'"'),i="");let s={family:nt(n.family,t.family),lineHeight:ph(nt(n.lineHeight,t.lineHeight),e),size:e,style:i,weight:nt(n.weight,t.weight),string:""};return s.string=dh(s),s}function Kn(n,t,e,i){let s=!0,r,o,a;for(r=0,o=n.length;r<o;++r)if(a=n[r],a!==void 0&&(t!==void 0&&typeof a=="function"&&(a=a(t),s=!1),e!==void 0&&pt(a)&&(a=a[e%a.length],s=!1),a!==void 0))return i&&!s&&(i.cacheable=!1),a}function pa(n,t,e){let{min:i,max:s}=n,r=Ks(t,(s-i)/2),o=(a,l)=>e&&a===0?0:a+l;return{min:o(i,-Math.abs(r)),max:o(s,r)}}function Ne(n,t){return Object.assign(Object.create(n),t)}function es(n,t=[""],e=n,i,s=()=>n[0]){Nt(i)||(i=vh("_fallback",n));let r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:n,_rootScopes:e,_fallback:i,_getTarget:s,override:o=>es([o,...n],t,e,i)};return new Proxy(r,{deleteProperty(o,a){return delete o[a],delete o._keys,delete n[0][a],!0},get(o,a){return gh(o,a,()=>Ub(a,t,n,o))},getOwnPropertyDescriptor(o,a){return Reflect.getOwnPropertyDescriptor(o._scopes[0],a)},getPrototypeOf(){return Reflect.getPrototypeOf(n[0])},has(o,a){return _h(o).includes(a)},ownKeys(o){return _h(o)},set(o,a,l){let c=o._storage||(o._storage=s());return o[a]=c[a]=l,delete o._keys,!0}})}function kn(n,t,e,i){let s={_cacheable:!1,_proxy:n,_context:t,_subProxy:e,_stack:new Set,_descriptors:mr(n,i),setContext:r=>kn(n,r,e,i),override:r=>kn(n.override(r),t,e,i)};return new Proxy(s,{deleteProperty(r,o){return delete r[o],delete n[o],!0},get(r,o,a){return gh(r,o,()=>Wb(r,o,a))},getOwnPropertyDescriptor(r,o){return r._descriptors.allKeys?Reflect.has(n,o)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(n,o)},getPrototypeOf(){return Reflect.getPrototypeOf(n)},has(r,o){return Reflect.has(n,o)},ownKeys(){return Reflect.ownKeys(n)},set(r,o,a){return n[o]=a,delete r[o],!0}})}function mr(n,t={scriptable:!0,indexable:!0}){let{_scriptable:e=t.scriptable,_indexable:i=t.indexable,_allKeys:s=t.allKeys}=n;return{allKeys:s,scriptable:e,indexable:i,isScriptable:le(e)?e:()=>e,isIndexable:le(i)?i:()=>i}}var Hb=(n,t)=>n?n+Vi(t):t,ga=(n,t)=>at(t)&&n!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function gh(n,t,e){if(Object.prototype.hasOwnProperty.call(n,t))return n[t];let i=e();return n[t]=i,i}function Wb(n,t,e){let{_proxy:i,_context:s,_subProxy:r,_descriptors:o}=n,a=i[t];return le(a)&&o.isScriptable(t)&&(a=Vb(t,a,n,e)),pt(a)&&a.length&&(a=Yb(t,a,n,o.isIndexable)),ga(t,a)&&(a=kn(a,s,r&&r[t],o)),a}function Vb(n,t,e,i){let{_proxy:s,_context:r,_subProxy:o,_stack:a}=e;if(a.has(n))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+n);return a.add(n),t=t(r,o||i),a.delete(n),ga(n,t)&&(t=ma(s._scopes,s,n,t)),t}function Yb(n,t,e,i){let{_proxy:s,_context:r,_subProxy:o,_descriptors:a}=e;if(Nt(r.index)&&i(n))t=t[r.index%t.length];else if(at(t[0])){let l=t,c=s._scopes.filter(h=>h!==l);t=[];for(let h of l){let f=ma(c,s,n,h);t.push(kn(f,r,o&&o[n],a))}}return t}function mh(n,t,e){return le(n)?n(t,e):n}var Xb=(n,t)=>n===!0?t:typeof n=="string"?Be(t,n):void 0;function qb(n,t,e,i,s){for(let r of t){let o=Xb(e,r);if(o){n.add(o);let a=mh(o._fallback,e,s);if(Nt(a)&&a!==e&&a!==i)return a}else if(o===!1&&Nt(i)&&e!==i)return null}return!1}function ma(n,t,e,i){let s=t._rootScopes,r=mh(t._fallback,e,i),o=[...n,...s],a=new Set;a.add(i);let l=bh(a,o,e,r||e,i);return l===null||Nt(r)&&r!==e&&(l=bh(a,o,r,l,i),l===null)?!1:es(Array.from(a),[""],s,r,()=>Gb(t,e,i))}function bh(n,t,e,i,s){for(;e;)e=qb(n,t,e,i,s);return e}function Gb(n,t,e){let i=n._getTarget();t in i||(i[t]={});let s=i[t];return pt(s)&&at(e)?e:s}function Ub(n,t,e,i){let s;for(let r of t)if(s=vh(Hb(r,n),e),Nt(s))return ga(n,s)?ma(e,i,n,s):s}function vh(n,t){for(let e of t){if(!e)continue;let i=e[n];if(Nt(i))return i}}function _h(n){let t=n._keys;return t||(t=n._keys=Kb(n._scopes)),t}function Kb(n){let t=new Set;for(let e of n)for(let i of Object.keys(e).filter(s=>!s.startsWith("_")))t.add(i);return Array.from(t)}function br(n,t,e,i){let{iScale:s}=n,{key:r="r"}=this._parsing,o=new Array(i),a,l,c,h;for(a=0,l=i;a<l;++a)c=a+e,h=t[c],o[a]={r:s.parse(Be(h,r),c)};return o}var Zb=Number.EPSILON||1e-14,Si=(n,t)=>t<n.length&&!n[t].skip&&n[t],yh=n=>n==="x"?"y":"x";function xh(n,t,e,i){let s=n.skip?t:n,r=t,o=e.skip?t:e,a=Ke(r,s),l=Ke(o,r),c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;let f=i*c,g=i*h;return{previous:{x:r.x-f*(o.x-s.x),y:r.y-f*(o.y-s.y)},next:{x:r.x+g*(o.x-s.x),y:r.y+g*(o.y-s.y)}}}function Jb(n,t,e){let i=n.length,s,r,o,a,l,c=Si(n,0);for(let h=0;h<i-1;++h)if(l=c,c=Si(n,h+1),!(!l||!c)){if(Vn(t[h],0,Zb)){e[h]=e[h+1]=0;continue}s=e[h]/t[h],r=e[h+1]/t[h],a=Math.pow(s,2)+Math.pow(r,2),!(a<=9)&&(o=3/Math.sqrt(a),e[h]=s*o*t[h],e[h+1]=r*o*t[h])}}function Qb(n,t,e="x"){let i=yh(e),s=n.length,r,o,a,l=Si(n,0);for(let c=0;c<s;++c){if(o=a,a=l,l=Si(n,c+1),!a)continue;let h=a[e],f=a[i];o&&(r=(h-o[e])/3,a[`cp1${e}`]=h-r,a[`cp1${i}`]=f-r*t[c]),l&&(r=(l[e]-h)/3,a[`cp2${e}`]=h+r,a[`cp2${i}`]=f+r*t[c])}}function wh(n,t="x"){let e=yh(t),i=n.length,s=Array(i).fill(0),r=Array(i),o,a,l,c=Si(n,0);for(o=0;o<i;++o)if(a=l,l=c,c=Si(n,o+1),!!l){if(c){let h=c[t]-l[t];s[o]=h!==0?(c[e]-l[e])/h:0}r[o]=a?c?Me(s[o-1])!==Me(s[o])?0:(s[o-1]+s[o])/2:s[o-1]:s[o]}Jb(n,s,r),Qb(n,r,t)}function vr(n,t,e){return Math.max(Math.min(n,e),t)}function t0(n,t){let e,i,s,r,o,a=Un(n[0],t);for(e=0,i=n.length;e<i;++e)o=r,r=a,a=e<i-1&&Un(n[e+1],t),!!r&&(s=n[e],o&&(s.cp1x=vr(s.cp1x,t.left,t.right),s.cp1y=vr(s.cp1y,t.top,t.bottom)),a&&(s.cp2x=vr(s.cp2x,t.left,t.right),s.cp2y=vr(s.cp2y,t.top,t.bottom)))}function ba(n,t,e,i,s){let r,o,a,l;if(t.spanGaps&&(n=n.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")wh(n,s);else{let c=i?n[n.length-1]:n[0];for(r=0,o=n.length;r<o;++r)a=n[r],l=xh(c,a,n[Math.min(r+1,o-(i?0:1))%o],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&t0(n,e)}function _r(){return typeof window!="undefined"&&typeof document!="undefined"}function ns(n){let t=n.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function yr(n,t,e){let i;return typeof n=="string"?(i=parseInt(n,10),n.indexOf("%")!==-1&&(i=i/100*t.parentNode[e])):i=n,i}var xr=n=>window.getComputedStyle(n,null);function kh(n,t){return xr(n).getPropertyValue(t)}var e0=["top","right","bottom","left"];function Zn(n,t,e){let i={};e=e?"-"+e:"";for(let s=0;s<4;s++){let r=e0[s];i[r]=parseFloat(n[t+"-"+r+e])||0}return i.width=i.left+i.right,i.height=i.top+i.bottom,i}var n0=(n,t,e)=>(n>0||t>0)&&(!e||!e.shadowRoot);function i0(n,t){let e=n.touches,i=e&&e.length?e[0]:n,{offsetX:s,offsetY:r}=i,o=!1,a,l;if(n0(s,r,n.target))a=s,l=r;else{let c=t.getBoundingClientRect();a=i.clientX-c.left,l=i.clientY-c.top,o=!0}return{x:a,y:l,box:o}}function nn(n,t){if("native"in n)return n;let{canvas:e,currentDevicePixelRatio:i}=t,s=xr(e),r=s.boxSizing==="border-box",o=Zn(s,"padding"),a=Zn(s,"border","width"),{x:l,y:c,box:h}=i0(n,e),f=o.left+(h&&a.left),g=o.top+(h&&a.top),{width:p,height:m}=t;return r&&(p-=o.width+a.width,m-=o.height+a.height),{x:Math.round((l-f)/p*e.width/i),y:Math.round((c-g)/m*e.height/i)}}function s0(n,t,e){let i,s;if(t===void 0||e===void 0){let r=ns(n);if(!r)t=n.clientWidth,e=n.clientHeight;else{let o=r.getBoundingClientRect(),a=xr(r),l=Zn(a,"border","width"),c=Zn(a,"padding");t=o.width-c.width-l.width,e=o.height-c.height-l.height,i=yr(a.maxWidth,r,"clientWidth"),s=yr(a.maxHeight,r,"clientHeight")}}return{width:t,height:e,maxWidth:i||Yi,maxHeight:s||Yi}}var va=n=>Math.round(n*10)/10;function _a(n,t,e,i){let s=xr(n),r=Zn(s,"margin"),o=yr(s.maxWidth,n,"clientWidth")||Yi,a=yr(s.maxHeight,n,"clientHeight")||Yi,l=s0(n,t,e),{width:c,height:h}=l;if(s.boxSizing==="content-box"){let f=Zn(s,"border","width"),g=Zn(s,"padding");c-=g.width+f.width,h-=g.height+f.height}return c=Math.max(0,c-r.width),h=Math.max(0,i?Math.floor(c/i):h-r.height),c=va(Math.min(c,o,l.maxWidth)),h=va(Math.min(h,a,l.maxHeight)),c&&!h&&(h=va(c/2)),{width:c,height:h}}function wr(n,t,e){let i=t||1,s=Math.floor(n.height*i),r=Math.floor(n.width*i);n.height=s/i,n.width=r/i;let o=n.canvas;return o.style&&(e||!o.style.height&&!o.style.width)&&(o.style.height=`${n.height}px`,o.style.width=`${n.width}px`),n.currentDevicePixelRatio!==i||o.height!==s||o.width!==r?(n.currentDevicePixelRatio=i,o.height=s,o.width=r,n.ctx.setTransform(i,0,0,i,0,0),!0):!1}var ya=function(){let n=!1;try{let t={get passive(){return n=!0,!1}};window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(t){}return n}();function kr(n,t){let e=kh(n,t),i=e&&e.match(/^(\d+)(\.\d+)?px$/);return i?+i[1]:void 0}function sn(n,t,e,i){return{x:n.x+e*(t.x-n.x),y:n.y+e*(t.y-n.y)}}function xa(n,t,e,i){return{x:n.x+e*(t.x-n.x),y:i==="middle"?e<.5?n.y:t.y:i==="after"?e<1?n.y:t.y:e>0?t.y:n.y}}function wa(n,t,e,i){let s={x:n.cp2x,y:n.cp2y},r={x:t.cp1x,y:t.cp1y},o=sn(n,s,e),a=sn(s,r,e),l=sn(r,t,e),c=sn(o,a,e),h=sn(a,l,e);return sn(c,h,e)}var Sh=new Map;function r0(n,t){t=t||{};let e=n+JSON.stringify(t),i=Sh.get(e);return i||(i=new Intl.NumberFormat(n,t),Sh.set(e,i)),i}function Jn(n,t,e){return r0(t,e).format(n)}var o0=function(n,t){return{x(e){return n+n+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,i){return e-i},leftForLtr(e,i){return e-i}}},a0=function(){return{x(n){return n},setWidth(n){},textAlign(n){return n},xPlus(n,t){return n+t},leftForLtr(n,t){return n}}};function Sn(n,t,e){return n?o0(t,e):a0()}function Sr(n,t){let e,i;(t==="ltr"||t==="rtl")&&(e=n.canvas.style,i=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),n.prevTextDirection=i)}function Mr(n,t){t!==void 0&&(delete n.prevTextDirection,n.canvas.style.setProperty("direction",t[0],t[1]))}function Mh(n){return n==="angle"?{between:Xn,compare:th,normalize:fe}:{between:Ae,compare:(t,e)=>t-e,normalize:t=>t}}function Ch({start:n,end:t,count:e,loop:i,style:s}){return{start:n%e,end:t%e,loop:i&&(t-n+1)%e==0,style:s}}function l0(n,t,e){let{property:i,start:s,end:r}=e,{between:o,normalize:a}=Mh(i),l=t.length,{start:c,end:h,loop:f}=n,g,p;if(f){for(c+=l,h+=l,g=0,p=l;g<p&&o(a(t[c%l][i]),s,r);++g)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:f,style:n.style}}function Cr(n,t,e){if(!e)return[n];let{property:i,start:s,end:r}=e,o=t.length,{compare:a,between:l,normalize:c}=Mh(i),{start:h,end:f,loop:g,style:p}=l0(n,t,e),m=[],y=!1,S=null,M,C,F,D=()=>l(s,F,M)&&a(s,F)!==0,I=()=>a(r,M)===0||l(r,F,M),$=()=>y||D(),N=()=>!y||I();for(let G=h,U=h;G<=f;++G)C=t[G%o],!C.skip&&(M=c(C[i]),M!==F&&(y=l(M,s,r),S===null&&$()&&(S=a(M,s)===0?G:U),S!==null&&N()&&(m.push(Ch({start:S,end:G,loop:g,count:o,style:p})),S=null),U=G,F=M));return S!==null&&m.push(Ch({start:S,end:f,loop:g,count:o,style:p})),m}function Pr(n,t){let e=[],i=n.segments;for(let s=0;s<i.length;s++){let r=Cr(i[s],n.points,t);r.length&&e.push(...r)}return e}function c0(n,t,e,i){let s=0,r=t-1;if(e&&!i)for(;s<t&&!n[s].skip;)s++;for(;s<t&&n[s].skip;)s++;for(s%=t,e&&(r+=s);r>s&&n[r%t].skip;)r--;return r%=t,{start:s,end:r}}function h0(n,t,e,i){let s=n.length,r=[],o=t,a=n[t],l;for(l=t+1;l<=e;++l){let c=n[l%s];c.skip||c.stop?a.skip||(i=!1,r.push({start:t%s,end:(l-1)%s,loop:i}),t=o=c.stop?l:null):(o=l,a.skip&&(t=l)),a=c}return o!==null&&r.push({start:t%s,end:o%s,loop:i}),r}function ka(n,t){let e=n.points,i=n.options.spanGaps,s=e.length;if(!s)return[];let r=!!n._loop,{start:o,end:a}=c0(e,s,r,i);if(i===!0)return Ph(n,[{start:o,end:a,loop:r}],e,t);let l=a<o?a+s:a,c=!!n._fullLoop&&o===0&&a===s-1;return Ph(n,h0(e,o,l,c),e,t)}function Ph(n,t,e,i){return!i||!i.setContext||!e?t:u0(n,t,e,i)}function u0(n,t,e,i){let s=n._chart.getContext(),r=Th(n.options),{_datasetIndex:o,options:{spanGaps:a}}=n,l=e.length,c=[],h=r,f=t[0].start,g=f;function p(m,y,S,M){let C=a?-1:1;if(m!==y){for(m+=l;e[m%l].skip;)m-=C;for(;e[y%l].skip;)y+=C;m%l!=y%l&&(c.push({start:m%l,end:y%l,loop:S,style:M}),h=M,f=y%l)}}for(let m of t){f=a?f:m.start;let y=e[f%l],S;for(g=f+1;g<=m.end;g++){let M=e[g%l];S=Th(i.setContext(Ne(s,{type:"segment",p0:y,p1:M,p0DataIndex:(g-1)%l,p1DataIndex:g%l,datasetIndex:o}))),f0(S,h)&&p(f,g-1,m.loop,h),y=M,h=S}f<g-1&&p(f,g-1,m.loop,h)}return c}function Th(n){return{backgroundColor:n.backgroundColor,borderCapStyle:n.borderCapStyle,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderJoinStyle:n.borderJoinStyle,borderWidth:n.borderWidth,borderColor:n.borderColor}}function f0(n,t){return t&&JSON.stringify(n)!==JSON.stringify(t)}var Dh=class{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){let r=e.listeners[s],o=e.duration;r.forEach(a=>a({chart:t,initial:e.initial,numSteps:o,currentStep:Math.min(i-e.start,o)}))}_refresh(){this._request||(this._running=!0,this._request=sr.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,s)=>{if(!i.running||!i.items.length)return;let r=i.items,o=r.length-1,a=!1,l;for(;o>=0;--o)l=r[o],l._active?(l._total>i.duration&&(i.duration=l._total),l.tick(t),a=!0):(r[o]=r[r.length-1],r.pop());a&&(s.draw(),this._notify(s,i,t,"progress")),r.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=r.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){let e=this._charts,i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){let e=this._charts.get(t);!e||(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((i,s)=>Math.max(i,s._duration),0),this._refresh())}running(t){if(!this._running)return!1;let e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){let e=this._charts.get(t);if(!e||!e.items.length)return;let i=e.items,s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}},rn=new Dh,Eh="transparent",d0={boolean(n,t,e){return e>.5?t:n},color(n,t,e){let i=Je(n||Eh),s=i.valid&&Je(t||Eh);return s&&s.valid?s.mix(i,e).hexString():t},number(n,t,e){return n+(t-n)*e}},Oh=class{constructor(t,e,i,s){let r=e[i];s=Kn([t.to,s,r,t.from]);let o=Kn([t.from,r,s]);this._active=!0,this._fn=t.fn||d0[t.type||typeof o],this._easing=qn[t.easing]||qn.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=o,this._to=s,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);let s=this._target[this._prop],r=i-this._start,o=this._duration-r;this._start=i,this._duration=Math.floor(Math.max(o,t.duration)),this._total+=r,this._loop=!!t.loop,this._to=Kn([t.to,e,s,t.from]),this._from=Kn([t.from,s,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){let e=t-this._start,i=this._duration,s=this._prop,r=this._from,o=this._loop,a=this._to,l;if(this._active=r!==a&&(o||e<i),!this._active){this._target[s]=a,this._notify(!0);return}if(e<0){this._target[s]=r;return}l=e/i%2,l=o&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[s]=this._fn(r,a,l)}wait(){let t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){let e=t?"res":"rej",i=this._promises||[];for(let s=0;s<i.length;s++)i[s][e]()}},p0=["x","y","borderWidth","radius","tension"],g0=["color","borderColor","backgroundColor"];ft.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0});var m0=Object.keys(ft.animation);ft.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:n=>n!=="onProgress"&&n!=="onComplete"&&n!=="fn"});ft.set("animations",{colors:{type:"color",properties:g0},numbers:{type:"number",properties:p0}});ft.describe("animations",{_fallback:"animation"});ft.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:n=>n|0}}}});var is=class{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!at(t))return;let e=this._properties;Object.getOwnPropertyNames(t).forEach(i=>{let s=t[i];if(!at(s))return;let r={};for(let o of m0)r[o]=s[o];(pt(s.properties)&&s.properties||[i]).forEach(o=>{(o===i||!e.has(o))&&e.set(o,r)})})}_animateOptions(t,e){let i=e.options,s=v0(t,i);if(!s)return[];let r=this._createAnimations(s,i);return i.$shared&&b0(t.options.$animations,i).then(()=>{t.options=i},()=>{}),r}_createAnimations(t,e){let i=this._properties,s=[],r=t.$animations||(t.$animations={}),o=Object.keys(e),a=Date.now(),l;for(l=o.length-1;l>=0;--l){let c=o[l];if(c.charAt(0)==="$")continue;if(c==="options"){s.push(...this._animateOptions(t,e));continue}let h=e[c],f=r[c],g=i.get(c);if(f)if(g&&f.active()){f.update(g,h,a);continue}else f.cancel();if(!g||!g.duration){t[c]=h;continue}r[c]=f=new Oh(g,t,c,h),s.push(f)}return s}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}let i=this._createAnimations(t,e);if(i.length)return rn.add(this._chart,i),!0}};function b0(n,t){let e=[],i=Object.keys(t);for(let s=0;s<i.length;s++){let r=n[i[s]];r&&r.active()&&e.push(r.wait())}return Promise.all(e)}function v0(n,t){if(!t)return;let e=n.options;if(!e){n.options=t;return}return e.$shared&&(n.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function Ah(n,t){let e=n&&n.options||{},i=e.reverse,s=e.min===void 0?t:0,r=e.max===void 0?t:0;return{start:i?r:s,end:i?s:r}}function _0(n,t,e){if(e===!1)return!1;let i=Ah(n,e),s=Ah(t,e);return{top:s.end,right:i.end,bottom:s.start,left:i.start}}function y0(n){let t,e,i,s;return at(n)?(t=n.top,e=n.right,i=n.bottom,s=n.left):t=e=i=s=n,{top:t,right:e,bottom:i,left:s,disabled:n===!1}}function Rh(n,t){let e=[],i=n._getSortedDatasetMetas(t),s,r;for(s=0,r=i.length;s<r;++s)e.push(i[s].index);return e}function Lh(n,t,e,i={}){let s=n.keys,r=i.mode==="single",o,a,l,c;if(t!==null){for(o=0,a=s.length;o<a;++o){if(l=+s[o],l===e){if(i.all)continue;break}c=n.values[l],jt(c)&&(r||t===0||Me(t)===Me(c))&&(t+=c)}return t}}function x0(n){let t=Object.keys(n),e=new Array(t.length),i,s,r;for(i=0,s=t.length;i<s;++i)r=t[i],e[i]={x:r,y:n[r]};return e}function Fh(n,t){let e=n&&n.options.stacked;return e||e===void 0&&t.stack!==void 0}function w0(n,t,e){return`${n.id}.${t.id}.${e.stack||e.type}`}function k0(n){let{min:t,max:e,minDefined:i,maxDefined:s}=n.getUserBounds();return{min:i?t:Number.NEGATIVE_INFINITY,max:s?e:Number.POSITIVE_INFINITY}}function S0(n,t,e){let i=n[t]||(n[t]={});return i[e]||(i[e]={})}function Ih(n,t,e,i){for(let s of t.getMatchingVisibleMetas(i).reverse()){let r=n[s.index];if(e&&r>0||!e&&r<0)return s.index}return null}function $h(n,t){let{chart:e,_cachedMeta:i}=n,s=e._stacks||(e._stacks={}),{iScale:r,vScale:o,index:a}=i,l=r.axis,c=o.axis,h=w0(r,o,i),f=t.length,g;for(let p=0;p<f;++p){let m=t[p],{[l]:y,[c]:S}=m,M=m._stacks||(m._stacks={});g=M[c]=S0(s,h,y),g[a]=S,g._top=Ih(g,o,!0,i.type),g._bottom=Ih(g,o,!1,i.type)}}function Sa(n,t){let e=n.scales;return Object.keys(e).filter(i=>e[i].axis===t).shift()}function M0(n,t){return Ne(n,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function C0(n,t,e){return Ne(n,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function ss(n,t){let e=n.controller.index,i=n.vScale&&n.vScale.axis;if(!!i){t=t||n._parsed;for(let s of t){let r=s._stacks;if(!r||r[i]===void 0||r[i][e]===void 0)return;delete r[i][e]}}}var Ma=n=>n==="reset"||n==="none",jh=(n,t)=>t?n:Object.assign({},n),P0=(n,t,e)=>n&&!t.hidden&&t._stacked&&{keys:Rh(e,!0),values:null},ve=class{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.initialize()}initialize(){let t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Fh(t.vScale,t),this.addElements()}updateIndex(t){this.index!==t&&ss(this._cachedMeta),this.index=t}linkScales(){let t=this.chart,e=this._cachedMeta,i=this.getDataset(),s=(f,g,p,m)=>f==="x"?g:f==="r"?m:p,r=e.xAxisID=nt(i.xAxisID,Sa(t,"x")),o=e.yAxisID=nt(i.yAxisID,Sa(t,"y")),a=e.rAxisID=nt(i.rAxisID,Sa(t,"r")),l=e.indexAxis,c=e.iAxisID=s(l,r,o,a),h=e.vAxisID=s(l,o,r,a);e.xScale=this.getScaleForId(r),e.yScale=this.getScaleForId(o),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){let e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){let t=this._cachedMeta;this._data&&nr(this._data,this),t._stacked&&ss(t)}_dataCheck(){let t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(at(e))this._data=x0(e);else if(i!==e){if(i){nr(i,this);let s=this._cachedMeta;ss(s),s._parsed=[]}e&&Object.isExtensible(e)&&ea(e,this),this._syncList=[],this._data=e}}addElements(){let t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){let e=this._cachedMeta,i=this.getDataset(),s=!1;this._dataCheck();let r=e._stacked;e._stacked=Fh(e.vScale,e),e.stack!==i.stack&&(s=!0,ss(e),e.stack=i.stack),this._resyncElements(t),(s||r!==e._stacked)&&$h(this,e._parsed)}configure(){let t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){let{_cachedMeta:i,_data:s}=this,{iScale:r,_stacked:o}=i,a=r.axis,l=t===0&&e===s.length?!0:i._sorted,c=t>0&&i._parsed[t-1],h,f,g;if(this._parsing===!1)i._parsed=s,i._sorted=!0,g=s;else{pt(s[t])?g=this.parseArrayData(i,s,t,e):at(s[t])?g=this.parseObjectData(i,s,t,e):g=this.parsePrimitiveData(i,s,t,e);let p=()=>f[a]===null||c&&f[a]<c[a];for(h=0;h<e;++h)i._parsed[h+t]=f=g[h],l&&(p()&&(l=!1),c=f);i._sorted=l}o&&$h(this,g)}parsePrimitiveData(t,e,i,s){let{iScale:r,vScale:o}=t,a=r.axis,l=o.axis,c=r.getLabels(),h=r===o,f=new Array(s),g,p,m;for(g=0,p=s;g<p;++g)m=g+i,f[g]={[a]:h||r.parse(c[m],m),[l]:o.parse(e[m],m)};return f}parseArrayData(t,e,i,s){let{xScale:r,yScale:o}=t,a=new Array(s),l,c,h,f;for(l=0,c=s;l<c;++l)h=l+i,f=e[h],a[l]={x:r.parse(f[0],h),y:o.parse(f[1],h)};return a}parseObjectData(t,e,i,s){let{xScale:r,yScale:o}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(s),h,f,g,p;for(h=0,f=s;h<f;++h)g=h+i,p=e[g],c[h]={x:r.parse(Be(p,a),g),y:o.parse(Be(p,l),g)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){let s=this.chart,r=this._cachedMeta,o=e[t.axis],a={keys:Rh(s,!0),values:e._stacks[t.axis]};return Lh(a,o,r.index,{mode:i})}updateRangeFromParsed(t,e,i,s){let r=i[e.axis],o=r===null?NaN:r,a=s&&i._stacks[e.axis];s&&a&&(s.values=a,o=Lh(s,r,this._cachedMeta.index)),t.min=Math.min(t.min,o),t.max=Math.max(t.max,o)}getMinMax(t,e){let i=this._cachedMeta,s=i._parsed,r=i._sorted&&t===i.iScale,o=s.length,a=this._getOtherScale(t),l=P0(e,i,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:f}=k0(a),g,p;function m(){p=s[g];let y=p[a.axis];return!jt(p[t.axis])||h>y||f<y}for(g=0;g<o&&!(!m()&&(this.updateRangeFromParsed(c,t,p,l),r));++g);if(r){for(g=o-1;g>=0;--g)if(!m()){this.updateRangeFromParsed(c,t,p,l);break}}return c}getAllParsedValues(t){let e=this._cachedMeta._parsed,i=[],s,r,o;for(s=0,r=e.length;s<r;++s)o=e[s][t.axis],jt(o)&&i.push(o);return i}getMaxOverflow(){return!1}getLabelAndValue(t){let e=this._cachedMeta,i=e.iScale,s=e.vScale,r=this.getParsed(t);return{label:i?""+i.getLabelForValue(r[i.axis]):"",value:s?""+s.getLabelForValue(r[s.axis]):""}}_update(t){let e=this._cachedMeta;this.update(t||"default"),e._clip=y0(nt(this.options.clip,_0(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){let t=this._ctx,e=this.chart,i=this._cachedMeta,s=i.data||[],r=e.chartArea,o=[],a=this._drawStart||0,l=this._drawCount||s.length-a,c=this.options.drawActiveElementsOnTop,h;for(i.dataset&&i.dataset.draw(t,r,a,l),h=a;h<a+l;++h){let f=s[h];f.hidden||(f.active&&c?o.push(f):f.draw(t,r))}for(h=0;h<o.length;++h)o[h].draw(t,r)}getStyle(t,e){let i=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){let s=this.getDataset(),r;if(t>=0&&t<this._cachedMeta.data.length){let o=this._cachedMeta.data[t];r=o.$context||(o.$context=C0(this.getContext(),t,o)),r.parsed=this.getParsed(t),r.raw=s.data[t],r.index=r.dataIndex=t}else r=this.$context||(this.$context=M0(this.chart.getContext(),this.index)),r.dataset=s,r.index=r.datasetIndex=this.index;return r.active=!!e,r.mode=i,r}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){let s=e==="active",r=this._cachedDataOpts,o=t+"-"+e,a=r[o],l=this.enableOptionSharing&&Nt(i);if(a)return jh(a,l);let c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),f=s?[`${t}Hover`,"hover",t,""]:[t,""],g=c.getOptionScopes(this.getDataset(),h),p=Object.keys(ft.elements[t]),m=()=>this.getContext(i,s),y=c.resolveNamedOptions(g,p,m,f);return y.$shared&&(y.$shared=l,r[o]=Object.freeze(jh(y,l))),y}_resolveAnimations(t,e,i){let s=this.chart,r=this._cachedDataOpts,o=`animation-${e}`,a=r[o];if(a)return a;let l;if(s.options.animation!==!1){let h=this.chart.config,f=h.datasetAnimationScopeKeys(this._type,e),g=h.getOptionScopes(this.getDataset(),f);l=h.createResolver(g,this.getContext(t,i,e))}let c=new is(s,l&&l.animations);return l&&l._cacheable&&(r[o]=Object.freeze(c)),c}getSharedOptions(t){if(!!t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||Ma(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){let i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,r=this.getSharedOptions(i),o=this.includeOptions(e,r)||r!==s;return this.updateSharedOptions(r,e,i),{sharedOptions:r,includeOptions:o}}updateElement(t,e,i,s){Ma(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!Ma(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;let r=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(r)||r})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let e=this._data,i=this._cachedMeta.data;for(let[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];let s=i.length,r=e.length,o=Math.min(r,s);o&&this.parse(0,o),r>s?this._insertElements(s,r-s,t):r<s&&this._removeElements(r,s-r)}_insertElements(t,e,i=!0){let s=this._cachedMeta,r=s.data,o=t+e,a,l=c=>{for(c.length+=e,a=c.length-1;a>=o;a--)c[a]=c[a-e]};for(l(r),a=t;a<o;++a)r[a]=new this.dataElementType;this._parsing&&l(s._parsed),this.parse(t,e),i&&this.updateElements(r,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){let i=this._cachedMeta;if(this._parsing){let s=i._parsed.splice(t,e);i._stacked&&ss(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{let[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){let t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);let i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}};ve.defaults={};ve.prototype.datasetElementType=null;ve.prototype.dataElementType=null;function T0(n,t){if(!n._cache.$bar){let e=n.getMatchingVisibleMetas(t),i=[];for(let s=0,r=e.length;s<r;s++)i=i.concat(e[s].controller.getAllParsedValues(n));n._cache.$bar=ir(i.sort((s,r)=>s-r))}return n._cache.$bar}function D0(n){let t=n.iScale,e=T0(t,n.type),i=t._length,s,r,o,a,l=()=>{o===32767||o===-32768||(Nt(a)&&(i=Math.min(i,Math.abs(o-a)||i)),a=o)};for(s=0,r=e.length;s<r;++s)o=t.getPixelForValue(e[s]),l();for(a=void 0,s=0,r=t.ticks.length;s<r;++s)o=t.getPixelForTick(s),l();return i}function E0(n,t,e,i){let s=e.barThickness,r,o;return ut(s)?(r=t.min*e.categoryPercentage,o=e.barPercentage):(r=s*i,o=1),{chunk:r/i,ratio:o,start:t.pixels[n]-r/2}}function O0(n,t,e,i){let s=t.pixels,r=s[n],o=n>0?s[n-1]:null,a=n<s.length-1?s[n+1]:null,l=e.categoryPercentage;o===null&&(o=r-(a===null?t.end-t.start:a-r)),a===null&&(a=r+r-o);let c=r-(r-Math.min(o,a))/2*l;return{chunk:Math.abs(a-o)/2*l/i,ratio:e.barPercentage,start:c}}function A0(n,t,e,i){let s=e.parse(n[0],i),r=e.parse(n[1],i),o=Math.min(s,r),a=Math.max(s,r),l=o,c=a;Math.abs(o)>Math.abs(a)&&(l=a,c=o),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:s,end:r,min:o,max:a}}function zh(n,t,e,i){return pt(n)?A0(n,t,e,i):t[e.axis]=e.parse(n,i),t}function Bh(n,t,e,i){let s=n.iScale,r=n.vScale,o=s.getLabels(),a=s===r,l=[],c,h,f,g;for(c=e,h=e+i;c<h;++c)g=t[c],f={},f[s.axis]=a||s.parse(o[c],c),l.push(zh(g,f,r,c));return l}function Ca(n){return n&&n.barStart!==void 0&&n.barEnd!==void 0}function R0(n,t,e){return n!==0?Me(n):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function L0(n){let t,e,i,s,r;return n.horizontal?(t=n.base>n.x,e="left",i="right"):(t=n.base<n.y,e="bottom",i="top"),t?(s="end",r="start"):(s="start",r="end"),{start:e,end:i,reverse:t,top:s,bottom:r}}function F0(n,t,e,i){let s=t.borderSkipped,r={};if(!s){n.borderSkipped=r;return}if(s===!0){n.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}let{start:o,end:a,reverse:l,top:c,bottom:h}=L0(n);s==="middle"&&e&&(n.enableBorderRadius=!0,(e._top||0)===i?s=c:(e._bottom||0)===i?s=h:(r[Nh(h,o,a,l)]=!0,s=c)),r[Nh(s,o,a,l)]=!0,n.borderSkipped=r}function Nh(n,t,e,i){return i?(n=I0(n,t,e),n=Hh(n,e,t)):n=Hh(n,t,e),n}function I0(n,t,e){return n===t?e:n===e?t:n}function Hh(n,t,e){return n==="start"?t:n==="end"?e:n}function $0(n,{inflateAmount:t},e){n.inflateAmount=t==="auto"?e===1?.33:0:t}var rs=class extends ve{parsePrimitiveData(t,e,i,s){return Bh(t,e,i,s)}parseArrayData(t,e,i,s){return Bh(t,e,i,s)}parseObjectData(t,e,i,s){let{iScale:r,vScale:o}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=r.axis==="x"?a:l,h=o.axis==="x"?a:l,f=[],g,p,m,y;for(g=i,p=i+s;g<p;++g)y=e[g],m={},m[r.axis]=r.parse(Be(y,c),g),f.push(zh(Be(y,h),m,o,g));return f}updateRangeFromParsed(t,e,i,s){super.updateRangeFromParsed(t,e,i,s);let r=i._custom;r&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,r.min),t.max=Math.max(t.max,r.max))}getMaxOverflow(){return 0}getLabelAndValue(t){let e=this._cachedMeta,{iScale:i,vScale:s}=e,r=this.getParsed(t),o=r._custom,a=Ca(o)?"["+o.start+", "+o.end+"]":""+s.getLabelForValue(r[s.axis]);return{label:""+i.getLabelForValue(r[i.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();let t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){let e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,s){let r=s==="reset",{index:o,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),h=this._getRuler(),{sharedOptions:f,includeOptions:g}=this._getSharedOptions(e,s);for(let p=e;p<e+i;p++){let m=this.getParsed(p),y=r||ut(m[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(p),S=this._calculateBarIndexPixels(p,h),M=(m._stacks||{})[a.axis],C={horizontal:c,base:y.base,enableBorderRadius:!M||Ca(m._custom)||o===M._top||o===M._bottom,x:c?y.head:S.center,y:c?S.center:y.head,height:c?S.size:Math.abs(y.size),width:c?Math.abs(y.size):S.size};g&&(C.options=f||this.resolveDataElementOptions(p,t[p].active?"active":s));let F=C.options||t[p].options;F0(C,F,M,o),$0(C,F,h.ratio),this.updateElement(t[p],p,C,s)}}_getStacks(t,e){let{iScale:i}=this._cachedMeta,s=i.getMatchingVisibleMetas(this._type).filter(l=>l.controller.options.grouped),r=i.options.stacked,o=[],a=l=>{let c=l.controller.getParsed(e),h=c&&c[l.vScale.axis];if(ut(h)||isNaN(h))return!0};for(let l of s)if(!(e!==void 0&&a(l))&&((r===!1||o.indexOf(l.stack)===-1||r===void 0&&l.stack===void 0)&&o.push(l.stack),l.index===t))break;return o.length||o.push(void 0),o}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,i){let s=this._getStacks(t,i),r=e!==void 0?s.indexOf(e):-1;return r===-1?s.length-1:r}_getRuler(){let t=this.options,e=this._cachedMeta,i=e.iScale,s=[],r,o;for(r=0,o=e.data.length;r<o;++r)s.push(i.getPixelForValue(this.getParsed(r)[i.axis],r));let a=t.barThickness;return{min:a||D0(e),pixels:s,start:i._startPixel,end:i._endPixel,stackCount:this._getStackCount(),scale:i,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){let{_cachedMeta:{vScale:e,_stacked:i},options:{base:s,minBarLength:r}}=this,o=s||0,a=this.getParsed(t),l=a._custom,c=Ca(l),h=a[e.axis],f=0,g=i?this.applyStack(e,a,i):h,p,m;g!==h&&(f=g-h,g=h),c&&(h=l.barStart,g=l.barEnd-l.barStart,h!==0&&Me(h)!==Me(l.barEnd)&&(f=0),f+=h);let y=!ut(s)&&!c?s:f,S=e.getPixelForValue(y);if(this.chart.getDataVisibility(t)?p=e.getPixelForValue(f+g):p=S,m=p-S,Math.abs(m)<r){m=R0(m,e,o)*r,h===o&&(S-=m/2);let M=e.getPixelForDecimal(0),C=e.getPixelForDecimal(1),F=Math.min(M,C),D=Math.max(M,C);S=Math.max(Math.min(S,D),F),p=S+m}if(S===e.getPixelForValue(o)){let M=Me(m)*e.getLineWidthForValue(o)/2;S+=M,m-=M}return{size:m,base:S,head:p,center:p+m/2}}_calculateBarIndexPixels(t,e){let i=e.scale,s=this.options,r=s.skipNull,o=nt(s.maxBarThickness,1/0),a,l;if(e.grouped){let c=r?this._getStackCount(t):e.stackCount,h=s.barThickness==="flex"?O0(t,e,s,c):E0(t,e,s,c),f=this._getStackIndex(this.index,this._cachedMeta.stack,r?t:void 0);a=h.start+h.chunk*f+h.chunk/2,l=Math.min(o,h.chunk*h.ratio)}else a=i.getPixelForValue(this.getParsed(t)[i.axis],t),l=Math.min(o,e.min*e.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){let t=this._cachedMeta,e=t.vScale,i=t.data,s=i.length,r=0;for(;r<s;++r)this.getParsed(r)[e.axis]!==null&&i[r].draw(this._ctx)}};rs.id="bar";rs.defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};rs.overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};var os=class extends ve{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,s){let r=super.parsePrimitiveData(t,e,i,s);for(let o=0;o<r.length;o++)r[o]._custom=this.resolveDataElementOptions(o+i).radius;return r}parseArrayData(t,e,i,s){let r=super.parseArrayData(t,e,i,s);for(let o=0;o<r.length;o++){let a=e[i+o];r[o]._custom=nt(a[2],this.resolveDataElementOptions(o+i).radius)}return r}parseObjectData(t,e,i,s){let r=super.parseObjectData(t,e,i,s);for(let o=0;o<r.length;o++){let a=e[i+o];r[o]._custom=nt(a&&a.r&&+a.r,this.resolveDataElementOptions(o+i).radius)}return r}getMaxOverflow(){let t=this._cachedMeta.data,e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){let e=this._cachedMeta,{xScale:i,yScale:s}=e,r=this.getParsed(t),o=i.getLabelForValue(r.x),a=s.getLabelForValue(r.y),l=r._custom;return{label:e.label,value:"("+o+", "+a+(l?", "+l:"")+")"}}update(t){let e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,s){let r=s==="reset",{iScale:o,vScale:a}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(e,s),h=o.axis,f=a.axis;for(let g=e;g<e+i;g++){let p=t[g],m=!r&&this.getParsed(g),y={},S=y[h]=r?o.getPixelForDecimal(.5):o.getPixelForValue(m[h]),M=y[f]=r?a.getBasePixel():a.getPixelForValue(m[f]);y.skip=isNaN(S)||isNaN(M),c&&(y.options=l||this.resolveDataElementOptions(g,p.active?"active":s),r&&(y.options.radius=0)),this.updateElement(p,g,y,s)}}resolveDataElementOptions(t,e){let i=this.getParsed(t),s=super.resolveDataElementOptions(t,e);s.$shared&&(s=Object.assign({},s,{$shared:!1}));let r=s.radius;return e!=="active"&&(s.radius=0),s.radius+=nt(i&&i._custom,r),s}};os.id="bubble";os.defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};os.overrides={scales:{x:{type:"linear"},y:{type:"linear"}},plugins:{tooltip:{callbacks:{title(){return""}}}}};function j0(n,t,e){let i=1,s=1,r=0,o=0;if(t<kt){let a=n,l=a+t,c=Math.cos(a),h=Math.sin(a),f=Math.cos(l),g=Math.sin(l),p=(F,D,I)=>Xn(F,a,l,!0)?1:Math.max(D,D*e,I,I*e),m=(F,D,I)=>Xn(F,a,l,!0)?-1:Math.min(D,D*e,I,I*e),y=p(0,c,f),S=p(Rt,h,g),M=m(gt,c,f),C=m(gt+Rt,h,g);i=(y-M)/2,s=(S-C)/2,r=-(y+M)/2,o=-(S+C)/2}return{ratioX:i,ratioY:s,offsetX:r,offsetY:o}}var Qn=class extends ve{constructor(t,e){super(t,e);this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){let i=this.getDataset().data,s=this._cachedMeta;if(this._parsing===!1)s._parsed=i;else{let r=l=>+i[l];if(at(i[t])){let{key:l="value"}=this._parsing;r=c=>+Be(i[c],l)}let o,a;for(o=t,a=t+e;o<a;++o)s._parsed[o]=r(o)}}_getRotation(){return Wt(this.options.rotation-90)}_getCircumference(){return Wt(this.options.circumference)}_getRotationExtents(){let t=kt,e=-kt;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)){let s=this.chart.getDatasetMeta(i).controller,r=s._getRotation(),o=s._getCircumference();t=Math.min(t,r),e=Math.max(e,r+o)}return{rotation:t,circumference:e-t}}update(t){let e=this.chart,{chartArea:i}=e,s=this._cachedMeta,r=s.data,o=this.getMaxBorderWidth()+this.getMaxOffset(r)+this.options.spacing,a=Math.max((Math.min(i.width,i.height)-o)/2,0),l=Math.min(Go(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:h,rotation:f}=this._getRotationExtents(),{ratioX:g,ratioY:p,offsetX:m,offsetY:y}=j0(f,h,l),S=(i.width-o)/g,M=(i.height-o)/p,C=Math.max(Math.min(S,M)/2,0),F=Ks(this.options.radius,C),D=Math.max(F*l,0),I=(F-D)/this._getVisibleDatasetWeightTotal();this.offsetX=m*F,this.offsetY=y*F,s.total=this.calculateTotal(),this.outerRadius=F-I*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-I*c,0),this.updateElements(r,0,r.length,t)}_circumference(t,e){let i=this.options,s=this._cachedMeta,r=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||s._parsed[t]===null||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*r/kt)}updateElements(t,e,i,s){let r=s==="reset",o=this.chart,a=o.chartArea,c=o.options.animation,h=(a.left+a.right)/2,f=(a.top+a.bottom)/2,g=r&&c.animateScale,p=g?0:this.innerRadius,m=g?0:this.outerRadius,{sharedOptions:y,includeOptions:S}=this._getSharedOptions(e,s),M=this._getRotation(),C;for(C=0;C<e;++C)M+=this._circumference(C,r);for(C=e;C<e+i;++C){let F=this._circumference(C,r),D=t[C],I={x:h+this.offsetX,y:f+this.offsetY,startAngle:M,endAngle:M+F,circumference:F,outerRadius:m,innerRadius:p};S&&(I.options=y||this.resolveDataElementOptions(C,D.active?"active":s)),M+=F,this.updateElement(D,C,I,s)}}calculateTotal(){let t=this._cachedMeta,e=t.data,i=0,s;for(s=0;s<e.length;s++){let r=t._parsed[s];r!==null&&!isNaN(r)&&this.chart.getDataVisibility(s)&&!e[s].hidden&&(i+=Math.abs(r))}return i}calculateCircumference(t){let e=this._cachedMeta.total;return e>0&&!isNaN(t)?kt*(Math.abs(t)/e):0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],r=Jn(e._parsed[t],i.options.locale);return{label:s[t]||"",value:r}}getMaxBorderWidth(t){let e=0,i=this.chart,s,r,o,a,l;if(!t){for(s=0,r=i.data.datasets.length;s<r;++s)if(i.isDatasetVisible(s)){o=i.getDatasetMeta(s),t=o.data,a=o.controller;break}}if(!t)return 0;for(s=0,r=t.length;s<r;++s)l=a.resolveDataElementOptions(s),l.borderAlign!=="inner"&&(e=Math.max(e,l.borderWidth||0,l.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let i=0,s=t.length;i<s;++i){let r=this.resolveDataElementOptions(i);e=Math.max(e,r.offset||0,r.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(nt(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}};Qn.id="doughnut";Qn.defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};Qn.descriptors={_scriptable:n=>n!=="spacing",_indexable:n=>n!=="spacing"};Qn.overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(n){let t=n.data;if(t.labels.length&&t.datasets.length){let{labels:{pointStyle:e}}=n.legend.options;return t.labels.map((i,s)=>{let o=n.getDatasetMeta(0).controller.getStyle(s);return{text:i,fillStyle:o.backgroundColor,strokeStyle:o.borderColor,lineWidth:o.borderWidth,pointStyle:e,hidden:!n.getDataVisibility(s),index:s}})}return[]}},onClick(n,t,e){e.chart.toggleDataVisibility(t.index),e.chart.update()}},tooltip:{callbacks:{title(){return""},label(n){let t=n.label,e=": "+n.formattedValue;return pt(t)?(t=t.slice(),t[0]+=e):t+=e,t}}}}};var as=class extends ve{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){let e=this._cachedMeta,{dataset:i,data:s=[],_dataset:r}=e,o=this.chart._animationsDisabled,{start:a,count:l}=or(e,s,o);this._drawStart=a,this._drawCount=l,ar(e)&&(a=0,l=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!r._decimated,i.points=s;let c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(i,void 0,{animated:!o,options:c},t),this.updateElements(s,a,l,t)}updateElements(t,e,i,s){let r=s==="reset",{iScale:o,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:f}=this._getSharedOptions(e,s),g=o.axis,p=a.axis,{spanGaps:m,segment:y}=this.options,S=Oe(m)?m:Number.POSITIVE_INFINITY,M=this.chart._animationsDisabled||r||s==="none",C=e>0&&this.getParsed(e-1);for(let F=e;F<e+i;++F){let D=t[F],I=this.getParsed(F),$=M?D:{},N=ut(I[p]),G=$[g]=o.getPixelForValue(I[g],F),U=$[p]=r||N?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,I,l):I[p],F);$.skip=isNaN(G)||isNaN(U)||N,$.stop=F>0&&Math.abs(I[g]-C[g])>S,y&&($.parsed=I,$.raw=c.data[F]),f&&($.options=h||this.resolveDataElementOptions(F,D.active?"active":s)),M||this.updateElement(D,F,$,s),C=I}}getMaxOverflow(){let t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];if(!s.length)return i;let r=s[0].size(this.resolveDataElementOptions(0)),o=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(i,r,o)/2}draw(){let t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}};as.id="line";as.defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};as.overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};var ls=class extends ve{constructor(t,e){super(t,e);this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],r=Jn(e._parsed[t].r,i.options.locale);return{label:s[t]||"",value:r}}parseObjectData(t,e,i,s){return br.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){let t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((i,s)=>{let r=this.getParsed(s).r;!isNaN(r)&&this.chart.getDataVisibility(s)&&(r<e.min&&(e.min=r),r>e.max&&(e.max=r))}),e}_updateRadius(){let t=this.chart,e=t.chartArea,i=t.options,s=Math.min(e.right-e.left,e.bottom-e.top),r=Math.max(s/2,0),o=Math.max(i.cutoutPercentage?r/100*i.cutoutPercentage:1,0),a=(r-o)/t.getVisibleDatasetCount();this.outerRadius=r-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(t,e,i,s){let r=s==="reset",o=this.chart,l=o.options.animation,c=this._cachedMeta.rScale,h=c.xCenter,f=c.yCenter,g=c.getIndexAngle(0)-.5*gt,p=g,m,y=360/this.countVisibleElements();for(m=0;m<e;++m)p+=this._computeAngle(m,s,y);for(m=e;m<e+i;m++){let S=t[m],M=p,C=p+this._computeAngle(m,s,y),F=o.getDataVisibility(m)?c.getDistanceFromCenterForValue(this.getParsed(m).r):0;p=C,r&&(l.animateScale&&(F=0),l.animateRotate&&(M=C=g));let D={x:h,y:f,innerRadius:0,outerRadius:F,startAngle:M,endAngle:C,options:this.resolveDataElementOptions(m,S.active?"active":s)};this.updateElement(S,m,D,s)}}countVisibleElements(){let t=this._cachedMeta,e=0;return t.data.forEach((i,s)=>{!isNaN(this.getParsed(s).r)&&this.chart.getDataVisibility(s)&&e++}),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?Wt(this.resolveDataElementOptions(t,e).angle||i):0}};ls.id="polarArea";ls.defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};ls.overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(n){let t=n.data;if(t.labels.length&&t.datasets.length){let{labels:{pointStyle:e}}=n.legend.options;return t.labels.map((i,s)=>{let o=n.getDatasetMeta(0).controller.getStyle(s);return{text:i,fillStyle:o.backgroundColor,strokeStyle:o.borderColor,lineWidth:o.borderWidth,pointStyle:e,hidden:!n.getDataVisibility(s),index:s}})}return[]}},onClick(n,t,e){e.chart.toggleDataVisibility(t.index),e.chart.update()}},tooltip:{callbacks:{title(){return""},label(n){return n.chart.data.labels[n.dataIndex]+": "+n.formattedValue}}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};var Tr=class extends Qn{};Tr.id="pie";Tr.defaults={cutout:0,rotation:0,circumference:360,radius:"100%"};var cs=class extends ve{getLabelAndValue(t){let e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,s){return br.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta,i=e.dataset,s=e.data||[],r=e.iScale.getLabels();if(i.points=s,t!=="resize"){let o=this.resolveDatasetElementOptions(t);this.options.showLine||(o.borderWidth=0);let a={_loop:!0,_fullLoop:r.length===s.length,options:o};this.updateElement(i,void 0,a,t)}this.updateElements(s,0,s.length,t)}updateElements(t,e,i,s){let r=this._cachedMeta.rScale,o=s==="reset";for(let a=e;a<e+i;a++){let l=t[a],c=this.resolveDataElementOptions(a,l.active?"active":s),h=r.getPointPositionForValue(a,this.getParsed(a).r),f=o?r.xCenter:h.x,g=o?r.yCenter:h.y,p={x:f,y:g,angle:h.angle,skip:isNaN(f)||isNaN(g),options:c};this.updateElement(l,a,p,s)}}};cs.id="radar";cs.defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};cs.overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};var qt=class{constructor(){this.x=void 0,this.y=void 0,this.active=!1,this.options=void 0,this.$animations=void 0}tooltipPosition(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return Oe(this.x)&&Oe(this.y)}getProps(t,e){let i=this.$animations;if(!e||!i)return this;let s={};return t.forEach(r=>{s[r]=i[r]&&i[r].active()?i[r]._to:this[r]}),s}};qt.defaults={};qt.defaultRoutes=void 0;var Wh={values(n){return pt(n)?n:""+n},numeric(n,t,e){if(n===0)return"0";let i=this.chart.options.locale,s,r=n;if(e.length>1){let c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(s="scientific"),r=z0(n,e)}let o=be(Math.abs(r)),a=Math.max(Math.min(-1*Math.floor(o),20),0),l={notation:s,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),Jn(n,i,l)},logarithmic(n,t,e){if(n===0)return"0";let i=n/Math.pow(10,Math.floor(be(n)));return i===1||i===2||i===5?Wh.numeric.call(this,n,t,e):""}};function z0(n,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&n!==Math.floor(n)&&(e=n-Math.floor(n)),e}var Dr={formatters:Wh};ft.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",grace:0,grid:{display:!0,lineWidth:1,drawBorder:!0,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(n,t)=>t.lineWidth,tickColor:(n,t)=>t.color,offset:!1,borderDash:[],borderDashOffset:0,borderWidth:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Dr.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}});ft.route("scale.ticks","color","","color");ft.route("scale.grid","color","","borderColor");ft.route("scale.grid","borderColor","","borderColor");ft.route("scale.title","color","","color");ft.describe("scale",{_fallback:!1,_scriptable:n=>!n.startsWith("before")&&!n.startsWith("after")&&n!=="callback"&&n!=="parser",_indexable:n=>n!=="borderDash"&&n!=="tickBorderDash"});ft.describe("scales",{_fallback:"scale"});ft.describe("scale.ticks",{_scriptable:n=>n!=="backdropPadding"&&n!=="callback",_indexable:n=>n!=="backdropPadding"});function B0(n,t){let e=n.options.ticks,i=e.maxTicksLimit||N0(n),s=e.major.enabled?W0(t):[],r=s.length,o=s[0],a=s[r-1],l=[];if(r>i)return V0(t,l,s,r/i),l;let c=H0(s,t,i);if(r>0){let h,f,g=r>1?Math.round((a-o)/(r-1)):null;for(Er(t,l,c,ut(g)?0:o-g,o),h=0,f=r-1;h<f;h++)Er(t,l,c,s[h],s[h+1]);return Er(t,l,c,a,ut(g)?t.length:a+g),l}return Er(t,l,c),l}function N0(n){let t=n.options.offset,e=n._tickSize(),i=n._length/e+(t?0:1),s=n._maxLength/e;return Math.floor(Math.min(i,s))}function H0(n,t,e){let i=Y0(n),s=t.length/e;if(!i)return Math.max(s,1);let r=Ko(i);for(let o=0,a=r.length-1;o<a;o++){let l=r[o];if(l>s)return l}return Math.max(s,1)}function W0(n){let t=[],e,i;for(e=0,i=n.length;e<i;e++)n[e].major&&t.push(e);return t}function V0(n,t,e,i){let s=0,r=e[0],o;for(i=Math.ceil(i),o=0;o<n.length;o++)o===r&&(t.push(n[o]),s++,r=e[s*i])}function Er(n,t,e,i,s){let r=nt(i,0),o=Math.min(nt(s,n.length),n.length),a=0,l,c,h;for(e=Math.ceil(e),s&&(l=s-i,e=l/Math.floor(l/e)),h=r;h<0;)a++,h=Math.round(r+a*e);for(c=Math.max(r,0);c<o;c++)c===h&&(t.push(n[c]),a++,h=Math.round(r+a*e))}function Y0(n){let t=n.length,e,i;if(t<2)return!1;for(i=n[0],e=1;e<t;++e)if(n[e]-n[e-1]!==i)return!1;return i}var X0=n=>n==="left"?"right":n==="right"?"left":n,Vh=(n,t,e)=>t==="top"||t==="left"?n[t]+e:n[t]-e;function Yh(n,t){let e=[],i=n.length/t,s=n.length,r=0;for(;r<s;r+=i)e.push(n[Math.floor(r)]);return e}function q0(n,t,e){let i=n.ticks.length,s=Math.min(t,i-1),r=n._startPixel,o=n._endPixel,a=1e-6,l=n.getPixelForTick(s),c;if(!(e&&(i===1?c=Math.max(l-r,o-l):t===0?c=(n.getPixelForTick(1)-l)/2:c=(l-n.getPixelForTick(s-1))/2,l+=s<t?c:-c,l<r-a||l>o+a)))return l}function G0(n,t){Ct(n,e=>{let i=e.gc,s=i.length/2,r;if(s>t){for(r=0;r<s;++r)delete e.data[i[r]];i.splice(0,s)}})}function hs(n){return n.drawTicks?n.tickLength:0}function Xh(n,t){if(!n.display)return 0;let e=Ft(n.font,t),i=Vt(n.padding);return(pt(n.text)?n.text.length:1)*e.lineHeight+i.height}function U0(n,t){return Ne(n,{scale:t,type:"scale"})}function K0(n,t,e){return Ne(n,{tick:e,index:t,type:"tick"})}function Z0(n,t,e){let i=Gi(n);return(e&&t!=="right"||!e&&t==="right")&&(i=X0(i)),i}function J0(n,t,e,i){let{top:s,left:r,bottom:o,right:a,chart:l}=n,{chartArea:c,scales:h}=l,f=0,g,p,m,y=o-s,S=a-r;if(n.isHorizontal()){if(p=ie(i,r,a),at(e)){let M=Object.keys(e)[0],C=e[M];m=h[M].getPixelForValue(C)+y-t}else e==="center"?m=(c.bottom+c.top)/2+y-t:m=Vh(n,e,t);g=a-r}else{if(at(e)){let M=Object.keys(e)[0],C=e[M];p=h[M].getPixelForValue(C)-S+t}else e==="center"?p=(c.left+c.right)/2-S+t:p=Vh(n,e,t);m=ie(i,o,s),f=e==="left"?-Rt:Rt}return{titleX:p,titleY:m,maxWidth:g,rotation:f}}var Mn=class extends qt{constructor(t){super();this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:s}=this;return t=me(t,Number.POSITIVE_INFINITY),e=me(e,Number.NEGATIVE_INFINITY),i=me(i,Number.POSITIVE_INFINITY),s=me(s,Number.NEGATIVE_INFINITY),{min:me(t,i),max:me(e,s),minDefined:jt(t),maxDefined:jt(e)}}getMinMax(t){let{min:e,max:i,minDefined:s,maxDefined:r}=this.getUserBounds(),o;if(s&&r)return{min:e,max:i};let a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)o=a[l].controller.getMinMax(this,t),s||(e=Math.min(e,o.min)),r||(i=Math.max(i,o.max));return e=r&&e>i?i:e,i=s&&e>i?e:i,{min:me(e,me(i,e)),max:me(i,me(e,i))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){wt(this.options.beforeUpdate,[this])}update(t,e,i){let{beginAtZero:s,grace:r,ticks:o}=this.options,a=o.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=pa(this,r,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let l=a<this.ticks.length;this._convertTicksToLabels(l?Yh(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),o.display&&(o.autoSkip||o.source==="auto")&&(this.ticks=B0(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,i;this.isHorizontal()?(e=this.left,i=this.right):(e=this.top,i=this.bottom,t=!t),this._startPixel=e,this._endPixel=i,this._reversePixels=t,this._length=i-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){wt(this.options.afterUpdate,[this])}beforeSetDimensions(){wt(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){wt(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),wt(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){wt(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){let e=this.options.ticks,i,s,r;for(i=0,s=t.length;i<s;i++)r=t[i],r.label=wt(e.callback,[r.value,i,t],this)}afterTickToLabelConversion(){wt(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){wt(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let t=this.options,e=t.ticks,i=this.ticks.length,s=e.minRotation||0,r=e.maxRotation,o=s,a,l,c;if(!this._isVisible()||!e.display||s>=r||i<=1||!this.isHorizontal()){this.labelRotation=s;return}let h=this._getLabelSizes(),f=h.widest.width,g=h.highest.height,p=Zt(this.chart.width-f,0,this.maxWidth);a=t.offset?this.maxWidth/i:p/(i-1),f+6>a&&(a=p/(i-(t.offset?.5:1)),l=this.maxHeight-hs(t.grid)-e.padding-Xh(t.title,this.chart.options.font),c=Math.sqrt(f*f+g*g),o=Yn(Math.min(Math.asin(Zt((h.highest.height+6)/a,-1,1)),Math.asin(Zt(l/c,-1,1))-Math.asin(Zt(g/c,-1,1)))),o=Math.max(s,Math.min(r,o))),this.labelRotation=o}afterCalculateLabelRotation(){wt(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){wt(this.options.beforeFit,[this])}fit(){let t={width:0,height:0},{chart:e,options:{ticks:i,title:s,grid:r}}=this,o=this._isVisible(),a=this.isHorizontal();if(o){let l=Xh(s,e.options.font);if(a?(t.width=this.maxWidth,t.height=hs(r)+l):(t.height=this.maxHeight,t.width=hs(r)+l),i.display&&this.ticks.length){let{first:c,last:h,widest:f,highest:g}=this._getLabelSizes(),p=i.padding*2,m=Wt(this.labelRotation),y=Math.cos(m),S=Math.sin(m);if(a){let M=i.mirror?0:S*f.width+y*g.height;t.height=Math.min(this.maxHeight,t.height+M+p)}else{let M=i.mirror?0:y*f.width+S*g.height;t.width=Math.min(this.maxWidth,t.width+M+p)}this._calculatePadding(c,h,S,y)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){let{ticks:{align:r,padding:o},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){let h=this.getPixelForTick(0)-this.left,f=this.right-this.getPixelForTick(this.ticks.length-1),g=0,p=0;l?c?(g=s*t.width,p=i*e.height):(g=i*t.height,p=s*e.width):r==="start"?p=e.width:r==="end"?g=t.width:r!=="inner"&&(g=t.width/2,p=e.width/2),this.paddingLeft=Math.max((g-h+o)*this.width/(this.width-h),0),this.paddingRight=Math.max((p-f+o)*this.width/(this.width-f),0)}else{let h=e.height/2,f=t.height/2;r==="start"?(h=0,f=t.height):r==="end"&&(h=e.height,f=0),this.paddingTop=h+o,this.paddingBottom=f+o}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){wt(this.options.afterFit,[this])}isHorizontal(){let{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,i;for(e=0,i=t.length;e<i;e++)ut(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){let e=this.options.ticks.sampleSize,i=this.ticks;e<i.length&&(i=Yh(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length)}return t}_computeLabelSizes(t,e){let{ctx:i,_longestTextCache:s}=this,r=[],o=[],a=0,l=0,c,h,f,g,p,m,y,S,M,C,F;for(c=0;c<e;++c){if(g=t[c].label,p=this._resolveTickFontOptions(c),i.font=m=p.string,y=s[m]=s[m]||{data:{},gc:[]},S=p.lineHeight,M=C=0,!ut(g)&&!pt(g))M=ki(i,y.data,y.gc,M,g),C=S;else if(pt(g))for(h=0,f=g.length;h<f;++h)F=g[h],!ut(F)&&!pt(F)&&(M=ki(i,y.data,y.gc,M,F),C+=S);r.push(M),o.push(C),a=Math.max(M,a),l=Math.max(C,l)}G0(s,e);let D=r.indexOf(a),I=o.indexOf(l),$=N=>({width:r[N]||0,height:o[N]||0});return{first:$(0),last:$(e-1),widest:$(D),highest:$(I),widths:r,heights:o}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);let e=this._startPixel+t*this._length;return Jo(this._alignToPixels?Qe(this.chart,e,0):e)}getDecimalForPixel(t){let e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){let e=this.ticks||[];if(t>=0&&t<e.length){let i=e[t];return i.$context||(i.$context=K0(this.getContext(),t,i))}return this.$context||(this.$context=U0(this.chart.getContext(),this))}_tickSize(){let t=this.options.ticks,e=Wt(this.labelRotation),i=Math.abs(Math.cos(e)),s=Math.abs(Math.sin(e)),r=this._getLabelSizes(),o=t.autoSkipPadding||0,a=r?r.widest.width+o:0,l=r?r.highest.height+o:0;return this.isHorizontal()?l*i>a*s?a/i:l/s:l*s<a*i?l/i:a/s}_isVisible(){let t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){let e=this.axis,i=this.chart,s=this.options,{grid:r,position:o}=s,a=r.offset,l=this.isHorizontal(),h=this.ticks.length+(a?1:0),f=hs(r),g=[],p=r.setContext(this.getContext()),m=p.drawBorder?p.borderWidth:0,y=m/2,S=function(et){return Qe(i,et,m)},M,C,F,D,I,$,N,G,U,it,lt,rt;if(o==="top")M=S(this.bottom),$=this.bottom-f,G=M-y,it=S(t.top)+y,rt=t.bottom;else if(o==="bottom")M=S(this.top),it=t.top,rt=S(t.bottom)-y,$=M+y,G=this.top+f;else if(o==="left")M=S(this.right),I=this.right-f,N=M-y,U=S(t.left)+y,lt=t.right;else if(o==="right")M=S(this.left),U=t.left,lt=S(t.right)-y,I=M+y,N=this.left+f;else if(e==="x"){if(o==="center")M=S((t.top+t.bottom)/2+.5);else if(at(o)){let et=Object.keys(o)[0],Et=o[et];M=S(this.chart.scales[et].getPixelForValue(Et))}it=t.top,rt=t.bottom,$=M+y,G=$+f}else if(e==="y"){if(o==="center")M=S((t.left+t.right)/2);else if(at(o)){let et=Object.keys(o)[0],Et=o[et];M=S(this.chart.scales[et].getPixelForValue(Et))}I=M-y,N=I-f,U=t.left,lt=t.right}let Pt=nt(s.ticks.maxTicksLimit,h),zt=Math.max(1,Math.ceil(h/Pt));for(C=0;C<h;C+=zt){let et=r.setContext(this.getContext(C)),Et=et.lineWidth,St=et.color,re=et.borderDash||[],ye=et.borderDashOffset,ot=et.tickWidth,Lt=et.tickColor,Bt=et.tickBorderDash||[],Ut=et.tickBorderDashOffset;F=q0(this,C,a),F!==void 0&&(D=Qe(i,F,Et),l?I=N=U=lt=D:$=G=it=rt=D,g.push({tx1:I,ty1:$,tx2:N,ty2:G,x1:U,y1:it,x2:lt,y2:rt,width:Et,color:St,borderDash:re,borderDashOffset:ye,tickWidth:ot,tickColor:Lt,tickBorderDash:Bt,tickBorderDashOffset:Ut}))}return this._ticksLength=h,this._borderValue=M,g}_computeLabelItems(t){let e=this.axis,i=this.options,{position:s,ticks:r}=i,o=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:f}=r,g=hs(i.grid),p=g+h,m=f?-h:p,y=-Wt(this.labelRotation),S=[],M,C,F,D,I,$,N,G,U,it,lt,rt,Pt="middle";if(s==="top")$=this.bottom-m,N=this._getXAxisLabelAlignment();else if(s==="bottom")$=this.top+m,N=this._getXAxisLabelAlignment();else if(s==="left"){let et=this._getYAxisLabelAlignment(g);N=et.textAlign,I=et.x}else if(s==="right"){let et=this._getYAxisLabelAlignment(g);N=et.textAlign,I=et.x}else if(e==="x"){if(s==="center")$=(t.top+t.bottom)/2+p;else if(at(s)){let et=Object.keys(s)[0],Et=s[et];$=this.chart.scales[et].getPixelForValue(Et)+p}N=this._getXAxisLabelAlignment()}else if(e==="y"){if(s==="center")I=(t.left+t.right)/2-p;else if(at(s)){let et=Object.keys(s)[0],Et=s[et];I=this.chart.scales[et].getPixelForValue(Et)}N=this._getYAxisLabelAlignment(g).textAlign}e==="y"&&(l==="start"?Pt="top":l==="end"&&(Pt="bottom"));let zt=this._getLabelSizes();for(M=0,C=a.length;M<C;++M){F=a[M],D=F.label;let et=r.setContext(this.getContext(M));G=this.getPixelForTick(M)+r.labelOffset,U=this._resolveTickFontOptions(M),it=U.lineHeight,lt=pt(D)?D.length:1;let Et=lt/2,St=et.color,re=et.textStrokeColor,ye=et.textStrokeWidth,ot=N;o?(I=G,N==="inner"&&(M===C-1?ot=this.options.reverse?"left":"right":M===0?ot=this.options.reverse?"right":"left":ot="center"),s==="top"?c==="near"||y!==0?rt=-lt*it+it/2:c==="center"?rt=-zt.highest.height/2-Et*it+it:rt=-zt.highest.height+it/2:c==="near"||y!==0?rt=it/2:c==="center"?rt=zt.highest.height/2-Et*it:rt=zt.highest.height-lt*it,f&&(rt*=-1)):($=G,rt=(1-lt)*it/2);let Lt;if(et.showLabelBackdrop){let Bt=Vt(et.backdropPadding),Ut=zt.heights[M],Qt=zt.widths[M],k=$+rt-Bt.top,v=I-Bt.left;switch(Pt){case"middle":k-=Ut/2;break;case"bottom":k-=Ut;break}switch(N){case"center":v-=Qt/2;break;case"right":v-=Qt;break}Lt={left:v,top:k,width:Qt+Bt.width,height:Ut+Bt.height,color:et.backdropColor}}S.push({rotation:y,label:D,font:U,color:St,strokeColor:re,strokeWidth:ye,textOffset:rt,textAlign:ot,textBaseline:Pt,translation:[I,$],backdrop:Lt})}return S}_getXAxisLabelAlignment(){let{position:t,ticks:e}=this.options;if(-Wt(this.labelRotation))return t==="top"?"left":"right";let s="center";return e.align==="start"?s="left":e.align==="end"?s="right":e.align==="inner"&&(s="inner"),s}_getYAxisLabelAlignment(t){let{position:e,ticks:{crossAlign:i,mirror:s,padding:r}}=this.options,o=this._getLabelSizes(),a=t+r,l=o.widest.width,c,h;return e==="left"?s?(h=this.right+r,i==="near"?c="left":i==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,i==="near"?c="right":i==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?s?(h=this.left+r,i==="near"?c="right":i==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,i==="near"?c="left":i==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;let t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){let{ctx:t,options:{backgroundColor:e},left:i,top:s,width:r,height:o}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,r,o),t.restore())}getLineWidthForValue(t){let e=this.options.grid;if(!this._isVisible()||!e.display)return 0;let s=this.ticks.findIndex(r=>r.value===t);return s>=0?e.setContext(this.getContext(s)).lineWidth:0}drawGrid(t){let e=this.options.grid,i=this.ctx,s=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t)),r,o,a=(l,c,h)=>{!h.width||!h.color||(i.save(),i.lineWidth=h.width,i.strokeStyle=h.color,i.setLineDash(h.borderDash||[]),i.lineDashOffset=h.borderDashOffset,i.beginPath(),i.moveTo(l.x,l.y),i.lineTo(c.x,c.y),i.stroke(),i.restore())};if(e.display)for(r=0,o=s.length;r<o;++r){let l=s[r];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){let{chart:t,ctx:e,options:{grid:i}}=this,s=i.setContext(this.getContext()),r=i.drawBorder?s.borderWidth:0;if(!r)return;let o=i.setContext(this.getContext(0)).lineWidth,a=this._borderValue,l,c,h,f;this.isHorizontal()?(l=Qe(t,this.left,r)-r/2,c=Qe(t,this.right,o)+o/2,h=f=a):(h=Qe(t,this.top,r)-r/2,f=Qe(t,this.bottom,o)+o/2,l=c=a),e.save(),e.lineWidth=s.borderWidth,e.strokeStyle=s.borderColor,e.beginPath(),e.moveTo(l,h),e.lineTo(c,f),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;let i=this.ctx,s=this._computeLabelArea();s&&xn(i,s);let r=this._labelItems||(this._labelItems=this._computeLabelItems(t)),o,a;for(o=0,a=r.length;o<a;++o){let l=r[o],c=l.font,h=l.label;l.backdrop&&(i.fillStyle=l.backdrop.color,i.fillRect(l.backdrop.left,l.backdrop.top,l.backdrop.width,l.backdrop.height));let f=l.textOffset;tn(i,h,0,f,c,l)}s&&wn(i)}drawTitle(){let{ctx:t,options:{position:e,title:i,reverse:s}}=this;if(!i.display)return;let r=Ft(i.font),o=Vt(i.padding),a=i.align,l=r.lineHeight/2;e==="bottom"||e==="center"||at(e)?(l+=o.bottom,pt(i.text)&&(l+=r.lineHeight*(i.text.length-1))):l+=o.top;let{titleX:c,titleY:h,maxWidth:f,rotation:g}=J0(this,l,e,a);tn(t,i.text,0,0,r,{color:i.color,maxWidth:f,rotation:g,textAlign:Z0(a,e,s),textBaseline:"middle",translation:[c,h]})}draw(t){!this._isVisible()||(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){let t=this.options,e=t.ticks&&t.ticks.z||0,i=nt(t.grid&&t.grid.z,-1);return!this._isVisible()||this.draw!==Mn.prototype.draw?[{z:e,draw:s=>{this.draw(s)}}]:[{z:i,draw:s=>{this.drawBackground(),this.drawGrid(s),this.drawTitle()}},{z:i+1,draw:()=>{this.drawBorder()}},{z:e,draw:s=>{this.drawLabels(s)}}]}getMatchingVisibleMetas(t){let e=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",s=[],r,o;for(r=0,o=e.length;r<o;++r){let a=e[r];a[i]===this.id&&(!t||a.type===t)&&s.push(a)}return s}_resolveTickFontOptions(t){let e=this.options.ticks.setContext(this.getContext(t));return Ft(e.font)}_maxDigits(){let t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}},us=class{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){let e=Object.getPrototypeOf(t),i;ev(e)&&(i=this.register(e));let s=this.items,r=t.id,o=this.scope+"."+r;if(!r)throw new Error("class does not have id: "+t);return r in s||(s[r]=t,Q0(t,o,i),this.override&&ft.override(t.id,t.overrides)),o}get(t){return this.items[t]}unregister(t){let e=this.items,i=t.id,s=this.scope;i in e&&delete e[i],s&&i in ft[s]&&(delete ft[s][i],this.override&&delete yn[i])}};function Q0(n,t,e){let i=Hn(Object.create(null),[e?ft.get(e):{},ft.get(t),n.defaults]);ft.set(t,i),n.defaultRoutes&&tv(t,n.defaultRoutes),n.descriptors&&ft.describe(t,n.descriptors)}function tv(n,t){Object.keys(t).forEach(e=>{let i=e.split("."),s=i.pop(),r=[n].concat(i).join("."),o=t[e].split("."),a=o.pop(),l=o.join(".");ft.route(r,s,l,a)})}function ev(n){return"id"in n&&"defaults"in n}var qh=class{constructor(){this.controllers=new us(ve,"datasets",!0),this.elements=new us(qt,"elements"),this.plugins=new us(Object,"plugins"),this.scales=new us(Mn,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(s=>{let r=i||this._getRegistryForType(s);i||r.isForType(s)||r===this.plugins&&s.id?this._exec(t,r,s):Ct(s,o=>{let a=i||this._getRegistryForType(o);this._exec(t,a,o)})})}_exec(t,e,i){let s=Vi(t);wt(i["before"+s],[],i),e[t](i),wt(i["after"+s],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){let i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){let s=e.get(t);if(s===void 0)throw new Error('"'+t+'" is not a registered '+i+".");return s}},He=new qh,fs=class extends ve{update(t){let e=this._cachedMeta,{data:i=[]}=e,s=this.chart._animationsDisabled,{start:r,count:o}=or(e,i,s);if(this._drawStart=r,this._drawCount=o,ar(e)&&(r=0,o=i.length),this.options.showLine){let{dataset:a,_dataset:l}=e;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!l._decimated,a.points=i;let c=this.resolveDatasetElementOptions(t);c.segment=this.options.segment,this.updateElement(a,void 0,{animated:!s,options:c},t)}this.updateElements(i,r,o,t)}addElements(){let{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=He.getElement("line")),super.addElements()}updateElements(t,e,i,s){let r=s==="reset",{iScale:o,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,h=this.resolveDataElementOptions(e,s),f=this.getSharedOptions(h),g=this.includeOptions(s,f),p=o.axis,m=a.axis,{spanGaps:y,segment:S}=this.options,M=Oe(y)?y:Number.POSITIVE_INFINITY,C=this.chart._animationsDisabled||r||s==="none",F=e>0&&this.getParsed(e-1);for(let D=e;D<e+i;++D){let I=t[D],$=this.getParsed(D),N=C?I:{},G=ut($[m]),U=N[p]=o.getPixelForValue($[p],D),it=N[m]=r||G?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,$,l):$[m],D);N.skip=isNaN(U)||isNaN(it)||G,N.stop=D>0&&Math.abs($[p]-F[p])>M,S&&(N.parsed=$,N.raw=c.data[D]),g&&(N.options=f||this.resolveDataElementOptions(D,I.active?"active":s)),C||this.updateElement(I,D,N,s),F=$}this.updateSharedOptions(f,s,h)}getMaxOverflow(){let t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let a=0;for(let l=e.length-1;l>=0;--l)a=Math.max(a,e[l].size(this.resolveDataElementOptions(l))/2);return a>0&&a}let i=t.dataset,s=i.options&&i.options.borderWidth||0;if(!e.length)return s;let r=e[0].size(this.resolveDataElementOptions(0)),o=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(s,r,o)/2}};fs.id="scatter";fs.defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};fs.overrides={interaction:{mode:"point"},plugins:{tooltip:{callbacks:{title(){return""},label(n){return"("+n.label+", "+n.formattedValue+")"}}}},scales:{x:{type:"linear"},y:{type:"linear"}}};var nv=Object.freeze({__proto__:null,BarController:rs,BubbleController:os,DoughnutController:Qn,LineController:as,PolarAreaController:ls,PieController:Tr,RadarController:cs,ScatterController:fs});function ti(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}var Or=class{constructor(t){this.options=t||{}}init(t){}formats(){return ti()}parse(t,e){return ti()}format(t,e){return ti()}add(t,e,i){return ti()}diff(t,e,i){return ti()}startOf(t,e,i){return ti()}endOf(t,e){return ti()}};Or.override=function(n){Object.assign(Or.prototype,n)};var Pa={_date:Or};function iv(n,t,e,i){let{controller:s,data:r,_sorted:o}=n,a=s._cachedMeta.iScale;if(a&&t===a.axis&&t!=="r"&&o&&r.length){let l=a._reversePixels?Qo:Re;if(i){if(s._sharedOptions){let c=r[0],h=typeof c.getRange=="function"&&c.getRange(t);if(h){let f=l(r,t,e-h),g=l(r,t,e+h);return{lo:f.lo,hi:g.hi}}}}else return l(r,t,e)}return{lo:0,hi:r.length-1}}function ds(n,t,e,i,s){let r=n.getSortedVisibleDatasetMetas(),o=e[t];for(let a=0,l=r.length;a<l;++a){let{index:c,data:h}=r[a],{lo:f,hi:g}=iv(r[a],t,o,s);for(let p=f;p<=g;++p){let m=h[p];m.skip||i(m,c,p)}}}function sv(n){let t=n.indexOf("x")!==-1,e=n.indexOf("y")!==-1;return function(i,s){let r=t?Math.abs(i.x-s.x):0,o=e?Math.abs(i.y-s.y):0;return Math.sqrt(Math.pow(r,2)+Math.pow(o,2))}}function Ta(n,t,e,i,s){let r=[];return!s&&!n.isPointInArea(t)||ds(n,e,t,function(a,l,c){!s&&!Un(a,n.chartArea,0)||a.inRange(t.x,t.y,i)&&r.push({element:a,datasetIndex:l,index:c})},!0),r}function rv(n,t,e,i){let s=[];function r(o,a,l){let{startAngle:c,endAngle:h}=o.getProps(["startAngle","endAngle"],i),{angle:f}=er(o,{x:t.x,y:t.y});Xn(f,c,h)&&s.push({element:o,datasetIndex:a,index:l})}return ds(n,e,t,r),s}function ov(n,t,e,i,s,r){let o=[],a=sv(e),l=Number.POSITIVE_INFINITY;function c(h,f,g){let p=h.inRange(t.x,t.y,s);if(i&&!p)return;let m=h.getCenterPoint(s);if(!(!!r||n.isPointInArea(m))&&!p)return;let S=a(t,m);S<l?(o=[{element:h,datasetIndex:f,index:g}],l=S):S===l&&o.push({element:h,datasetIndex:f,index:g})}return ds(n,e,t,c),o}function Da(n,t,e,i,s,r){return!r&&!n.isPointInArea(t)?[]:e==="r"&&!i?rv(n,t,e,s):ov(n,t,e,i,s,r)}function Gh(n,t,e,i,s){let r=[],o=e==="x"?"inXRange":"inYRange",a=!1;return ds(n,e,t,(l,c,h)=>{l[o](t[e],s)&&(r.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,s))}),i&&!a?[]:r}var av={evaluateInteractionItems:ds,modes:{index(n,t,e,i){let s=nn(t,n),r=e.axis||"x",o=e.includeInvisible||!1,a=e.intersect?Ta(n,s,r,i,o):Da(n,s,r,!1,i,o),l=[];return a.length?(n.getSortedVisibleDatasetMetas().forEach(c=>{let h=a[0].index,f=c.data[h];f&&!f.skip&&l.push({element:f,datasetIndex:c.index,index:h})}),l):[]},dataset(n,t,e,i){let s=nn(t,n),r=e.axis||"xy",o=e.includeInvisible||!1,a=e.intersect?Ta(n,s,r,i,o):Da(n,s,r,!1,i,o);if(a.length>0){let l=a[0].datasetIndex,c=n.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(n,t,e,i){let s=nn(t,n),r=e.axis||"xy",o=e.includeInvisible||!1;return Ta(n,s,r,i,o)},nearest(n,t,e,i){let s=nn(t,n),r=e.axis||"xy",o=e.includeInvisible||!1;return Da(n,s,r,e.intersect,i,o)},x(n,t,e,i){let s=nn(t,n);return Gh(n,s,"x",e.intersect,i)},y(n,t,e,i){let s=nn(t,n);return Gh(n,s,"y",e.intersect,i)}}},Uh=["left","top","right","bottom"];function ps(n,t){return n.filter(e=>e.pos===t)}function Kh(n,t){return n.filter(e=>Uh.indexOf(e.pos)===-1&&e.box.axis===t)}function gs(n,t){return n.sort((e,i)=>{let s=t?i:e,r=t?e:i;return s.weight===r.weight?s.index-r.index:s.weight-r.weight})}function lv(n){let t=[],e,i,s,r,o,a;for(e=0,i=(n||[]).length;e<i;++e)s=n[e],{position:r,options:{stack:o,stackWeight:a=1}}=s,t.push({index:e,box:s,pos:r,horizontal:s.isHorizontal(),weight:s.weight,stack:o&&r+o,stackWeight:a});return t}function cv(n){let t={};for(let e of n){let{stack:i,pos:s,stackWeight:r}=e;if(!i||!Uh.includes(s))continue;let o=t[i]||(t[i]={count:0,placed:0,weight:0,size:0});o.count++,o.weight+=r}return t}function hv(n,t){let e=cv(n),{vBoxMaxWidth:i,hBoxMaxHeight:s}=t,r,o,a;for(r=0,o=n.length;r<o;++r){a=n[r];let{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*i:l&&t.availableWidth,a.height=s):(a.width=i,a.height=h?h*s:l&&t.availableHeight)}return e}function uv(n){let t=lv(n),e=gs(t.filter(c=>c.box.fullSize),!0),i=gs(ps(t,"left"),!0),s=gs(ps(t,"right")),r=gs(ps(t,"top"),!0),o=gs(ps(t,"bottom")),a=Kh(t,"x"),l=Kh(t,"y");return{fullSize:e,leftAndTop:i.concat(r),rightAndBottom:s.concat(l).concat(o).concat(a),chartArea:ps(t,"chartArea"),vertical:i.concat(s).concat(l),horizontal:r.concat(o).concat(a)}}function Zh(n,t,e,i){return Math.max(n[e],t[e])+Math.max(n[i],t[i])}function Jh(n,t){n.top=Math.max(n.top,t.top),n.left=Math.max(n.left,t.left),n.bottom=Math.max(n.bottom,t.bottom),n.right=Math.max(n.right,t.right)}function fv(n,t,e,i){let{pos:s,box:r}=e,o=n.maxPadding;if(!at(s)){e.size&&(n[s]-=e.size);let f=i[e.stack]||{size:0,count:1};f.size=Math.max(f.size,e.horizontal?r.height:r.width),e.size=f.size/f.count,n[s]+=e.size}r.getPadding&&Jh(o,r.getPadding());let a=Math.max(0,t.outerWidth-Zh(o,n,"left","right")),l=Math.max(0,t.outerHeight-Zh(o,n,"top","bottom")),c=a!==n.w,h=l!==n.h;return n.w=a,n.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function dv(n){let t=n.maxPadding;function e(i){let s=Math.max(t[i]-n[i],0);return n[i]+=s,s}n.y+=e("top"),n.x+=e("left"),e("right"),e("bottom")}function pv(n,t){let e=t.maxPadding;function i(s){let r={left:0,top:0,right:0,bottom:0};return s.forEach(o=>{r[o]=Math.max(t[o],e[o])}),r}return i(n?["left","right"]:["top","bottom"])}function ms(n,t,e,i){let s=[],r,o,a,l,c,h;for(r=0,o=n.length,c=0;r<o;++r){a=n[r],l=a.box,l.update(a.width||t.w,a.height||t.h,pv(a.horizontal,t));let{same:f,other:g}=fv(t,e,a,i);c|=f&&s.length,h=h||g,l.fullSize||s.push(a)}return c&&ms(s,t,e,i)||h}function Ar(n,t,e,i,s){n.top=e,n.left=t,n.right=t+i,n.bottom=e+s,n.width=i,n.height=s}function Qh(n,t,e,i){let s=e.padding,{x:r,y:o}=t;for(let a of n){let l=a.box,c=i[a.stack]||{count:1,placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){let f=t.w*h,g=c.size||l.height;Nt(c.start)&&(o=c.start),l.fullSize?Ar(l,s.left,o,e.outerWidth-s.right-s.left,g):Ar(l,t.left+c.placed,o,f,g),c.start=o,c.placed+=f,o=l.bottom}else{let f=t.h*h,g=c.size||l.width;Nt(c.start)&&(r=c.start),l.fullSize?Ar(l,r,s.top,g,e.outerHeight-s.bottom-s.top):Ar(l,r,t.top+c.placed,g,f),c.start=r,c.placed+=f,r=l.right}}t.x=r,t.y=o}ft.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}});var he={addBox(n,t){n.boxes||(n.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},n.boxes.push(t)},removeBox(n,t){let e=n.boxes?n.boxes.indexOf(t):-1;e!==-1&&n.boxes.splice(e,1)},configure(n,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(n,t,e,i){if(!n)return;let s=Vt(n.options.layout.padding),r=Math.max(t-s.width,0),o=Math.max(e-s.height,0),a=uv(n.boxes),l=a.vertical,c=a.horizontal;Ct(n.boxes,y=>{typeof y.beforeLayout=="function"&&y.beforeLayout()});let h=l.reduce((y,S)=>S.box.options&&S.box.options.display===!1?y:y+1,0)||1,f=Object.freeze({outerWidth:t,outerHeight:e,padding:s,availableWidth:r,availableHeight:o,vBoxMaxWidth:r/2/h,hBoxMaxHeight:o/2}),g=Object.assign({},s);Jh(g,Vt(i));let p=Object.assign({maxPadding:g,w:r,h:o,x:s.left,y:s.top},s),m=hv(l.concat(c),f);ms(a.fullSize,p,f,m),ms(l,p,f,m),ms(c,p,f,m)&&ms(l,p,f,m),dv(p),Qh(a.leftAndTop,p,f,m),p.x+=p.w,p.y+=p.h,Qh(a.rightAndBottom,p,f,m),n.chartArea={left:p.left,top:p.top,right:p.left+p.w,bottom:p.top+p.h,height:p.h,width:p.w},Ct(a.chartArea,y=>{let S=y.box;Object.assign(S,n.chartArea),S.update(p.w,p.h,{left:0,top:0,right:0,bottom:0})})}},Ea=class{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}},tu=class extends Ea{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}},Rr="$chartjs",gv={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},eu=n=>n===null||n==="";function mv(n,t){let e=n.style,i=n.getAttribute("height"),s=n.getAttribute("width");if(n[Rr]={initial:{height:i,width:s,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",eu(s)){let r=kr(n,"width");r!==void 0&&(n.width=r)}if(eu(i))if(n.style.height==="")n.height=n.width/(t||2);else{let r=kr(n,"height");r!==void 0&&(n.height=r)}return n}var nu=ya?{passive:!0}:!1;function bv(n,t,e){n.addEventListener(t,e,nu)}function vv(n,t,e){n.canvas.removeEventListener(t,e,nu)}function _v(n,t){let e=gv[n.type]||n.type,{x:i,y:s}=nn(n,t);return{type:e,chart:t,native:n,x:i!==void 0?i:null,y:s!==void 0?s:null}}function Lr(n,t){for(let e of n)if(e===t||e.contains(t))return!0}function yv(n,t,e){let i=n.canvas,s=new MutationObserver(r=>{let o=!1;for(let a of r)o=o||Lr(a.addedNodes,i),o=o&&!Lr(a.removedNodes,i);o&&e()});return s.observe(document,{childList:!0,subtree:!0}),s}function xv(n,t,e){let i=n.canvas,s=new MutationObserver(r=>{let o=!1;for(let a of r)o=o||Lr(a.removedNodes,i),o=o&&!Lr(a.addedNodes,i);o&&e()});return s.observe(document,{childList:!0,subtree:!0}),s}var bs=new Map,iu=0;function su(){let n=window.devicePixelRatio;n!==iu&&(iu=n,bs.forEach((t,e)=>{e.currentDevicePixelRatio!==n&&t()}))}function wv(n,t){bs.size||window.addEventListener("resize",su),bs.set(n,t)}function kv(n){bs.delete(n),bs.size||window.removeEventListener("resize",su)}function Sv(n,t,e){let i=n.canvas,s=i&&ns(i);if(!s)return;let r=rr((a,l)=>{let c=s.clientWidth;e(a,l),c<s.clientWidth&&e()},window),o=new ResizeObserver(a=>{let l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||r(c,h)});return o.observe(s),wv(n,r),o}function Oa(n,t,e){e&&e.disconnect(),t==="resize"&&kv(n)}function Mv(n,t,e){let i=n.canvas,s=rr(r=>{n.ctx!==null&&e(_v(r,n))},n,r=>{let o=r[0];return[o,o.offsetX,o.offsetY]});return bv(i,t,s),s}var ru=class extends Ea{acquireContext(t,e){let i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(mv(t,e),i):null}releaseContext(t){let e=t.canvas;if(!e[Rr])return!1;let i=e[Rr].initial;["height","width"].forEach(r=>{let o=i[r];ut(o)?e.removeAttribute(r):e.setAttribute(r,o)});let s=i.style||{};return Object.keys(s).forEach(r=>{e.style[r]=s[r]}),e.width=e.width,delete e[Rr],!0}addEventListener(t,e,i){this.removeEventListener(t,e);let s=t.$proxies||(t.$proxies={}),o={attach:yv,detach:xv,resize:Sv}[e]||Mv;s[e]=o(t,e,i)}removeEventListener(t,e){let i=t.$proxies||(t.$proxies={}),s=i[e];if(!s)return;({attach:Oa,detach:Oa,resize:Oa}[e]||vv)(t,e,s),i[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return _a(t,e,i,s)}isAttached(t){let e=ns(t);return!!(e&&e.isConnected)}};function Cv(n){return!_r()||typeof OffscreenCanvas!="undefined"&&n instanceof OffscreenCanvas?tu:ru}var ou=class{constructor(){this._init=[]}notify(t,e,i,s){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));let r=s?this._descriptors(t).filter(s):this._descriptors(t),o=this._notify(r,t,e,i);return e==="afterDestroy"&&(this._notify(r,t,"stop"),this._notify(this._init,t,"uninstall")),o}_notify(t,e,i,s){s=s||{};for(let r of t){let o=r.plugin,a=o[i],l=[e,s,r.options];if(wt(a,l,o)===!1&&s.cancelable)return!1}return!0}invalidate(){ut(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;let e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){let i=t&&t.config,s=nt(i.options&&i.options.plugins,{}),r=Pv(i);return s===!1&&!e?[]:Dv(t,r,s,e)}_notifyStateChanges(t){let e=this._oldCache||[],i=this._cache,s=(r,o)=>r.filter(a=>!o.some(l=>a.plugin.id===l.plugin.id));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}};function Pv(n){let t={},e=[],i=Object.keys(He.plugins.items);for(let r=0;r<i.length;r++)e.push(He.getPlugin(i[r]));let s=n.plugins||[];for(let r=0;r<s.length;r++){let o=s[r];e.indexOf(o)===-1&&(e.push(o),t[o.id]=!0)}return{plugins:e,localIds:t}}function Tv(n,t){return!t&&n===!1?null:n===!0?{}:n}function Dv(n,{plugins:t,localIds:e},i,s){let r=[],o=n.getContext();for(let a of t){let l=a.id,c=Tv(i[l],s);c!==null&&r.push({plugin:a,options:Ev(n.config,{plugin:a,local:e[l]},c,o)})}return r}function Ev(n,{plugin:t,local:e},i,s){let r=n.pluginScopeKeys(t),o=n.getOptionScopes(i,r);return e&&t.defaults&&o.push(t.defaults),n.createResolver(o,s,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Aa(n,t){let e=ft.datasets[n]||{};return((t.datasets||{})[n]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function Ov(n,t){let e=n;return n==="_index_"?e=t:n==="_value_"&&(e=t==="x"?"y":"x"),e}function Av(n,t){return n===t?"_index_":"_value_"}function Rv(n){if(n==="top"||n==="bottom")return"x";if(n==="left"||n==="right")return"y"}function Ra(n,t){return n==="x"||n==="y"?n:t.axis||Rv(t.position)||n.charAt(0).toLowerCase()}function Lv(n,t){let e=yn[n.type]||{scales:{}},i=t.scales||{},s=Aa(n.type,t),r=Object.create(null),o=Object.create(null);return Object.keys(i).forEach(a=>{let l=i[a];if(!at(l))return console.error(`Invalid scale configuration for scale: ${a}`);if(l._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${a}`);let c=Ra(a,l),h=Av(c,s),f=e.scales||{};r[c]=r[c]||a,o[a]=Wn(Object.create(null),[{axis:c},l,f[c],f[h]])}),n.data.datasets.forEach(a=>{let l=a.type||n.type,c=a.indexAxis||Aa(l,t),f=(yn[l]||{}).scales||{};Object.keys(f).forEach(g=>{let p=Ov(g,c),m=a[p+"AxisID"]||r[p]||p;o[m]=o[m]||Object.create(null),Wn(o[m],[{axis:p},i[m],f[g]])})}),Object.keys(o).forEach(a=>{let l=o[a];Wn(l,[ft.scales[l.type],ft.scale])}),o}function au(n){let t=n.options||(n.options={});t.plugins=nt(t.plugins,{}),t.scales=Lv(n,t)}function lu(n){return n=n||{},n.datasets=n.datasets||[],n.labels=n.labels||[],n}function Fv(n){return n=n||{},n.data=lu(n.data),au(n),n}var cu=new Map,hu=new Set;function Fr(n,t){let e=cu.get(n);return e||(e=t(),cu.set(n,e),hu.add(e)),e}var vs=(n,t,e)=>{let i=Be(t,e);i!==void 0&&n.add(i)},uu=class{constructor(t){this._config=Fv(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=lu(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){let t=this._config;this.clearCache(),au(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return Fr(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return Fr(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return Fr(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){let e=t.id,i=this.type;return Fr(`${i}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){let i=this._scopeCache,s=i.get(t);return(!s||e)&&(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){let{options:s,type:r}=this,o=this._cachedScopes(t,i),a=o.get(e);if(a)return a;let l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(f=>vs(l,t,f))),h.forEach(f=>vs(l,s,f)),h.forEach(f=>vs(l,yn[r]||{},f)),h.forEach(f=>vs(l,ft,f)),h.forEach(f=>vs(l,fr,f))});let c=Array.from(l);return c.length===0&&c.push(Object.create(null)),hu.has(e)&&o.set(e,c),c}chartOptionScopes(){let{options:t,type:e}=this;return[t,yn[e]||{},ft.datasets[e]||{},{type:e},ft,fr]}resolveNamedOptions(t,e,i,s=[""]){let r={$shared:!0},{resolver:o,subPrefixes:a}=fu(this._resolverCache,t,s),l=o;if($v(o,e)){r.$shared=!1,i=le(i)?i():i;let c=this.createResolver(t,i,a);l=kn(o,i,c)}for(let c of e)r[c]=l[c];return r}createResolver(t,e,i=[""],s){let{resolver:r}=fu(this._resolverCache,t,i);return at(e)?kn(r,e,void 0,s):r}};function fu(n,t,e){let i=n.get(t);i||(i=new Map,n.set(t,i));let s=e.join(),r=i.get(s);return r||(r={resolver:es(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},i.set(s,r)),r}var Iv=n=>at(n)&&Object.getOwnPropertyNames(n).reduce((t,e)=>t||le(n[e]),!1);function $v(n,t){let{isScriptable:e,isIndexable:i}=mr(n);for(let s of t){let r=e(s),o=i(s),a=(o||r)&&n[s];if(r&&(le(a)||Iv(a))||o&&pt(a))return!0}return!1}var jv="3.9.1",zv=["top","bottom","left","right","chartArea"];function du(n,t){return n==="top"||n==="bottom"||zv.indexOf(n)===-1&&t==="x"}function pu(n,t){return function(e,i){return e[n]===i[n]?e[t]-i[t]:e[n]-i[n]}}function gu(n){let t=n.chart,e=t.options.animation;t.notifyPlugins("afterRender"),wt(e&&e.onComplete,[n],t)}function Bv(n){let t=n.chart,e=t.options.animation;wt(e&&e.onProgress,[n],t)}function mu(n){return _r()&&typeof n=="string"?n=document.getElementById(n):n&&n.length&&(n=n[0]),n&&n.canvas&&(n=n.canvas),n}var Ir={},bu=n=>{let t=mu(n);return Object.values(Ir).filter(e=>e.canvas===t).pop()};function Nv(n,t,e){let i=Object.keys(n);for(let s of i){let r=+s;if(r>=t){let o=n[s];delete n[s],(e>0||r>t)&&(n[r+e]=o)}}}function Hv(n,t,e,i){return!e||n.type==="mouseout"?null:i?t:n}var se=class{constructor(t,e){let i=this.config=new uu(e),s=mu(t),r=bu(s);if(r)throw new Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");let o=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||Cv(s)),this.platform.updateConfig(i);let a=this.platform.acquireContext(s,o.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=qo(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new ou,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=na(f=>this.update(f),o.resizeDelay||0),this._dataChanges=[],Ir[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}rn.listen(this,"complete",gu),rn.listen(this,"progress",Bv),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:r}=this;return ut(t)?e&&r?r:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():wr(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return dr(this.canvas,this.ctx),this}stop(){return rn.stop(this),this}resize(t,e){rn.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){let i=this.options,s=this.canvas,r=i.maintainAspectRatio&&this.aspectRatio,o=this.platform.getMaximumSize(s,t,e,r),a=i.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,!!wr(this,a,!0)&&(this.notifyPlugins("resize",{size:o}),wt(i.onResize,[this,o],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){let e=this.options.scales||{};Ct(e,(i,s)=>{i.id=s})}buildOrUpdateScales(){let t=this.options,e=t.scales,i=this.scales,s=Object.keys(i).reduce((o,a)=>(o[a]=!1,o),{}),r=[];e&&(r=r.concat(Object.keys(e).map(o=>{let a=e[o],l=Ra(o,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),Ct(r,o=>{let a=o.options,l=a.id,c=Ra(l,a),h=nt(a.type,o.dtype);(a.position===void 0||du(a.position,c)!==du(o.dposition))&&(a.position=o.dposition),s[l]=!0;let f=null;if(l in i&&i[l].type===h)f=i[l];else{let g=He.getScale(h);f=new g({id:l,type:h,ctx:this.ctx,chart:this}),i[f.id]=f}f.init(a,t)}),Ct(s,(o,a)=>{o||delete i[a]}),Ct(i,o=>{he.configure(this,o,o.options),he.addBox(this,o)})}_updateMetasets(){let t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((s,r)=>s.index-r.index),i>e){for(let s=e;s<i;++s)this._destroyDatasetMeta(s);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(pu("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((i,s)=>{e.filter(r=>r===i._dataset).length===0&&this._destroyDatasetMeta(s)})}buildOrUpdateControllers(){let t=[],e=this.data.datasets,i,s;for(this._removeUnreferencedMetasets(),i=0,s=e.length;i<s;i++){let r=e[i],o=this.getDatasetMeta(i),a=r.type||this.config.type;if(o.type&&o.type!==a&&(this._destroyDatasetMeta(i),o=this.getDatasetMeta(i)),o.type=a,o.indexAxis=r.indexAxis||Aa(a,this.options),o.order=r.order||0,o.index=i,o.label=""+r.label,o.visible=this.isDatasetVisible(i),o.controller)o.controller.updateIndex(i),o.controller.linkScales();else{let l=He.getController(a),{datasetElementType:c,dataElementType:h}=ft.datasets[a];Object.assign(l.prototype,{dataElementType:He.getElement(h),datasetElementType:c&&He.getElement(c)}),o.controller=new l(this,i),t.push(o.controller)}}return this._updateMetasets(),t}_resetElements(){Ct(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let e=this.config;e.update();let i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;let r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let c=0,h=this.data.datasets.length;c<h;c++){let{controller:f}=this.getDatasetMeta(c),g=!s&&r.indexOf(f)===-1;f.buildOrUpdateElements(g),o=Math.max(+f.getMaxOverflow(),o)}o=this._minPadding=i.layout.autoPadding?o:0,this._updateLayout(o),s||Ct(r,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(pu("z","_idx"));let{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){Ct(this.scales,t=>{he.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let t=this.options,e=new Set(Object.keys(this._listeners)),i=new Set(t.events);(!Zs(e,i)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(let{method:i,start:s,count:r}of e){let o=i==="_removeElements"?-r:r;Nv(t,s,o)}}_getUniformDataChanges(){let t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];let e=this.data.datasets.length,i=r=>new Set(t.filter(o=>o[0]===r).map((o,a)=>a+","+o.splice(1).join(","))),s=i(0);for(let r=1;r<e;r++)if(!Zs(s,i(r)))return;return Array.from(s).map(r=>r.split(",")).map(r=>({method:r[1],start:+r[2],count:+r[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;he.update(this,this.width,this.height,t);let e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],Ct(this.boxes,s=>{i&&s.position==="chartArea"||(s.configure&&s.configure(),this._layers.push(...s._layers()))},this),this._layers.forEach((s,r)=>{s._idx=r}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,i=this.data.datasets.length;e<i;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,le(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){let i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",s)!==!1&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(rn.has(this)?this.attached&&!rn.running(this)&&rn.start(this):(this.draw(),gu({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){let{width:i,height:s}=this._resizeBeforeDraw;this._resize(i,s),this._resizeBeforeDraw=null}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;let e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){let e=this._sortedMetasets,i=[],s,r;for(s=0,r=e.length;s<r;++s){let o=e[s];(!t||o.visible)&&i.push(o)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;let t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){let e=this.ctx,i=t._clip,s=!i.disabled,r=this.chartArea,o={meta:t,index:t.index,cancelable:!0};this.notifyPlugins("beforeDatasetDraw",o)!==!1&&(s&&xn(e,{left:i.left===!1?0:r.left-i.left,right:i.right===!1?this.width:r.right+i.right,top:i.top===!1?0:r.top-i.top,bottom:i.bottom===!1?this.height:r.bottom+i.bottom}),t.controller.draw(),s&&wn(e),o.cancelable=!1,this.notifyPlugins("afterDatasetDraw",o))}isPointInArea(t){return Un(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){let r=av.modes[e];return typeof r=="function"?r(this,t,i,s):[]}getDatasetMeta(t){let e=this.data.datasets[t],i=this._metasets,s=i.filter(r=>r&&r._dataset===e).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=Ne(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){let e=this.data.datasets[t];if(!e)return!1;let i=this.getDatasetMeta(t);return typeof i.hidden=="boolean"?!i.hidden:!e.hidden}setDatasetVisibility(t,e){let i=this.getDatasetMeta(t);i.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){let s=i?"show":"hide",r=this.getDatasetMeta(t),o=r.controller._resolveAnimations(void 0,s);Nt(e)?(r.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),o.update(r,{visible:i}),this.update(a=>a.datasetIndex===t?s:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){let e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),rn.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");let{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),dr(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),this.notifyPlugins("destroy"),delete Ir[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let t=this._listeners,e=this.platform,i=(r,o)=>{e.addEventListener(this,r,o),t[r]=o},s=(r,o,a)=>{r.offsetX=o,r.offsetY=a,this._eventHandler(r)};Ct(this.options.events,r=>i(r,s))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});let t=this._responsiveListeners,e=this.platform,i=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},s=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},r=(l,c)=>{this.canvas&&this.resize(l,c)},o,a=()=>{s("attach",a),this.attached=!0,this.resize(),i("resize",r),i("detach",o)};o=()=>{this.attached=!1,s("resize",r),this._stop(),this._resize(0,0),i("attach",a)},e.isAttached(this.canvas)?a():o()}unbindEvents(){Ct(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},Ct(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){let s=i?"set":"remove",r,o,a,l;for(e==="dataset"&&(r=this.getDatasetMeta(t[0].datasetIndex),r.controller["_"+s+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){o=t[a];let c=o&&this.getDatasetMeta(o.datasetIndex).controller;c&&c[s+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){let e=this._active||[],i=t.map(({datasetIndex:r,index:o})=>{let a=this.getDatasetMeta(r);if(!a)throw new Error("No dataset found at index "+r);return{datasetIndex:r,element:a.data[o],index:o}});!yi(i,e)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}_updateHoverStyles(t,e,i){let s=this.options.hover,r=(l,c)=>l.filter(h=>!c.some(f=>h.datasetIndex===f.datasetIndex&&h.index===f.index)),o=r(e,t),a=i?t:r(t,e);o.length&&this.updateHoverStyle(o,s.mode,!1),a.length&&s.mode&&this.updateHoverStyle(a,s.mode,!0)}_eventHandler(t,e){let i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=o=>(o.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",i,s)===!1)return;let r=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(r||i.changed)&&this.render(),this}_handleEvent(t,e,i){let{_active:s=[],options:r}=this,o=e,a=this._getActiveElements(t,s,i,o),l=Uo(t),c=Hv(t,this._lastEvent,i,l);i&&(this._lastEvent=null,wt(r.onHover,[t,a,this],this),l&&wt(r.onClick,[t,a,this],this));let h=!yi(a,s);return(h||e)&&(this._active=a,this._updateHoverStyles(a,s,e)),this._lastEvent=c,h}_getActiveElements(t,e,i,s){if(t.type==="mouseout")return[];if(!i)return e;let r=this.options.hover;return this.getElementsAtEventForMode(t,r.mode,r,s)}},vu=()=>Ct(se.instances,n=>n._plugins.invalidate()),Cn=!0;Object.defineProperties(se,{defaults:{enumerable:Cn,value:ft},instances:{enumerable:Cn,value:Ir},overrides:{enumerable:Cn,value:yn},registry:{enumerable:Cn,value:He},version:{enumerable:Cn,value:jv},getChart:{enumerable:Cn,value:bu},register:{enumerable:Cn,value:(...n)=>{He.add(...n),vu()}},unregister:{enumerable:Cn,value:(...n)=>{He.remove(...n),vu()}}});function _u(n,t,e){let{startAngle:i,pixelMargin:s,x:r,y:o,outerRadius:a,innerRadius:l}=t,c=s/a;n.beginPath(),n.arc(r,o,a,i-c,e+c),l>s?(c=s/l,n.arc(r,o,l,e+c,i-c,!0)):n.arc(r,o,s,e+Rt,i-Rt),n.closePath(),n.clip()}function Wv(n){return ts(n,["outerStart","outerEnd","innerStart","innerEnd"])}function Vv(n,t,e,i){let s=Wv(n.options.borderRadius),r=(e-t)/2,o=Math.min(r,i*t/2),a=l=>{let c=(e-Math.min(r,l))*i/2;return Zt(l,0,Math.min(r,c))};return{outerStart:a(s.outerStart),outerEnd:a(s.outerEnd),innerStart:Zt(s.innerStart,0,o),innerEnd:Zt(s.innerEnd,0,o)}}function Mi(n,t,e,i){return{x:e+n*Math.cos(t),y:i+n*Math.sin(t)}}function La(n,t,e,i,s,r){let{x:o,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=t,f=Math.max(t.outerRadius+i+e-c,0),g=h>0?h+i+e+c:0,p=0,m=s-l;if(i){let et=h>0?h-i:0,Et=f>0?f-i:0,St=(et+Et)/2,re=St!==0?m*St/(St+i):m;p=(m-re)/2}let y=Math.max(.001,m*f-e/gt)/f,S=(m-y)/2,M=l+S+p,C=s-S-p,{outerStart:F,outerEnd:D,innerStart:I,innerEnd:$}=Vv(t,g,f,C-M),N=f-F,G=f-D,U=M+F/N,it=C-D/G,lt=g+I,rt=g+$,Pt=M+I/lt,zt=C-$/rt;if(n.beginPath(),r){if(n.arc(o,a,f,U,it),D>0){let St=Mi(G,it,o,a);n.arc(St.x,St.y,D,it,C+Rt)}let et=Mi(rt,C,o,a);if(n.lineTo(et.x,et.y),$>0){let St=Mi(rt,zt,o,a);n.arc(St.x,St.y,$,C+Rt,zt+Math.PI)}if(n.arc(o,a,g,C-$/g,M+I/g,!0),I>0){let St=Mi(lt,Pt,o,a);n.arc(St.x,St.y,I,Pt+Math.PI,M-Rt)}let Et=Mi(N,M,o,a);if(n.lineTo(Et.x,Et.y),F>0){let St=Mi(N,U,o,a);n.arc(St.x,St.y,F,M-Rt,U)}}else{n.moveTo(o,a);let et=Math.cos(U)*f+o,Et=Math.sin(U)*f+a;n.lineTo(et,Et);let St=Math.cos(it)*f+o,re=Math.sin(it)*f+a;n.lineTo(St,re)}n.closePath()}function Yv(n,t,e,i,s){let{fullCircles:r,startAngle:o,circumference:a}=t,l=t.endAngle;if(r){La(n,t,e,i,o+kt,s);for(let c=0;c<r;++c)n.fill();isNaN(a)||(l=o+a%kt,a%kt==0&&(l+=kt))}return La(n,t,e,i,l,s),n.fill(),l}function Xv(n,t,e){let{x:i,y:s,startAngle:r,pixelMargin:o,fullCircles:a}=t,l=Math.max(t.outerRadius-o,0),c=t.innerRadius+o,h;for(e&&_u(n,t,r+kt),n.beginPath(),n.arc(i,s,c,r+kt,r,!0),h=0;h<a;++h)n.stroke();for(n.beginPath(),n.arc(i,s,l,r,r+kt),h=0;h<a;++h)n.stroke()}function qv(n,t,e,i,s,r){let{options:o}=t,{borderWidth:a,borderJoinStyle:l}=o,c=o.borderAlign==="inner";!a||(c?(n.lineWidth=a*2,n.lineJoin=l||"round"):(n.lineWidth=a,n.lineJoin=l||"bevel"),t.fullCircles&&Xv(n,t,c),c&&_u(n,t,s),La(n,t,e,i,s,r),n.stroke())}var _s=class extends qt{constructor(t){super();this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){let s=this.getProps(["x","y"],i),{angle:r,distance:o}=er(s,{x:t,y:e}),{startAngle:a,endAngle:l,innerRadius:c,outerRadius:h,circumference:f}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),g=this.options.spacing/2,m=nt(f,l-a)>=kt||Xn(r,a,l),y=Ae(o,c+g,h+g);return m&&y}getCenterPoint(t){let{x:e,y:i,startAngle:s,endAngle:r,innerRadius:o,outerRadius:a}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius","circumference"],t),{offset:l,spacing:c}=this.options,h=(s+r)/2,f=(o+a+c+l)/2;return{x:e+Math.cos(h)*f,y:i+Math.sin(h)*f}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){let{options:e,circumference:i}=this,s=(e.offset||0)/2,r=(e.spacing||0)/2,o=e.circular;if(this.pixelMargin=e.borderAlign==="inner"?.33:0,this.fullCircles=i>kt?Math.floor(i/kt):0,i===0||this.innerRadius<0||this.outerRadius<0)return;t.save();let a=0;if(s){a=s/2;let c=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(c)*a,Math.sin(c)*a),this.circumference>=gt&&(a=s)}t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor;let l=Yv(t,this,a,r,o);qv(t,this,a,r,l,o),t.restore()}};_s.id="arc";_s.defaults={borderAlign:"center",borderColor:"#fff",borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};_s.defaultRoutes={backgroundColor:"backgroundColor"};function yu(n,t,e=t){n.lineCap=nt(e.borderCapStyle,t.borderCapStyle),n.setLineDash(nt(e.borderDash,t.borderDash)),n.lineDashOffset=nt(e.borderDashOffset,t.borderDashOffset),n.lineJoin=nt(e.borderJoinStyle,t.borderJoinStyle),n.lineWidth=nt(e.borderWidth,t.borderWidth),n.strokeStyle=nt(e.borderColor,t.borderColor)}function Gv(n,t,e){n.lineTo(e.x,e.y)}function Uv(n){return n.stepped?fa:n.tension||n.cubicInterpolationMode==="monotone"?da:Gv}function xu(n,t,e={}){let i=n.length,{start:s=0,end:r=i-1}=e,{start:o,end:a}=t,l=Math.max(s,o),c=Math.min(r,a),h=s<o&&r<o||s>a&&r>a;return{count:i,start:l,loop:t.loop,ilen:c<l&&!h?i+c-l:c-l}}function Kv(n,t,e,i){let{points:s,options:r}=t,{count:o,start:a,loop:l,ilen:c}=xu(s,e,i),h=Uv(r),{move:f=!0,reverse:g}=i||{},p,m,y;for(p=0;p<=c;++p)m=s[(a+(g?c-p:p))%o],!m.skip&&(f?(n.moveTo(m.x,m.y),f=!1):h(n,y,m,g,r.stepped),y=m);return l&&(m=s[(a+(g?c:0))%o],h(n,y,m,g,r.stepped)),!!l}function Zv(n,t,e,i){let s=t.points,{count:r,start:o,ilen:a}=xu(s,e,i),{move:l=!0,reverse:c}=i||{},h=0,f=0,g,p,m,y,S,M,C=D=>(o+(c?a-D:D))%r,F=()=>{y!==S&&(n.lineTo(h,S),n.lineTo(h,y),n.lineTo(h,M))};for(l&&(p=s[C(0)],n.moveTo(p.x,p.y)),g=0;g<=a;++g){if(p=s[C(g)],p.skip)continue;let D=p.x,I=p.y,$=D|0;$===m?(I<y?y=I:I>S&&(S=I),h=(f*h+D)/++f):(F(),n.lineTo(D,I),m=$,f=0,y=S=I),M=I}F()}function Fa(n){let t=n.options,e=t.borderDash&&t.borderDash.length;return!n._decimated&&!n._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?Zv:Kv}function Jv(n){return n.stepped?xa:n.tension||n.cubicInterpolationMode==="monotone"?wa:sn}function Qv(n,t,e,i){let s=t._path;s||(s=t._path=new Path2D,t.path(s,e,i)&&s.closePath()),yu(n,t.options),n.stroke(s)}function t_(n,t,e,i){let{segments:s,options:r}=t,o=Fa(t);for(let a of s)yu(n,r,a.style),n.beginPath(),o(n,t,a,{start:e,end:e+i-1})&&n.closePath(),n.stroke()}var e_=typeof Path2D=="function";function n_(n,t,e,i){e_&&!t.options.segment?Qv(n,t,e,i):t_(n,t,e,i)}var on=class extends qt{constructor(t){super();this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){let i=this.options;if((i.tension||i.cubicInterpolationMode==="monotone")&&!i.stepped&&!this._pointsUpdated){let s=i.spanGaps?this._loop:this._fullLoop;ba(this._points,i,t,s,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=ka(this,this.options.segment))}first(){let t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){let t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){let i=this.options,s=t[e],r=this.points,o=Pr(this,{property:e,start:s,end:s});if(!o.length)return;let a=[],l=Jv(i),c,h;for(c=0,h=o.length;c<h;++c){let{start:f,end:g}=o[c],p=r[f],m=r[g];if(p===m){a.push(p);continue}let y=Math.abs((s-p[e])/(m[e]-p[e])),S=l(p,m,y,i.stepped);S[e]=t[e],a.push(S)}return a.length===1?a[0]:a}pathSegment(t,e,i){return Fa(this)(t,this,e,i)}path(t,e,i){let s=this.segments,r=Fa(this),o=this._loop;e=e||0,i=i||this.points.length-e;for(let a of s)o&=r(t,this,a,{start:e,end:e+i-1});return!!o}draw(t,e,i,s){let r=this.options||{};(this.points||[]).length&&r.borderWidth&&(t.save(),n_(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}};on.id="line";on.defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};on.defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};on.descriptors={_scriptable:!0,_indexable:n=>n!=="borderDash"&&n!=="fill"};function wu(n,t,e,i){let s=n.options,{[e]:r}=n.getProps([e],i);return Math.abs(t-r)<s.radius+s.hitRadius}var ys=class extends qt{constructor(t){super();this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){let s=this.options,{x:r,y:o}=this.getProps(["x","y"],i);return Math.pow(t-r,2)+Math.pow(e-o,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(t,e){return wu(this,t,"x",e)}inYRange(t,e){return wu(this,t,"y",e)}getCenterPoint(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){t=t||this.options||{};let e=t.radius||0;e=Math.max(e,e&&t.hoverRadius||0);let i=e&&t.borderWidth||0;return(e+i)*2}draw(t,e){let i=this.options;this.skip||i.radius<.1||!Un(this,e,this.size(i)/2)||(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,Qi(t,i,this.x,this.y))}getRange(){let t=this.options||{};return t.radius+t.hitRadius}};ys.id="point";ys.defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};ys.defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};function ku(n,t){let{x:e,y:i,base:s,width:r,height:o}=n.getProps(["x","y","base","width","height"],t),a,l,c,h,f;return n.horizontal?(f=o/2,a=Math.min(e,s),l=Math.max(e,s),c=i-f,h=i+f):(f=r/2,a=e-f,l=e+f,c=Math.min(i,s),h=Math.max(i,s)),{left:a,top:c,right:l,bottom:h}}function Pn(n,t,e,i){return n?0:Zt(t,e,i)}function i_(n,t,e){let i=n.options.borderWidth,s=n.borderSkipped,r=gr(i);return{t:Pn(s.top,r.top,0,e),r:Pn(s.right,r.right,0,t),b:Pn(s.bottom,r.bottom,0,e),l:Pn(s.left,r.left,0,t)}}function s_(n,t,e){let{enableBorderRadius:i}=n.getProps(["enableBorderRadius"]),s=n.options.borderRadius,r=Le(s),o=Math.min(t,e),a=n.borderSkipped,l=i||at(s);return{topLeft:Pn(!l||a.top||a.left,r.topLeft,0,o),topRight:Pn(!l||a.top||a.right,r.topRight,0,o),bottomLeft:Pn(!l||a.bottom||a.left,r.bottomLeft,0,o),bottomRight:Pn(!l||a.bottom||a.right,r.bottomRight,0,o)}}function r_(n){let t=ku(n),e=t.right-t.left,i=t.bottom-t.top,s=i_(n,e/2,i/2),r=s_(n,e/2,i/2);return{outer:{x:t.left,y:t.top,w:e,h:i,radius:r},inner:{x:t.left+s.l,y:t.top+s.t,w:e-s.l-s.r,h:i-s.t-s.b,radius:{topLeft:Math.max(0,r.topLeft-Math.max(s.t,s.l)),topRight:Math.max(0,r.topRight-Math.max(s.t,s.r)),bottomLeft:Math.max(0,r.bottomLeft-Math.max(s.b,s.l)),bottomRight:Math.max(0,r.bottomRight-Math.max(s.b,s.r))}}}}function Ia(n,t,e,i){let s=t===null,r=e===null,a=n&&!(s&&r)&&ku(n,i);return a&&(s||Ae(t,a.left,a.right))&&(r||Ae(e,a.top,a.bottom))}function o_(n){return n.topLeft||n.topRight||n.bottomLeft||n.bottomRight}function a_(n,t){n.rect(t.x,t.y,t.w,t.h)}function $a(n,t,e={}){let i=n.x!==e.x?-t:0,s=n.y!==e.y?-t:0,r=(n.x+n.w!==e.x+e.w?t:0)-i,o=(n.y+n.h!==e.y+e.h?t:0)-s;return{x:n.x+i,y:n.y+s,w:n.w+r,h:n.h+o,radius:n.radius}}var xs=class extends qt{constructor(t){super();this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){let{inflateAmount:e,options:{borderColor:i,backgroundColor:s}}=this,{inner:r,outer:o}=r_(this),a=o_(o.radius)?en:a_;t.save(),(o.w!==r.w||o.h!==r.h)&&(t.beginPath(),a(t,$a(o,e,r)),t.clip(),a(t,$a(r,-e,o)),t.fillStyle=i,t.fill("evenodd")),t.beginPath(),a(t,$a(r,e)),t.fillStyle=s,t.fill(),t.restore()}inRange(t,e,i){return Ia(this,t,e,i)}inXRange(t,e){return Ia(this,t,null,e)}inYRange(t,e){return Ia(this,null,t,e)}getCenterPoint(t){let{x:e,y:i,base:s,horizontal:r}=this.getProps(["x","y","base","horizontal"],t);return{x:r?(e+s)/2:e,y:r?i:(i+s)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}};xs.id="bar";xs.defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};xs.defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};var l_=Object.freeze({__proto__:null,ArcElement:_s,LineElement:on,PointElement:ys,BarElement:xs});function c_(n,t,e,i,s){let r=s.samples||i;if(r>=e)return n.slice(t,t+e);let o=[],a=(e-2)/(r-2),l=0,c=t+e-1,h=t,f,g,p,m,y;for(o[l++]=n[h],f=0;f<r-2;f++){let S=0,M=0,C,F=Math.floor((f+1)*a)+1+t,D=Math.min(Math.floor((f+2)*a)+1,e)+t,I=D-F;for(C=F;C<D;C++)S+=n[C].x,M+=n[C].y;S/=I,M/=I;let $=Math.floor(f*a)+1+t,N=Math.min(Math.floor((f+1)*a)+1,e)+t,{x:G,y:U}=n[h];for(p=m=-1,C=$;C<N;C++)m=.5*Math.abs((G-S)*(n[C].y-U)-(G-n[C].x)*(M-U)),m>p&&(p=m,g=n[C],y=C);o[l++]=g,h=y}return o[l++]=n[c],o}function h_(n,t,e,i){let s=0,r=0,o,a,l,c,h,f,g,p,m,y,S=[],M=t+e-1,C=n[t].x,D=n[M].x-C;for(o=t;o<t+e;++o){a=n[o],l=(a.x-C)/D*i,c=a.y;let I=l|0;if(I===h)c<m?(m=c,f=o):c>y&&(y=c,g=o),s=(r*s+a.x)/++r;else{let $=o-1;if(!ut(f)&&!ut(g)){let N=Math.min(f,g),G=Math.max(f,g);N!==p&&N!==$&&S.push({...n[N],x:s}),G!==p&&G!==$&&S.push({...n[G],x:s})}o>0&&$!==p&&S.push(n[$]),S.push(a),h=I,r=0,m=y=c,f=g=p=o}}return S}function Su(n){if(n._decimated){let t=n._data;delete n._decimated,delete n._data,Object.defineProperty(n,"data",{value:t})}}function Mu(n){n.data.datasets.forEach(t=>{Su(t)})}function u_(n,t){let e=t.length,i=0,s,{iScale:r}=n,{min:o,max:a,minDefined:l,maxDefined:c}=r.getUserBounds();return l&&(i=Zt(Re(t,r.axis,o).lo,0,e-1)),c?s=Zt(Re(t,r.axis,a).hi+1,i,e)-i:s=e-i,{start:i,count:s}}var f_={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(n,t,e)=>{if(!e.enabled){Mu(n);return}let i=n.width;n.data.datasets.forEach((s,r)=>{let{_data:o,indexAxis:a}=s,l=n.getDatasetMeta(r),c=o||s.data;if(Kn([a,n.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;let h=n.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time"||n.options.parsing)return;let{start:f,count:g}=u_(l,c),p=e.threshold||4*i;if(g<=p){Su(s);return}ut(o)&&(s._data=c,delete s.data,Object.defineProperty(s,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(y){this._data=y}}));let m;switch(e.algorithm){case"lttb":m=c_(c,f,g,i,e);break;case"min-max":m=h_(c,f,g,i);break;default:throw new Error(`Unsupported decimation algorithm '${e.algorithm}'`)}s._decimated=m})},destroy(n){Mu(n)}};function d_(n,t,e){let i=n.segments,s=n.points,r=t.points,o=[];for(let a of i){let{start:l,end:c}=a;c=za(l,c,s);let h=ja(e,s[l],s[c],a.loop);if(!t.segments){o.push({source:a,target:h,start:s[l],end:s[c]});continue}let f=Pr(t,h);for(let g of f){let p=ja(e,r[g.start],r[g.end],g.loop),m=Cr(a,s,p);for(let y of m)o.push({source:y,target:g,start:{[e]:Cu(h,p,"start",Math.max)},end:{[e]:Cu(h,p,"end",Math.min)}})}}return o}function ja(n,t,e,i){if(i)return;let s=t[n],r=e[n];return n==="angle"&&(s=fe(s),r=fe(r)),{property:n,start:s,end:r}}function p_(n,t){let{x:e=null,y:i=null}=n||{},s=t.points,r=[];return t.segments.forEach(({start:o,end:a})=>{a=za(o,a,s);let l=s[o],c=s[a];i!==null?(r.push({x:l.x,y:i}),r.push({x:c.x,y:i})):e!==null&&(r.push({x:e,y:l.y}),r.push({x:e,y:c.y}))}),r}function za(n,t,e){for(;t>n;t--){let i=e[t];if(!isNaN(i.x)&&!isNaN(i.y))break}return t}function Cu(n,t,e,i){return n&&t?i(n[e],t[e]):n?n[e]:t?t[e]:0}function Pu(n,t){let e=[],i=!1;return pt(n)?(i=!0,e=n):e=p_(n,t),e.length?new on({points:e,options:{tension:0},_loop:i,_fullLoop:i}):null}function Tu(n){return n&&n.fill!==!1}function g_(n,t,e){let s=n[t].fill,r=[t],o;if(!e)return s;for(;s!==!1&&r.indexOf(s)===-1;){if(!jt(s))return s;if(o=n[s],!o)return!1;if(o.visible)return s;r.push(s),s=o.fill}return!1}function m_(n,t,e){let i=y_(n);if(at(i))return isNaN(i.value)?!1:i;let s=parseFloat(i);return jt(s)&&Math.floor(s)===s?b_(i[0],t,s,e):["origin","start","end","stack","shape"].indexOf(i)>=0&&i}function b_(n,t,e,i){return(n==="-"||n==="+")&&(e=t+e),e===t||e<0||e>=i?!1:e}function v_(n,t){let e=null;return n==="start"?e=t.bottom:n==="end"?e=t.top:at(n)?e=t.getPixelForValue(n.value):t.getBasePixel&&(e=t.getBasePixel()),e}function __(n,t,e){let i;return n==="start"?i=e:n==="end"?i=t.options.reverse?t.min:t.max:at(n)?i=n.value:i=t.getBaseValue(),i}function y_(n){let t=n.options,e=t.fill,i=nt(e&&e.target,e);return i===void 0&&(i=!!t.backgroundColor),i===!1||i===null?!1:i===!0?"origin":i}function x_(n){let{scale:t,index:e,line:i}=n,s=[],r=i.segments,o=i.points,a=w_(t,e);a.push(Pu({x:null,y:t.bottom},i));for(let l=0;l<r.length;l++){let c=r[l];for(let h=c.start;h<=c.end;h++)k_(s,o[h],a)}return new on({points:s,options:{}})}function w_(n,t){let e=[],i=n.getMatchingVisibleMetas("line");for(let s=0;s<i.length;s++){let r=i[s];if(r.index===t)break;r.hidden||e.unshift(r.dataset)}return e}function k_(n,t,e){let i=[];for(let s=0;s<e.length;s++){let r=e[s],{first:o,last:a,point:l}=S_(r,t,"x");if(!(!l||o&&a)){if(o)i.unshift(l);else if(n.push(l),!a)break}}n.push(...i)}function S_(n,t,e){let i=n.interpolate(t,e);if(!i)return{};let s=i[e],r=n.segments,o=n.points,a=!1,l=!1;for(let c=0;c<r.length;c++){let h=r[c],f=o[h.start][e],g=o[h.end][e];if(Ae(s,f,g)){a=s===f,l=s===g;break}}return{first:a,last:l,point:i}}var Ba=class{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){let{x:s,y:r,radius:o}=this;return e=e||{start:0,end:kt},t.arc(s,r,o,e.end,e.start,!0),!i.bounds}interpolate(t){let{x:e,y:i,radius:s}=this,r=t.angle;return{x:e+Math.cos(r)*s,y:i+Math.sin(r)*s,angle:r}}};function M_(n){let{chart:t,fill:e,line:i}=n;if(jt(e))return C_(t,e);if(e==="stack")return x_(n);if(e==="shape")return!0;let s=P_(n);return s instanceof Ba?s:Pu(s,i)}function C_(n,t){let e=n.getDatasetMeta(t);return e&&n.isDatasetVisible(t)?e.dataset:null}function P_(n){return(n.scale||{}).getPointPositionForValue?D_(n):T_(n)}function T_(n){let{scale:t={},fill:e}=n,i=v_(e,t);if(jt(i)){let s=t.isHorizontal();return{x:s?i:null,y:s?null:i}}return null}function D_(n){let{scale:t,fill:e}=n,i=t.options,s=t.getLabels().length,r=i.reverse?t.max:t.min,o=__(e,t,r),a=[];if(i.grid.circular){let l=t.getPointPositionForValue(0,r);return new Ba({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(o)})}for(let l=0;l<s;++l)a.push(t.getPointPositionForValue(l,o));return a}function Na(n,t,e){let i=M_(t),{line:s,scale:r,axis:o}=t,a=s.options,l=a.fill,c=a.backgroundColor,{above:h=c,below:f=c}=l||{};i&&s.points.length&&(xn(n,e),E_(n,{line:s,target:i,above:h,below:f,area:e,scale:r,axis:o}),wn(n))}function E_(n,t){let{line:e,target:i,above:s,below:r,area:o,scale:a}=t,l=e._loop?"angle":t.axis;n.save(),l==="x"&&r!==s&&(Du(n,i,o.top),Eu(n,{line:e,target:i,color:s,scale:a,property:l}),n.restore(),n.save(),Du(n,i,o.bottom)),Eu(n,{line:e,target:i,color:r,scale:a,property:l}),n.restore()}function Du(n,t,e){let{segments:i,points:s}=t,r=!0,o=!1;n.beginPath();for(let a of i){let{start:l,end:c}=a,h=s[l],f=s[za(l,c,s)];r?(n.moveTo(h.x,h.y),r=!1):(n.lineTo(h.x,e),n.lineTo(h.x,h.y)),o=!!t.pathSegment(n,a,{move:o}),o?n.closePath():n.lineTo(f.x,e)}n.lineTo(t.first().x,e),n.closePath(),n.clip()}function Eu(n,t){let{line:e,target:i,property:s,color:r,scale:o}=t,a=d_(e,i,s);for(let{source:l,target:c,start:h,end:f}of a){let{style:{backgroundColor:g=r}={}}=l,p=i!==!0;n.save(),n.fillStyle=g,O_(n,o,p&&ja(s,h,f)),n.beginPath();let m=!!e.pathSegment(n,l),y;if(p){m?n.closePath():Ou(n,i,f,s);let S=!!i.pathSegment(n,c,{move:m,reverse:!0});y=m&&S,y||Ou(n,i,h,s)}n.closePath(),n.fill(y?"evenodd":"nonzero"),n.restore()}}function O_(n,t,e){let{top:i,bottom:s}=t.chart.chartArea,{property:r,start:o,end:a}=e||{};r==="x"&&(n.beginPath(),n.rect(o,i,a-o,s-i),n.clip())}function Ou(n,t,e,i){let s=t.interpolate(e,i);s&&n.lineTo(s.x,s.y)}var A_={id:"filler",afterDatasetsUpdate(n,t,e){let i=(n.data.datasets||[]).length,s=[],r,o,a,l;for(o=0;o<i;++o)r=n.getDatasetMeta(o),a=r.dataset,l=null,a&&a.options&&a instanceof on&&(l={visible:n.isDatasetVisible(o),index:o,fill:m_(a,o,i),chart:n,axis:r.controller.options.indexAxis,scale:r.vScale,line:a}),r.$filler=l,s.push(l);for(o=0;o<i;++o)l=s[o],!(!l||l.fill===!1)&&(l.fill=g_(s,o,e.propagate))},beforeDraw(n,t,e){let i=e.drawTime==="beforeDraw",s=n.getSortedVisibleDatasetMetas(),r=n.chartArea;for(let o=s.length-1;o>=0;--o){let a=s[o].$filler;!a||(a.line.updateControlPoints(r,a.axis),i&&a.fill&&Na(n.ctx,a,r))}},beforeDatasetsDraw(n,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;let i=n.getSortedVisibleDatasetMetas();for(let s=i.length-1;s>=0;--s){let r=i[s].$filler;Tu(r)&&Na(n.ctx,r,n.chartArea)}},beforeDatasetDraw(n,t,e){let i=t.meta.$filler;!Tu(i)||e.drawTime!=="beforeDatasetDraw"||Na(n.ctx,i,n.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}},Au=(n,t)=>{let{boxHeight:e=t,boxWidth:i=t}=n;return n.usePointStyle&&(e=Math.min(e,t),i=n.pointStyleWidth||Math.min(i,t)),{boxWidth:i,boxHeight:e,itemHeight:Math.max(t,e)}},R_=(n,t)=>n!==null&&t!==null&&n.datasetIndex===t.datasetIndex&&n.index===t.index,Ha=class extends qt{constructor(t){super();this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let t=this.options.labels||{},e=wt(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(i=>t.filter(i,this.chart.data))),t.sort&&(e=e.sort((i,s)=>t.sort(i,s,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){let{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}let i=t.labels,s=Ft(i.font),r=s.size,o=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=Au(i,r),c,h;e.font=s.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(o,r,a,l)+10):(h=this.maxHeight,c=this._fitCols(o,r,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){let{ctx:r,maxWidth:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=s+a,f=t;r.textAlign="left",r.textBaseline="middle";let g=-1,p=-h;return this.legendItems.forEach((m,y)=>{let S=i+e/2+r.measureText(m.text).width;(y===0||c[c.length-1]+S+2*a>o)&&(f+=h,c[c.length-(y>0?0:1)]=0,p+=h,g++),l[y]={left:0,top:p,row:g,width:S,height:s},c[c.length-1]+=S+a}),f}_fitCols(t,e,i,s){let{ctx:r,maxHeight:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=o-t,f=a,g=0,p=0,m=0,y=0;return this.legendItems.forEach((S,M)=>{let C=i+e/2+r.measureText(S.text).width;M>0&&p+s+2*a>h&&(f+=g+a,c.push({width:g,height:p}),m+=g+a,y++,g=p=0),l[M]={left:m,top:p,col:y,width:C,height:s},g=Math.max(g,C),p+=s+a}),f+=g,c.push({width:g,height:p}),f}adjustHitBoxes(){if(!this.options.display)return;let t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:s},rtl:r}}=this,o=Sn(r,this.left,this.width);if(this.isHorizontal()){let a=0,l=ie(i,this.left+s,this.right-this.lineWidths[a]);for(let c of e)a!==c.row&&(a=c.row,l=ie(i,this.left+s,this.right-this.lineWidths[a])),c.top+=this.top+t+s,c.left=o.leftForLtr(o.x(l),c.width),l+=c.width+s}else{let a=0,l=ie(i,this.top+t+s,this.bottom-this.columnSizes[a].height);for(let c of e)c.col!==a&&(a=c.col,l=ie(i,this.top+t+s,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+s,c.left=o.leftForLtr(o.x(c.left),c.width),l+=c.height+s}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){let t=this.ctx;xn(t,this),this._draw(),wn(t)}}_draw(){let{options:t,columnSizes:e,lineWidths:i,ctx:s}=this,{align:r,labels:o}=t,a=ft.color,l=Sn(t.rtl,this.left,this.width),c=Ft(o.font),{color:h,padding:f}=o,g=c.size,p=g/2,m;this.drawTitle(),s.textAlign=l.textAlign("left"),s.textBaseline="middle",s.lineWidth=.5,s.font=c.string;let{boxWidth:y,boxHeight:S,itemHeight:M}=Au(o,g),C=function(N,G,U){if(isNaN(y)||y<=0||isNaN(S)||S<0)return;s.save();let it=nt(U.lineWidth,1);if(s.fillStyle=nt(U.fillStyle,a),s.lineCap=nt(U.lineCap,"butt"),s.lineDashOffset=nt(U.lineDashOffset,0),s.lineJoin=nt(U.lineJoin,"miter"),s.lineWidth=it,s.strokeStyle=nt(U.strokeStyle,a),s.setLineDash(nt(U.lineDash,[])),o.usePointStyle){let lt={radius:S*Math.SQRT2/2,pointStyle:U.pointStyle,rotation:U.rotation,borderWidth:it},rt=l.xPlus(N,y/2),Pt=G+p;pr(s,lt,rt,Pt,o.pointStyleWidth&&y)}else{let lt=G+Math.max((g-S)/2,0),rt=l.leftForLtr(N,y),Pt=Le(U.borderRadius);s.beginPath(),Object.values(Pt).some(zt=>zt!==0)?en(s,{x:rt,y:lt,w:y,h:S,radius:Pt}):s.rect(rt,lt,y,S),s.fill(),it!==0&&s.stroke()}s.restore()},F=function(N,G,U){tn(s,U.text,N,G+M/2,c,{strikethrough:U.hidden,textAlign:l.textAlign(U.textAlign)})},D=this.isHorizontal(),I=this._computeTitleHeight();D?m={x:ie(r,this.left+f,this.right-i[0]),y:this.top+f+I,line:0}:m={x:this.left+f,y:ie(r,this.top+I+f,this.bottom-e[0].height),line:0},Sr(this.ctx,t.textDirection);let $=M+f;this.legendItems.forEach((N,G)=>{s.strokeStyle=N.fontColor||h,s.fillStyle=N.fontColor||h;let U=s.measureText(N.text).width,it=l.textAlign(N.textAlign||(N.textAlign=o.textAlign)),lt=y+p+U,rt=m.x,Pt=m.y;l.setWidth(this.width),D?G>0&&rt+lt+f>this.right&&(Pt=m.y+=$,m.line++,rt=m.x=ie(r,this.left+f,this.right-i[m.line])):G>0&&Pt+$>this.bottom&&(rt=m.x=rt+e[m.line].width+f,m.line++,Pt=m.y=ie(r,this.top+I+f,this.bottom-e[m.line].height));let zt=l.x(rt);C(zt,Pt,N),rt=ia(it,rt+y+p,D?rt+lt:this.right,t.rtl),F(l.x(rt),Pt,N),D?m.x+=lt+f:m.y+=$}),Mr(this.ctx,t.textDirection)}drawTitle(){let t=this.options,e=t.title,i=Ft(e.font),s=Vt(e.padding);if(!e.display)return;let r=Sn(t.rtl,this.left,this.width),o=this.ctx,a=e.position,l=i.size/2,c=s.top+l,h,f=this.left,g=this.width;if(this.isHorizontal())g=Math.max(...this.lineWidths),h=this.top+c,f=ie(t.align,f,this.right-g);else{let m=this.columnSizes.reduce((y,S)=>Math.max(y,S.height),0);h=c+ie(t.align,this.top,this.bottom-m-t.labels.padding-this._computeTitleHeight())}let p=ie(a,f,f+g);o.textAlign=r.textAlign(Gi(a)),o.textBaseline="middle",o.strokeStyle=e.color,o.fillStyle=e.color,o.font=i.string,tn(o,e.text,p,h,i)}_computeTitleHeight(){let t=this.options.title,e=Ft(t.font),i=Vt(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,s,r;if(Ae(t,this.left,this.right)&&Ae(e,this.top,this.bottom)){for(r=this.legendHitBoxes,i=0;i<r.length;++i)if(s=r[i],Ae(t,s.left,s.left+s.width)&&Ae(e,s.top,s.top+s.height))return this.legendItems[i]}return null}handleEvent(t){let e=this.options;if(!L_(t.type,e))return;let i=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){let s=this._hoveredItem,r=R_(s,i);s&&!r&&wt(e.onLeave,[t,s,this],this),this._hoveredItem=i,i&&!r&&wt(e.onHover,[t,i,this],this)}else i&&wt(e.onClick,[t,i,this],this)}};function L_(n,t){return!!((n==="mousemove"||n==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(n==="click"||n==="mouseup"))}var F_={id:"legend",_element:Ha,start(n,t,e){let i=n.legend=new Ha({ctx:n.ctx,options:e,chart:n});he.configure(n,i,e),he.addBox(n,i)},stop(n){he.removeBox(n,n.legend),delete n.legend},beforeUpdate(n,t,e){let i=n.legend;he.configure(n,i,e),i.options=e},afterUpdate(n){let t=n.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(n,t){t.replay||n.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(n,t,e){let i=t.datasetIndex,s=e.chart;s.isDatasetVisible(i)?(s.hide(i),t.hidden=!0):(s.show(i),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:n=>n.chart.options.color,boxWidth:40,padding:10,generateLabels(n){let t=n.data.datasets,{labels:{usePointStyle:e,pointStyle:i,textAlign:s,color:r}}=n.legend.options;return n._getSortedDatasetMetas().map(o=>{let a=o.controller.getStyle(e?0:void 0),l=Vt(a.borderWidth);return{text:t[o.index].label,fillStyle:a.backgroundColor,fontColor:r,hidden:!o.visible,lineCap:a.borderCapStyle,lineDash:a.borderDash,lineDashOffset:a.borderDashOffset,lineJoin:a.borderJoinStyle,lineWidth:(l.width+l.height)/4,strokeStyle:a.borderColor,pointStyle:i||a.pointStyle,rotation:a.rotation,textAlign:s||a.textAlign,borderRadius:0,datasetIndex:o.index}},this)}},title:{color:n=>n.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:n=>!n.startsWith("on"),labels:{_scriptable:n=>!["generateLabels","filter","sort"].includes(n)}}},$r=class extends qt{constructor(t){super();this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){let i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;let s=pt(i.text)?i.text.length:1;this._padding=Vt(i.padding);let r=s*Ft(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=r:this.width=r}isHorizontal(){let t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){let{top:e,left:i,bottom:s,right:r,options:o}=this,a=o.align,l=0,c,h,f;return this.isHorizontal()?(h=ie(a,i,r),f=e+t,c=r-i):(o.position==="left"?(h=i+t,f=ie(a,s,e),l=gt*-.5):(h=r-t,f=ie(a,e,s),l=gt*.5),c=s-e),{titleX:h,titleY:f,maxWidth:c,rotation:l}}draw(){let t=this.ctx,e=this.options;if(!e.display)return;let i=Ft(e.font),r=i.lineHeight/2+this._padding.top,{titleX:o,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(r);tn(t,e.text,0,0,i,{color:e.color,maxWidth:l,rotation:c,textAlign:Gi(e.align),textBaseline:"middle",translation:[o,a]})}};function I_(n,t){let e=new $r({ctx:n.ctx,options:t,chart:n});he.configure(n,e,t),he.addBox(n,e),n.titleBlock=e}var $_={id:"title",_element:$r,start(n,t,e){I_(n,e)},stop(n){let t=n.titleBlock;he.removeBox(n,t),delete n.titleBlock},beforeUpdate(n,t,e){let i=n.titleBlock;he.configure(n,i,e),i.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},jr=new WeakMap,j_={id:"subtitle",start(n,t,e){let i=new $r({ctx:n.ctx,options:e,chart:n});he.configure(n,i,e),he.addBox(n,i),jr.set(n,i)},stop(n){he.removeBox(n,jr.get(n)),jr.delete(n)},beforeUpdate(n,t,e){let i=jr.get(n);he.configure(n,i,e),i.options=e},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},ws={average(n){if(!n.length)return!1;let t,e,i=0,s=0,r=0;for(t=0,e=n.length;t<e;++t){let o=n[t].element;if(o&&o.hasValue()){let a=o.tooltipPosition();i+=a.x,s+=a.y,++r}}return{x:i/r,y:s/r}},nearest(n,t){if(!n.length)return!1;let e=t.x,i=t.y,s=Number.POSITIVE_INFINITY,r,o,a;for(r=0,o=n.length;r<o;++r){let l=n[r].element;if(l&&l.hasValue()){let c=l.getCenterPoint(),h=Ke(t,c);h<s&&(s=h,a=l)}}if(a){let l=a.tooltipPosition();e=l.x,i=l.y}return{x:e,y:i}}};function We(n,t){return t&&(pt(t)?Array.prototype.push.apply(n,t):n.push(t)),n}function an(n){return(typeof n=="string"||n instanceof String)&&n.indexOf(`
`)>-1?n.split(`
`):n}function z_(n,t){let{element:e,datasetIndex:i,index:s}=t,r=n.getDatasetMeta(i).controller,{label:o,value:a}=r.getLabelAndValue(s);return{chart:n,label:o,parsed:r.getParsed(s),raw:n.data.datasets[i].data[s],formattedValue:a,dataset:r.getDataset(),dataIndex:s,datasetIndex:i,element:e}}function Ru(n,t){let e=n.chart.ctx,{body:i,footer:s,title:r}=n,{boxWidth:o,boxHeight:a}=t,l=Ft(t.bodyFont),c=Ft(t.titleFont),h=Ft(t.footerFont),f=r.length,g=s.length,p=i.length,m=Vt(t.padding),y=m.height,S=0,M=i.reduce((D,I)=>D+I.before.length+I.lines.length+I.after.length,0);if(M+=n.beforeBody.length+n.afterBody.length,f&&(y+=f*c.lineHeight+(f-1)*t.titleSpacing+t.titleMarginBottom),M){let D=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;y+=p*D+(M-p)*l.lineHeight+(M-1)*t.bodySpacing}g&&(y+=t.footerMarginTop+g*h.lineHeight+(g-1)*t.footerSpacing);let C=0,F=function(D){S=Math.max(S,e.measureText(D).width+C)};return e.save(),e.font=c.string,Ct(n.title,F),e.font=l.string,Ct(n.beforeBody.concat(n.afterBody),F),C=t.displayColors?o+2+t.boxPadding:0,Ct(i,D=>{Ct(D.before,F),Ct(D.lines,F),Ct(D.after,F)}),C=0,e.font=h.string,Ct(n.footer,F),e.restore(),S+=m.width,{width:S,height:y}}function B_(n,t){let{y:e,height:i}=t;return e<i/2?"top":e>n.height-i/2?"bottom":"center"}function N_(n,t,e,i){let{x:s,width:r}=i,o=e.caretSize+e.caretPadding;if(n==="left"&&s+r+o>t.width||n==="right"&&s-r-o<0)return!0}function H_(n,t,e,i){let{x:s,width:r}=e,{width:o,chartArea:{left:a,right:l}}=n,c="center";return i==="center"?c=s<=(a+l)/2?"left":"right":s<=r/2?c="left":s>=o-r/2&&(c="right"),N_(c,n,t,e)&&(c="center"),c}function Lu(n,t,e){let i=e.yAlign||t.yAlign||B_(n,e);return{xAlign:e.xAlign||t.xAlign||H_(n,t,e,i),yAlign:i}}function W_(n,t){let{x:e,width:i}=n;return t==="right"?e-=i:t==="center"&&(e-=i/2),e}function V_(n,t,e){let{y:i,height:s}=n;return t==="top"?i+=e:t==="bottom"?i-=s+e:i-=s/2,i}function Fu(n,t,e,i){let{caretSize:s,caretPadding:r,cornerRadius:o}=n,{xAlign:a,yAlign:l}=e,c=s+r,{topLeft:h,topRight:f,bottomLeft:g,bottomRight:p}=Le(o),m=W_(t,a),y=V_(t,l,c);return l==="center"?a==="left"?m+=c:a==="right"&&(m-=c):a==="left"?m-=Math.max(h,g)+s:a==="right"&&(m+=Math.max(f,p)+s),{x:Zt(m,0,i.width-t.width),y:Zt(y,0,i.height-t.height)}}function zr(n,t,e){let i=Vt(e.padding);return t==="center"?n.x+n.width/2:t==="right"?n.x+n.width-i.right:n.x+i.left}function Iu(n){return We([],an(n))}function Y_(n,t,e){return Ne(n,{tooltip:t,tooltipItems:e,type:"tooltip"})}function $u(n,t){let e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?n.override(e):n}var Br=class extends qt{constructor(t){super();this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart||t._chart,this._chart=this.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let t=this._cachedAnimations;if(t)return t;let e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,r=new is(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(r)),r}getContext(){return this.$context||(this.$context=Y_(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){let{callbacks:i}=e,s=i.beforeTitle.apply(this,[t]),r=i.title.apply(this,[t]),o=i.afterTitle.apply(this,[t]),a=[];return a=We(a,an(s)),a=We(a,an(r)),a=We(a,an(o)),a}getBeforeBody(t,e){return Iu(e.callbacks.beforeBody.apply(this,[t]))}getBody(t,e){let{callbacks:i}=e,s=[];return Ct(t,r=>{let o={before:[],lines:[],after:[]},a=$u(i,r);We(o.before,an(a.beforeLabel.call(this,r))),We(o.lines,a.label.call(this,r)),We(o.after,an(a.afterLabel.call(this,r))),s.push(o)}),s}getAfterBody(t,e){return Iu(e.callbacks.afterBody.apply(this,[t]))}getFooter(t,e){let{callbacks:i}=e,s=i.beforeFooter.apply(this,[t]),r=i.footer.apply(this,[t]),o=i.afterFooter.apply(this,[t]),a=[];return a=We(a,an(s)),a=We(a,an(r)),a=We(a,an(o)),a}_createItems(t){let e=this._active,i=this.chart.data,s=[],r=[],o=[],a=[],l,c;for(l=0,c=e.length;l<c;++l)a.push(z_(this.chart,e[l]));return t.filter&&(a=a.filter((h,f,g)=>t.filter(h,f,g,i))),t.itemSort&&(a=a.sort((h,f)=>t.itemSort(h,f,i))),Ct(a,h=>{let f=$u(t.callbacks,h);s.push(f.labelColor.call(this,h)),r.push(f.labelPointStyle.call(this,h)),o.push(f.labelTextColor.call(this,h))}),this.labelColors=s,this.labelPointStyles=r,this.labelTextColors=o,this.dataPoints=a,a}update(t,e){let i=this.options.setContext(this.getContext()),s=this._active,r,o=[];if(!s.length)this.opacity!==0&&(r={opacity:0});else{let a=ws[i.position].call(this,s,this._eventPosition);o=this._createItems(i),this.title=this.getTitle(o,i),this.beforeBody=this.getBeforeBody(o,i),this.body=this.getBody(o,i),this.afterBody=this.getAfterBody(o,i),this.footer=this.getFooter(o,i);let l=this._size=Ru(this,i),c=Object.assign({},a,l),h=Lu(this.chart,i,c),f=Fu(i,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,r={opacity:1,x:f.x,y:f.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=o,this.$context=void 0,r&&this._resolveAnimations().update(this,r),t&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){let r=this.getCaretPosition(t,i,s);e.lineTo(r.x1,r.y1),e.lineTo(r.x2,r.y2),e.lineTo(r.x3,r.y3)}getCaretPosition(t,e,i){let{xAlign:s,yAlign:r}=this,{caretSize:o,cornerRadius:a}=i,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:f}=Le(a),{x:g,y:p}=t,{width:m,height:y}=e,S,M,C,F,D,I;return r==="center"?(D=p+y/2,s==="left"?(S=g,M=S-o,F=D+o,I=D-o):(S=g+m,M=S+o,F=D-o,I=D+o),C=S):(s==="left"?M=g+Math.max(l,h)+o:s==="right"?M=g+m-Math.max(c,f)-o:M=this.caretX,r==="top"?(F=p,D=F-o,S=M-o,C=M+o):(F=p+y,D=F+o,S=M+o,C=M-o),I=F),{x1:S,x2:M,x3:C,y1:F,y2:D,y3:I}}drawTitle(t,e,i){let s=this.title,r=s.length,o,a,l;if(r){let c=Sn(i.rtl,this.x,this.width);for(t.x=zr(this,i.titleAlign,i),e.textAlign=c.textAlign(i.titleAlign),e.textBaseline="middle",o=Ft(i.titleFont),a=i.titleSpacing,e.fillStyle=i.titleColor,e.font=o.string,l=0;l<r;++l)e.fillText(s[l],c.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+a,l+1===r&&(t.y+=i.titleMarginBottom-a)}}_drawColorBox(t,e,i,s,r){let o=this.labelColors[i],a=this.labelPointStyles[i],{boxHeight:l,boxWidth:c,boxPadding:h}=r,f=Ft(r.bodyFont),g=zr(this,"left",r),p=s.x(g),m=l<f.lineHeight?(f.lineHeight-l)/2:0,y=e.y+m;if(r.usePointStyle){let S={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},M=s.leftForLtr(p,c)+c/2,C=y+l/2;t.strokeStyle=r.multiKeyBackground,t.fillStyle=r.multiKeyBackground,Qi(t,S,M,C),t.strokeStyle=o.borderColor,t.fillStyle=o.backgroundColor,Qi(t,S,M,C)}else{t.lineWidth=at(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,t.strokeStyle=o.borderColor,t.setLineDash(o.borderDash||[]),t.lineDashOffset=o.borderDashOffset||0;let S=s.leftForLtr(p,c-h),M=s.leftForLtr(s.xPlus(p,1),c-h-2),C=Le(o.borderRadius);Object.values(C).some(F=>F!==0)?(t.beginPath(),t.fillStyle=r.multiKeyBackground,en(t,{x:S,y,w:c,h:l,radius:C}),t.fill(),t.stroke(),t.fillStyle=o.backgroundColor,t.beginPath(),en(t,{x:M,y:y+1,w:c-2,h:l-2,radius:C}),t.fill()):(t.fillStyle=r.multiKeyBackground,t.fillRect(S,y,c,l),t.strokeRect(S,y,c,l),t.fillStyle=o.backgroundColor,t.fillRect(M,y+1,c-2,l-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){let{body:s}=this,{bodySpacing:r,bodyAlign:o,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=i,f=Ft(i.bodyFont),g=f.lineHeight,p=0,m=Sn(i.rtl,this.x,this.width),y=function(G){e.fillText(G,m.x(t.x+p),t.y+g/2),t.y+=g+r},S=m.textAlign(o),M,C,F,D,I,$,N;for(e.textAlign=o,e.textBaseline="middle",e.font=f.string,t.x=zr(this,S,i),e.fillStyle=i.bodyColor,Ct(this.beforeBody,y),p=a&&S!=="right"?o==="center"?c/2+h:c+2+h:0,D=0,$=s.length;D<$;++D){for(M=s[D],C=this.labelTextColors[D],e.fillStyle=C,Ct(M.before,y),F=M.lines,a&&F.length&&(this._drawColorBox(e,t,D,m,i),g=Math.max(f.lineHeight,l)),I=0,N=F.length;I<N;++I)y(F[I]),g=f.lineHeight;Ct(M.after,y)}p=0,g=f.lineHeight,Ct(this.afterBody,y),t.y-=r}drawFooter(t,e,i){let s=this.footer,r=s.length,o,a;if(r){let l=Sn(i.rtl,this.x,this.width);for(t.x=zr(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=l.textAlign(i.footerAlign),e.textBaseline="middle",o=Ft(i.footerFont),e.fillStyle=i.footerColor,e.font=o.string,a=0;a<r;++a)e.fillText(s[a],l.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+i.footerSpacing}}drawBackground(t,e,i,s){let{xAlign:r,yAlign:o}=this,{x:a,y:l}=t,{width:c,height:h}=i,{topLeft:f,topRight:g,bottomLeft:p,bottomRight:m}=Le(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(a+f,l),o==="top"&&this.drawCaret(t,e,i,s),e.lineTo(a+c-g,l),e.quadraticCurveTo(a+c,l,a+c,l+g),o==="center"&&r==="right"&&this.drawCaret(t,e,i,s),e.lineTo(a+c,l+h-m),e.quadraticCurveTo(a+c,l+h,a+c-m,l+h),o==="bottom"&&this.drawCaret(t,e,i,s),e.lineTo(a+p,l+h),e.quadraticCurveTo(a,l+h,a,l+h-p),o==="center"&&r==="left"&&this.drawCaret(t,e,i,s),e.lineTo(a,l+f),e.quadraticCurveTo(a,l,a+f,l),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){let e=this.chart,i=this.$animations,s=i&&i.x,r=i&&i.y;if(s||r){let o=ws[t.position].call(this,this._active,this._eventPosition);if(!o)return;let a=this._size=Ru(this,t),l=Object.assign({},o,this._size),c=Lu(e,t,l),h=Fu(t,l,c,e);(s._to!==h.x||r._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=o.x,this.caretY=o.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){let e=this.options.setContext(this.getContext()),i=this.opacity;if(!i)return;this._updateAnimationTarget(e);let s={width:this.width,height:this.height},r={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;let o=Vt(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=i,this.drawBackground(r,t,s,e),Sr(t,e.textDirection),r.y+=o.top,this.drawTitle(r,t,e),this.drawBody(r,t,e),this.drawFooter(r,t,e),Mr(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){let i=this._active,s=t.map(({datasetIndex:a,index:l})=>{let c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),r=!yi(i,s),o=this._positionChanged(s,e);(r||o)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let s=this.options,r=this._active||[],o=this._getActiveElements(t,r,e,i),a=this._positionChanged(o,t),l=e||!yi(o,r)||a;return l&&(this._active=o,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,i,s){let r=this.options;if(t.type==="mouseout")return[];if(!s)return e;let o=this.chart.getElementsAtEventForMode(t,r.mode,r,i);return r.reverse&&o.reverse(),o}_positionChanged(t,e){let{caretX:i,caretY:s,options:r}=this,o=ws[r.position].call(this,t,e);return o!==!1&&(i!==o.x||s!==o.y)}};Br.positioners=ws;var X_={id:"tooltip",_element:Br,positioners:ws,afterInit(n,t,e){e&&(n.tooltip=new Br({chart:n,options:e}))},beforeUpdate(n,t,e){n.tooltip&&n.tooltip.initialize(e)},reset(n,t,e){n.tooltip&&n.tooltip.initialize(e)},afterDraw(n){let t=n.tooltip;if(t&&t._willRender()){let e={tooltip:t};if(n.notifyPlugins("beforeTooltipDraw",e)===!1)return;t.draw(n.ctx),n.notifyPlugins("afterTooltipDraw",e)}},afterEvent(n,t){if(n.tooltip){let e=t.replay;n.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(n,t)=>t.bodyFont.size,boxWidth:(n,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:{beforeTitle:Ee,title(n){if(n.length>0){let t=n[0],e=t.chart.data.labels,i=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(i>0&&t.dataIndex<i)return e[t.dataIndex]}return""},afterTitle:Ee,beforeBody:Ee,beforeLabel:Ee,label(n){if(this&&this.options&&this.options.mode==="dataset")return n.label+": "+n.formattedValue||n.formattedValue;let t=n.dataset.label||"";t&&(t+=": ");let e=n.formattedValue;return ut(e)||(t+=e),t},labelColor(n){let e=n.chart.getDatasetMeta(n.datasetIndex).controller.getStyle(n.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(n){let e=n.chart.getDatasetMeta(n.datasetIndex).controller.getStyle(n.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:Ee,afterBody:Ee,beforeFooter:Ee,footer:Ee,afterFooter:Ee}},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:n=>n!=="filter"&&n!=="itemSort"&&n!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},q_=Object.freeze({__proto__:null,Decimation:f_,Filler:A_,Legend:F_,SubTitle:j_,Title:$_,Tooltip:X_}),G_=(n,t,e,i)=>(typeof t=="string"?(e=n.push(t)-1,i.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function U_(n,t,e,i){let s=n.indexOf(t);if(s===-1)return G_(n,t,e,i);let r=n.lastIndexOf(t);return s!==r?e:s}var K_=(n,t)=>n===null?null:Zt(Math.round(n),0,t),ks=class extends Mn{constructor(t){super(t);this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){let e=this._addedLabels;if(e.length){let i=this.getLabels();for(let{index:s,label:r}of e)i[s]===r&&i.splice(s,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(ut(t))return null;let i=this.getLabels();return e=isFinite(e)&&i[e]===t?e:U_(i,t,nt(e,t),this._addedLabels),K_(e,i.length-1)}determineDataLimits(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),{min:i,max:s}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){let t=this.min,e=this.max,i=this.options.offset,s=[],r=this.getLabels();r=t===0&&e===r.length-1?r:r.slice(t,e+1),this._valueRange=Math.max(r.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let o=t;o<=e;o++)s.push({value:o});return s}getLabelForValue(t){let e=this.getLabels();return t>=0&&t<e.length?e[t]:t}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}};ks.id="category";ks.defaults={ticks:{callback:ks.prototype.getLabelForValue}};function Z_(n,t){let e=[],i=1e-14,{bounds:s,step:r,min:o,max:a,precision:l,count:c,maxTicks:h,maxDigits:f,includeBounds:g}=n,p=r||1,m=h-1,{min:y,max:S}=t,M=!ut(o),C=!ut(a),F=!ut(c),D=(S-y)/(f+1),I=Js((S-y)/m/p)*p,$,N,G,U;if(I<i&&!M&&!C)return[{value:y},{value:S}];U=Math.ceil(S/I)-Math.floor(y/I),U>m&&(I=Js(U*I/m/p)*p),ut(l)||($=Math.pow(10,l),I=Math.ceil(I*$)/$),s==="ticks"?(N=Math.floor(y/I)*I,G=Math.ceil(S/I)*I):(N=y,G=S),M&&C&&r&&Zo((a-o)/r,I/1e3)?(U=Math.round(Math.min((a-o)/I,h)),I=(a-o)/U,N=o,G=a):F?(N=M?o:N,G=C?a:G,U=c-1,I=(G-N)/U):(U=(G-N)/I,Vn(U,Math.round(U),I/1e3)?U=Math.round(U):U=Math.ceil(U));let it=Math.max(tr(I),tr(N));$=Math.pow(10,ut(l)?it:l),N=Math.round(N*$)/$,G=Math.round(G*$)/$;let lt=0;for(M&&(g&&N!==o?(e.push({value:o}),N<o&&lt++,Vn(Math.round((N+lt*I)*$)/$,o,ju(o,D,n))&&lt++):N<o&&lt++);lt<U;++lt)e.push({value:Math.round((N+lt*I)*$)/$});return C&&g&&G!==a?e.length&&Vn(e[e.length-1].value,a,ju(a,D,n))?e[e.length-1].value=a:e.push({value:a}):(!C||G===a)&&e.push({value:G}),e}function ju(n,t,{horizontal:e,minRotation:i}){let s=Wt(i),r=(e?Math.sin(s):Math.cos(s))||.001,o=.75*t*(""+n).length;return Math.min(t/r,o)}var Ss=class extends Mn{constructor(t){super(t);this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return ut(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){let{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds(),{min:s,max:r}=this,o=l=>s=e?s:l,a=l=>r=i?r:l;if(t){let l=Me(s),c=Me(r);l<0&&c<0?a(0):l>0&&c>0&&o(0)}if(s===r){let l=1;(r>=Number.MAX_SAFE_INTEGER||s<=Number.MIN_SAFE_INTEGER)&&(l=Math.abs(r*.05)),a(r+l),t||o(s-l)}this.min=s,this.max=r}getTickLimit(){let t=this.options.ticks,{maxTicksLimit:e,stepSize:i}=t,s;return i?(s=Math.ceil(this.max/i)-Math.floor(this.min/i)+1,s>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${s} ticks. Limiting to 1000.`),s=1e3)):(s=this.computeTickLimit(),e=e||11),e&&(s=Math.min(e,s)),s}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){let t=this.options,e=t.ticks,i=this.getTickLimit();i=Math.max(2,i);let s={maxTicks:i,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},r=this._range||this,o=Z_(s,r);return t.bounds==="ticks"&&Qs(o,this,"value"),t.reverse?(o.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),o}configure(){let t=this.ticks,e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){let s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return Jn(t,this.chart.options.locale,this.options.ticks.format)}},Nr=class extends Ss{determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=jt(t)?t:0,this.max=jt(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){let t=this.isHorizontal(),e=t?this.width:this.height,i=Wt(this.options.ticks.minRotation),s=(t?Math.sin(i):Math.cos(i))||.001,r=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,r.lineHeight/s))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}};Nr.id="linear";Nr.defaults={ticks:{callback:Dr.formatters.numeric}};function zu(n){return n/Math.pow(10,Math.floor(be(n)))===1}function J_(n,t){let e=Math.floor(be(t.max)),i=Math.ceil(t.max/Math.pow(10,e)),s=[],r=me(n.min,Math.pow(10,Math.floor(be(t.min)))),o=Math.floor(be(r)),a=Math.floor(r/Math.pow(10,o)),l=o<0?Math.pow(10,Math.abs(o)):1;do s.push({value:r,major:zu(r)}),++a,a===10&&(a=1,++o,l=o>=0?1:l),r=Math.round(a*Math.pow(10,o)*l)/l;while(o<e||o===e&&a<i);let c=me(n.max,r);return s.push({value:c,major:zu(r)}),s}var Hr=class extends Mn{constructor(t){super(t);this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){let i=Ss.prototype.parse.apply(this,[t,e]);if(i===0){this._zero=!0;return}return jt(i)&&i>0?i:null}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=jt(t)?Math.max(0,t):null,this.max=jt(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),i=this.min,s=this.max,r=l=>i=t?i:l,o=l=>s=e?s:l,a=(l,c)=>Math.pow(10,Math.floor(be(l))+c);i===s&&(i<=0?(r(1),o(10)):(r(a(i,-1)),o(a(s,1)))),i<=0&&r(a(s,-1)),s<=0&&o(a(i,1)),this._zero&&this.min!==this._suggestedMin&&i===a(this.min,0)&&r(a(i,-1)),this.min=i,this.max=s}buildTicks(){let t=this.options,e={min:this._userMin,max:this._userMax},i=J_(e,this);return t.bounds==="ticks"&&Qs(i,this,"value"),t.reverse?(i.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),i}getLabelForValue(t){return t===void 0?"0":Jn(t,this.chart.options.locale,this.options.ticks.format)}configure(){let t=this.min;super.configure(),this._startValue=be(t),this._valueRange=be(this.max)-be(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(be(t)-this._startValue)/this._valueRange)}getValueForPixel(t){let e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}};Hr.id="logarithmic";Hr.defaults={ticks:{callback:Dr.formatters.logarithmic,major:{enabled:!0}}};function Wa(n){let t=n.ticks;if(t.display&&n.display){let e=Vt(t.backdropPadding);return nt(t.font&&t.font.size,ft.font.size)+e.height}return 0}function Q_(n,t,e){return e=pt(e)?e:[e],{w:ua(n,t.string,e),h:e.length*t.lineHeight}}function Bu(n,t,e,i,s){return n===i||n===s?{start:t-e/2,end:t+e/2}:n<i||n>s?{start:t-e,end:t}:{start:t,end:t+e}}function ty(n){let t={l:n.left+n._padding.left,r:n.right-n._padding.right,t:n.top+n._padding.top,b:n.bottom-n._padding.bottom},e=Object.assign({},t),i=[],s=[],r=n._pointLabels.length,o=n.options.pointLabels,a=o.centerPointLabels?gt/r:0;for(let l=0;l<r;l++){let c=o.setContext(n.getPointLabelContext(l));s[l]=c.padding;let h=n.getPointPosition(l,n.drawingArea+s[l],a),f=Ft(c.font),g=Q_(n.ctx,f,n._pointLabels[l]);i[l]=g;let p=fe(n.getIndexAngle(l)+a),m=Math.round(Yn(p)),y=Bu(m,h.x,g.w,0,180),S=Bu(m,h.y,g.h,90,270);ey(e,t,p,y,S)}n.setCenterPoint(t.l-e.l,e.r-t.r,t.t-e.t,e.b-t.b),n._pointLabelItems=ny(n,i,s)}function ey(n,t,e,i,s){let r=Math.abs(Math.sin(e)),o=Math.abs(Math.cos(e)),a=0,l=0;i.start<t.l?(a=(t.l-i.start)/r,n.l=Math.min(n.l,t.l-a)):i.end>t.r&&(a=(i.end-t.r)/r,n.r=Math.max(n.r,t.r+a)),s.start<t.t?(l=(t.t-s.start)/o,n.t=Math.min(n.t,t.t-l)):s.end>t.b&&(l=(s.end-t.b)/o,n.b=Math.max(n.b,t.b+l))}function ny(n,t,e){let i=[],s=n._pointLabels.length,r=n.options,o=Wa(r)/2,a=n.drawingArea,l=r.pointLabels.centerPointLabels?gt/s:0;for(let c=0;c<s;c++){let h=n.getPointPosition(c,a+o+e[c],l),f=Math.round(Yn(fe(h.angle+Rt))),g=t[c],p=ry(h.y,g.h,f),m=iy(f),y=sy(h.x,g.w,m);i.push({x:h.x,y:p,textAlign:m,left:y,top:p,right:y+g.w,bottom:p+g.h})}return i}function iy(n){return n===0||n===180?"center":n<180?"left":"right"}function sy(n,t,e){return e==="right"?n-=t:e==="center"&&(n-=t/2),n}function ry(n,t,e){return e===90||e===270?n-=t/2:(e>270||e<90)&&(n-=t),n}function oy(n,t){let{ctx:e,options:{pointLabels:i}}=n;for(let s=t-1;s>=0;s--){let r=i.setContext(n.getPointLabelContext(s)),o=Ft(r.font),{x:a,y:l,textAlign:c,left:h,top:f,right:g,bottom:p}=n._pointLabelItems[s],{backdropColor:m}=r;if(!ut(m)){let y=Le(r.borderRadius),S=Vt(r.backdropPadding);e.fillStyle=m;let M=h-S.left,C=f-S.top,F=g-h+S.width,D=p-f+S.height;Object.values(y).some(I=>I!==0)?(e.beginPath(),en(e,{x:M,y:C,w:F,h:D,radius:y}),e.fill()):e.fillRect(M,C,F,D)}tn(e,n._pointLabels[s],a,l+o.lineHeight/2,o,{color:r.color,textAlign:c,textBaseline:"middle"})}}function Nu(n,t,e,i){let{ctx:s}=n;if(e)s.arc(n.xCenter,n.yCenter,t,0,kt);else{let r=n.getPointPosition(0,t);s.moveTo(r.x,r.y);for(let o=1;o<i;o++)r=n.getPointPosition(o,t),s.lineTo(r.x,r.y)}}function ay(n,t,e,i){let s=n.ctx,r=t.circular,{color:o,lineWidth:a}=t;!r&&!i||!o||!a||e<0||(s.save(),s.strokeStyle=o,s.lineWidth=a,s.setLineDash(t.borderDash),s.lineDashOffset=t.borderDashOffset,s.beginPath(),Nu(n,e,r,i),s.closePath(),s.stroke(),s.restore())}function ly(n,t,e){return Ne(n,{label:e,index:t,type:"pointLabel"})}var Ci=class extends Ss{constructor(t){super(t);this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let t=this._padding=Vt(Wa(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!1);this.min=jt(t)&&!isNaN(t)?t:0,this.max=jt(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Wa(this.options))}generateTickLabels(t){Ss.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((e,i)=>{let s=wt(this.options.pointLabels.callback,[e,i],this);return s||s===0?s:""}).filter((e,i)=>this.chart.getDataVisibility(i))}fit(){let t=this.options;t.display&&t.pointLabels.display?ty(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,s){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,s))}getIndexAngle(t){let e=kt/(this._pointLabels.length||1),i=this.options.startAngle||0;return fe(t*e+Wt(i))}getDistanceFromCenterForValue(t){if(ut(t))return NaN;let e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(ut(t))return NaN;let e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){let e=this._pointLabels||[];if(t>=0&&t<e.length){let i=e[t];return ly(this.getContext(),t,i)}}getPointPosition(t,e,i=0){let s=this.getIndexAngle(t)-Rt+i;return{x:Math.cos(s)*e+this.xCenter,y:Math.sin(s)*e+this.yCenter,angle:s}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){let{left:e,top:i,right:s,bottom:r}=this._pointLabelItems[t];return{left:e,top:i,right:s,bottom:r}}drawBackground(){let{backgroundColor:t,grid:{circular:e}}=this.options;if(t){let i=this.ctx;i.save(),i.beginPath(),Nu(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){let t=this.ctx,e=this.options,{angleLines:i,grid:s}=e,r=this._pointLabels.length,o,a,l;if(e.pointLabels.display&&oy(this,r),s.display&&this.ticks.forEach((c,h)=>{if(h!==0){a=this.getDistanceFromCenterForValue(c.value);let f=s.setContext(this.getContext(h-1));ay(this,f,a,r)}}),i.display){for(t.save(),o=r-1;o>=0;o--){let c=i.setContext(this.getPointLabelContext(o)),{color:h,lineWidth:f}=c;!f||!h||(t.lineWidth=f,t.strokeStyle=h,t.setLineDash(c.borderDash),t.lineDashOffset=c.borderDashOffset,a=this.getDistanceFromCenterForValue(e.ticks.reverse?this.min:this.max),l=this.getPointPosition(o,a),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(l.x,l.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){let t=this.ctx,e=this.options,i=e.ticks;if(!i.display)return;let s=this.getIndexAngle(0),r,o;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(s),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&!e.reverse)return;let c=i.setContext(this.getContext(l)),h=Ft(c.font);if(r=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=h.string,o=t.measureText(a.label).width,t.fillStyle=c.backdropColor;let f=Vt(c.backdropPadding);t.fillRect(-o/2-f.left,-r-h.size/2-f.top,o+f.width,h.size+f.height)}tn(t,a.label,0,-r,h,{color:c.color})}),t.restore()}drawTitle(){}};Ci.id="radialLinear";Ci.defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Dr.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(n){return n},padding:5,centerPointLabels:!1}};Ci.defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};Ci.descriptors={angleLines:{_fallback:"grid"}};var Wr={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},_e=Object.keys(Wr);function cy(n,t){return n-t}function Hu(n,t){if(ut(t))return null;let e=n._adapter,{parser:i,round:s,isoWeekday:r}=n._parseOpts,o=t;return typeof i=="function"&&(o=i(o)),jt(o)||(o=typeof i=="string"?e.parse(o,i):e.parse(o)),o===null?null:(s&&(o=s==="week"&&(Oe(r)||r===!0)?e.startOf(o,"isoWeek",r):e.startOf(o,s)),+o)}function Wu(n,t,e,i){let s=_e.length;for(let r=_e.indexOf(n);r<s-1;++r){let o=Wr[_e[r]],a=o.steps?o.steps:Number.MAX_SAFE_INTEGER;if(o.common&&Math.ceil((e-t)/(a*o.size))<=i)return _e[r]}return _e[s-1]}function hy(n,t,e,i,s){for(let r=_e.length-1;r>=_e.indexOf(e);r--){let o=_e[r];if(Wr[o].common&&n._adapter.diff(s,i,o)>=t-1)return o}return _e[e?_e.indexOf(e):0]}function uy(n){for(let t=_e.indexOf(n)+1,e=_e.length;t<e;++t)if(Wr[_e[t]].common)return _e[t]}function Vu(n,t,e){if(!e)n[t]=!0;else if(e.length){let{lo:i,hi:s}=qi(e,t),r=e[i]>=t?e[i]:e[s];n[r]=!0}}function fy(n,t,e,i){let s=n._adapter,r=+s.startOf(t[0].value,i),o=t[t.length-1].value,a,l;for(a=r;a<=o;a=+s.add(a,1,i))l=e[a],l>=0&&(t[l].major=!0);return t}function Yu(n,t,e){let i=[],s={},r=t.length,o,a;for(o=0;o<r;++o)a=t[o],s[a]=o,i.push({value:a,major:!1});return r===0||!e?i:fy(n,i,s,e)}var Pi=class extends Mn{constructor(t){super(t);this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e){let i=t.time||(t.time={}),s=this._adapter=new Pa._date(t.adapters.date);s.init(e),Wn(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:Hu(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let t=this.options,e=this._adapter,i=t.time.unit||"day",{min:s,max:r,minDefined:o,maxDefined:a}=this.getUserBounds();function l(c){!o&&!isNaN(c.min)&&(s=Math.min(s,c.min)),!a&&!isNaN(c.max)&&(r=Math.max(r,c.max))}(!o||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),s=jt(s)&&!isNaN(s)?s:+e.startOf(Date.now(),i),r=jt(r)&&!isNaN(r)?r:+e.endOf(Date.now(),i)+1,this.min=Math.min(s,r-1),this.max=Math.max(s+1,r)}_getLabelBounds(){let t=this.getLabelTimestamps(),e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){let t=this.options,e=t.time,i=t.ticks,s=i.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);let r=this.min,o=this.max,a=ta(s,r,o);return this._unit=e.unit||(i.autoSkip?Wu(e.minUnit,this.min,this.max,this._getLabelCapacity(r)):hy(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!i.major.enabled||this._unit==="year"?void 0:uy(this._unit),this.initOffsets(s),t.reverse&&a.reverse(),Yu(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t){let e=0,i=0,s,r;this.options.offset&&t.length&&(s=this.getDecimalForValue(t[0]),t.length===1?e=1-s:e=(this.getDecimalForValue(t[1])-s)/2,r=this.getDecimalForValue(t[t.length-1]),t.length===1?i=r:i=(r-this.getDecimalForValue(t[t.length-2]))/2);let o=t.length<3?.5:.25;e=Zt(e,0,o),i=Zt(i,0,o),this._offsets={start:e,end:i,factor:1/(e+1+i)}}_generate(){let t=this._adapter,e=this.min,i=this.max,s=this.options,r=s.time,o=r.unit||Wu(r.minUnit,e,i,this._getLabelCapacity(e)),a=nt(r.stepSize,1),l=o==="week"?r.isoWeekday:!1,c=Oe(l)||l===!0,h={},f=e,g,p;if(c&&(f=+t.startOf(f,"isoWeek",l)),f=+t.startOf(f,c?"day":o),t.diff(i,e,o)>1e5*a)throw new Error(e+" and "+i+" are too far apart with stepSize of "+a+" "+o);let m=s.ticks.source==="data"&&this.getDataTimestamps();for(g=f,p=0;g<i;g=+t.add(g,a,o),p++)Vu(h,g,m);return(g===i||s.bounds==="ticks"||p===1)&&Vu(h,g,m),Object.keys(h).sort((y,S)=>y-S).map(y=>+y)}getLabelForValue(t){let e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}_tickFormatFunction(t,e,i,s){let r=this.options,o=r.time.displayFormats,a=this._unit,l=this._majorUnit,c=a&&o[a],h=l&&o[l],f=i[e],g=l&&h&&f&&f.major,p=this._adapter.format(t,s||(g?h:c)),m=r.ticks.callback;return m?wt(m,[p,e,i],this):p}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)s=t[e],s.label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){let e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){let e=this.options.ticks,i=this.ctx.measureText(t).width,s=Wt(this.isHorizontal()?e.maxRotation:e.minRotation),r=Math.cos(s),o=Math.sin(s),a=this._resolveTickFontOptions(0).size;return{w:i*r+a*o,h:i*o+a*r}}_getLabelCapacity(t){let e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,r=this._tickFormatFunction(t,0,Yu(this,[t],this._majorUnit),s),o=this._getLabelSize(r),a=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,i;if(t.length)return t;let s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(e=0,i=s.length;e<i;++e)t=t.concat(s[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){let t=this._cache.labels||[],e,i;if(t.length)return t;let s=this.getLabels();for(e=0,i=s.length;e<i;++e)t.push(Hu(this,s[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return ir(t.sort(cy))}};Pi.id="time";Pi.defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",major:{enabled:!1}}};function Vr(n,t,e){let i=0,s=n.length-1,r,o,a,l;e?(t>=n[i].pos&&t<=n[s].pos&&({lo:i,hi:s}=Re(n,"pos",t)),{pos:r,time:a}=n[i],{pos:o,time:l}=n[s]):(t>=n[i].time&&t<=n[s].time&&({lo:i,hi:s}=Re(n,"time",t)),{time:r,pos:a}=n[i],{time:o,pos:l}=n[s]);let c=o-r;return c?a+(l-a)*(t-r)/c:a}var Yr=class extends Pi{constructor(t){super(t);this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=Vr(e,this.min),this._tableRange=Vr(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){let{min:e,max:i}=this,s=[],r=[],o,a,l,c,h;for(o=0,a=t.length;o<a;++o)c=t[o],c>=e&&c<=i&&s.push(c);if(s.length<2)return[{time:e,pos:0},{time:i,pos:1}];for(o=0,a=s.length;o<a;++o)h=s[o+1],l=s[o-1],c=s[o],Math.round((h+l)/2)!==c&&r.push({time:c,pos:o/(a-1)});return r}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;let e=this.getDataTimestamps(),i=this.getLabelTimestamps();return e.length&&i.length?t=this.normalize(e.concat(i)):t=e.length?e:i,t=this._cache.all=t,t}getDecimalForValue(t){return(Vr(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return Vr(this._table,i*this._tableRange+this._minPos,!0)}};Yr.id="timeseries";Yr.defaults=Pi.defaults;var dy=Object.freeze({__proto__:null,CategoryScale:ks,LinearScale:Nr,LogarithmicScale:Hr,RadialLinearScale:Ci,TimeScale:Pi,TimeSeriesScale:Yr}),Xu=[nv,l_,q_,dy];function py(n){let t=[],e=pt(n)?n:ut(n)?[]:[n];for(;e.length;){let i=e.pop();typeof i=="string"?t.unshift.apply(t,i.split(`
`)):Array.isArray(i)?e.push.apply(e,i):ut(e)||t.unshift(""+i)}return t}function Va(n){return!n||["min","max"].indexOf(n)===-1?"max":n}var Tn=n=>n!==void 0;function gy(n,t){let e=new Set(t.map(o=>o.to)),i=new Set(t.map(o=>o.from)),s=new Set([...n.keys()]),r=0;for(;s.size;){let o=my([...s],e);for(let a of o){let l=n.get(a);Tn(l.x)||(l.x=r),s.delete(a)}s.size&&(e.clear(),t.filter(a=>s.has(a.from)).forEach(a=>e.add(a.to)),r++)}return[...n.keys()].filter(o=>!i.has(o)).forEach(o=>{let a=n.get(o);a.column||(a.x=r)}),r}function my(n,t){let e=n.filter(i=>!t.has(i));return e.length?e:n.slice(0,1)}var by=(n,t)=>n.x!==t.x?n.x-t.x:n.y-t.y,Xr=-1;function vy(){return Xr=Xr<100?Xr+1:0,Xr}function Ya(n,t,e=vy()){let i=0;for(let s of n)s.node._visited!==e&&(s.node._visited=e,i+=s.node[t].length+Ya(s.node[t],t,e));return i}var qu=n=>(t,e)=>Ya(t.node[n],n)-Ya(e.node[n],n)||t.node[n].length-e.node[n].length;function Xa(n,t){n.from.sort(qu("from"));for(let e of n.from){let i=e.node;Tn(i.y)||(i.y=t,Xa(i,t)),t=Math.max(i.y+i.out,t)}return t}function ei(n,t){n.to.sort(qu("to"));for(let e of n.to){let i=e.node;Tn(i.y)||(i.y=t,ei(i,t)),t=Math.max(i.y+i.in,t)}return t}function Ms(n,t){return Tn(n.y)?n.y:(n.y=t,t)}function _y(n,t){let e=n.filter(h=>h.x===0),i=n.filter(h=>h.x===t),s=e.filter(h=>!Tn(h.y)),r=i.filter(h=>!Tn(h.y)),o=n.filter(h=>h.x>0&&h.x<t&&!Tn(h.y)),a=e.reduce((h,f)=>Math.max(h,f.y+f.out||0),0),l=i.reduce((h,f)=>Math.max(h,f.y+f.in||0),0),c=0;return a>=l?(s.forEach(h=>{a=Ms(h,a),a=Math.max(a+h.out,ei(h,a))}),r.forEach(h=>{l=Ms(h,l),l=Math.max(l+h.in,ei(h,l))})):(r.forEach(h=>{l=Ms(h,l),l=Math.max(l+h.in,ei(h,l))}),s.forEach(h=>{a=Ms(h,a),a=Math.max(a+h.out,ei(h,a))})),o.forEach(h=>{let f=n.filter(g=>g.x===h.x&&Tn(g.y)).reduce((g,p)=>Math.max(g,p.y+Math.max(p.in,p.out)),0);f=Ms(h,f),f=Math.max(f+h.in,Xa(h,f)),f=Math.max(f+h.out,ei(h,f)),c=Math.max(c,f)}),Math.max(a,l,c)}function yy(n,t){n.sort((o,a)=>Math.max(a.in,a.out)-Math.max(o.in,o.out));let e=n[0];e.y=0;let i=Xa(e,0),s=ei(e,0),r=_y(n,t);return Math.max(i,s,r)}function xy(n,t){let e=0,i=0;for(let s=0;s<=t;s++){let r=i,o=n.filter(a=>a.x===s).sort((a,l)=>a.priority-l.priority);i=o[0].to.filter(a=>a.node.x>s+1).reduce((a,l)=>a+l.flow,0)||0;for(let a of o)a.y=r,r+=Math.max(a.out,a.in);e=Math.max(r,e)}return e}function wy(n,t){let e=1,i=0,s=0,r=0,o=[];n.sort(by);for(let a of n){if(a.y){if(a.x===0)o.push(a.y);else{for(i!==a.x&&(i=a.x,s=0),e=s+1;e<o.length&&!(o[e]>a.y);e++);s=e}a.y+=e*t,e++}r=Math.max(r,a.y+Math.max(a.in,a.out))}return r}function ky(n,t){n.forEach(e=>{let i=Math[t](e.in||e.out,e.out||e.in),s=i<e.in,r=i<e.out,o=0,a=e.from.length;e.from.sort((l,c)=>l.node.y+l.node.out/2-(c.node.y+c.node.out/2)).forEach((l,c)=>{s?l.addY=c*(i-l.flow)/(a-1):(l.addY=o,o+=l.flow)}),o=0,a=e.to.length,e.to.sort((l,c)=>l.node.y+l.node.in/2-(c.node.y+c.node.in/2)).forEach((l,c)=>{r?l.addY=c*(i-l.flow)/(a-1):(l.addY=o,o+=l.flow)})})}function Sy(n,t,e,i){let s=[...n.values()],r=gy(n,t),a=(e?xy(s,r):yy(s,r))*.03,l=wy(s,a);return ky(s,i),{maxX:r,maxY:l}}function My(n){let t=new Map;for(let i=0;i<n.length;i++){let{from:s,to:r,flow:o}=n[i];if(!t.has(s))t.set(s,{key:s,in:0,out:o,from:[],to:[{key:r,flow:o,index:i}]});else{let a=t.get(s);a.out+=o,a.to.push({key:r,flow:o,index:i})}if(!t.has(r))t.set(r,{key:r,in:o,out:0,from:[{key:s,flow:o,index:i}],to:[]});else{let a=t.get(r);a.in+=o,a.from.push({key:s,flow:o,index:i})}}let e=(i,s)=>s.flow-i.flow;return[...t.values()].forEach(i=>{i.from=i.from.sort(e),i.from.forEach(s=>{s.node=t.get(s.key)}),i.to=i.to.sort(e),i.to.forEach(s=>{s.node=t.get(s.key)})}),t}function Gu(n,t,e){for(let i of n)if(i.key===t&&i.index===e)return i.addY;return 0}var Ti=class extends ve{parseObjectData(t,e,i,s){let{from:r="from",to:o="to",flow:a="flow"}=this.options.parsing,l=e.map(({[r]:C,[o]:F,[a]:D})=>({from:C,to:F,flow:D})),{xScale:c,yScale:h}=t,f=[],g=this._nodes=My(l),{column:p,priority:m,size:y}=this.getDataset();if(m)for(let C of g.values())C.key in m&&(C.priority=m[C.key]);if(p)for(let C of g.values())C.key in p&&(C.column=!0,C.x=p[C.key]);let{maxX:S,maxY:M}=Sy(g,l,!!m,Va(y));this._maxX=S,this._maxY=M;for(let C=0,F=l.length;C<F;++C){let D=l[C],I=g.get(D.from),$=g.get(D.to),N=I.y+Gu(I.to,D.to,C),G=$.y+Gu($.from,D.from,C);f.push({x:c.parse(I.x,C),y:h.parse(N,C),_custom:{from:I,to:$,x:c.parse($.x,C),y:h.parse(G,C),height:h.parse(D.flow,C)}})}return f.slice(i,i+s)}getMinMax(t){return{min:0,max:t===this._cachedMeta.xScale?this._maxX:this._maxY}}update(t){let{data:e}=this._cachedMeta;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,s){let{xScale:r,yScale:o}=this._cachedMeta,a=this.resolveDataElementOptions(e,s),l=this.getSharedOptions(s,t[e],a),c=this.getDataset(),h=nt(c.borderWidth,1)/2+.5,f=nt(c.nodeWidth,10);for(let g=e;g<e+i;g++){let p=this.getParsed(g),m=p._custom,y=o.getPixelForValue(p.y);this.updateElement(t[g],g,{x:r.getPixelForValue(p.x)+f+h,y,x2:r.getPixelForValue(m.x)-h,y2:o.getPixelForValue(m.y),from:m.from,to:m.to,progress:s==="reset"?0:1,height:Math.abs(o.getPixelForValue(p.y+m.height)-y),options:this.resolveDataElementOptions(g,s)},s)}this.updateSharedOptions(l,s)}_drawLabels(){let t=this._ctx,e=this._nodes||new Map,i=this.getDataset(),s=Va(i.size),r=nt(i.borderWidth,1),o=nt(i.nodeWidth,10),a=i.labels,{xScale:l,yScale:c}=this._cachedMeta;t.save();let h=this.chart.chartArea;for(let f of e.values()){let g=l.getPixelForValue(f.x),p=c.getPixelForValue(f.y),m=Math[s](f.in||f.out,f.out||f.in),y=Math.abs(c.getPixelForValue(f.y+m)-p),S=a&&a[f.key]||f.key,M=g;t.fillStyle=i.color||"black",t.textBaseline="middle",g<h.width/2?(t.textAlign="left",M+=o+r+4):(t.textAlign="right",M-=r+4),this._drawLabel(S,p,y,t,M)}t.restore()}_drawLabel(t,e,i,s,r){let o=Ft(this.options.font,this.chart.options.font),a=ut(t)?[]:py(t),l=a.length,c=e+i/2,h=o.lineHeight,f=nt(this.options.padding,h/2);if(s.font=o.string,l>1){let g=c-h*l/2+f;for(let p=0;p<l;p++)s.fillText(a[p],r,g+p*h)}else s.fillText(t,r,c)}_drawNodes(){let t=this._ctx,e=this._nodes||new Map,i=this.getDataset(),s=Va(i.size),{xScale:r,yScale:o}=this._cachedMeta,a=nt(i.borderWidth,1),l=nt(i.nodeWidth,10);t.save(),t.strokeStyle=i.borderColor||"black",t.lineWidth=a;for(let c of e.values()){t.fillStyle=c.color;let h=r.getPixelForValue(c.x),f=o.getPixelForValue(c.y),g=Math[s](c.in||c.out,c.out||c.in),p=Math.abs(o.getPixelForValue(c.y+g)-f);a&&t.strokeRect(h,f,l,p),t.fillRect(h,f,l,p)}t.restore()}draw(){let t=this._ctx,e=this.getMeta().data||[],i=[];for(let s=0,r=e.length;s<r;++s){let o=e[s];o.from.color=o.options.colorFrom,o.to.color=o.options.colorTo,o.active&&i.push(o)}for(let s of i)s.from.color=s.options.colorFrom,s.to.color=s.options.colorTo;this._drawNodes();for(let s=0,r=e.length;s<r;++s)e[s].draw(t);this._drawLabels()}};Ti.id="sankey";Ti.defaults={dataElementType:"flow",animations:{numbers:{type:"number",properties:["x","y","x2","y2","height"]},progress:{easing:"linear",duration:n=>n.type==="data"?(n.parsed._custom.x-n.parsed.x)*200:void 0,delay:n=>n.type==="data"?n.parsed.x*500+n.dataIndex*20:void 0},colors:{type:"color",properties:["colorFrom","colorTo"]}},transitions:{hide:{animations:{colors:{type:"color",properties:["colorFrom","colorTo"],to:"transparent"}}},show:{animations:{colors:{type:"color",properties:["colorFrom","colorTo"],from:"transparent"}}}}};Ti.overrides={interaction:{mode:"nearest",intersect:!0},datasets:{clip:!1,parsing:!0},plugins:{tooltip:{callbacks:{title(){return""},label(n){let t=n.dataset.data[n.dataIndex];return t.from+" -> "+t.to+": "+t.flow}}},legend:{display:!1}},scales:{x:{type:"linear",bounds:"data",display:!1,min:0,offset:!1},y:{type:"linear",bounds:"data",display:!1,min:0,reverse:!0,offset:!1}},layout:{padding:{top:3,left:3,right:13,bottom:3}}};var Uu=(n,t,e,i)=>n<e?{cp1:{x:n+(e-n)/3*2,y:t},cp2:{x:n+(e-n)/3,y:i}}:{cp1:{x:n-(n-e)/3,y:0},cp2:{x:e+(n-e)/3,y:0}},Di=(n,t,e)=>({x:n.x+e*(t.x-n.x),y:n.y+e*(t.y-n.y)});function Cy(n,{x:t,x2:e,options:i}){let s;i.colorMode==="from"?s=Je(i.colorFrom).alpha(.5).rgbString():i.colorMode==="to"?s=Je(i.colorTo).alpha(.5).rgbString():(s=n.createLinearGradient(t,0,e,0),s.addColorStop(0,Je(i.colorFrom).alpha(.5).rgbString()),s.addColorStop(1,Je(i.colorTo).alpha(.5).rgbString())),n.fillStyle=s,n.strokeStyle=s,n.lineWidth=.5}var Cs=class extends qt{constructor(t){super();this.options=void 0,this.x=void 0,this.y=void 0,this.x2=void 0,this.y2=void 0,this.height=void 0,t&&Object.assign(this,t)}draw(t){let e=this,{x:i,x2:s,y:r,y2:o,height:a,progress:l}=e,{cp1:c,cp2:h}=Uu(i,r,s,o);l!==0&&(t.save(),l<1&&(t.beginPath(),t.rect(i,Math.min(r,o),(s-i)*l+1,Math.abs(o-r)+a+1),t.clip()),Cy(t,e),t.beginPath(),t.moveTo(i,r),t.bezierCurveTo(c.x,c.y,h.x,h.y,s,o),t.lineTo(s,o+a),t.bezierCurveTo(h.x,h.y+a,c.x,c.y+a,i,r+a),t.lineTo(i,r),t.stroke(),t.closePath(),t.fill(),t.restore())}inRange(t,e,i){let{x:s,y:r,x2:o,y2:a,height:l}=this.getProps(["x","y","x2","y2","height"],i);if(t<s||t>o)return!1;let{cp1:c,cp2:h}=Uu(s,r,o,a),f=(t-s)/(o-s),g={x:s,y:r},p={x:o,y:a},m=Di(g,c,f),y=Di(c,h,f),S=Di(h,p,f),M=Di(m,y,f),C=Di(y,S,f),F=Di(M,C,f).y;return e>=F&&e<=F+l}inXRange(t,e){let{x:i,x2:s}=this.getProps(["x","x2"],e);return t>=i&&t<=s}inYRange(t,e){let{y:i,y2:s,height:r}=this.getProps(["y","y2","height"],e),o=Math.min(i,s),a=Math.max(i,s)+r;return t>=o&&t<=a}getCenterPoint(t){let{x:e,y:i,x2:s,y2:r,height:o}=this.getProps(["x","y","x2","y2","height"],t);return{x:(e+s)/2,y:(i+r+o)/2}}tooltipPosition(t){return this.getCenterPoint(t)}getRange(t){return t==="x"?this.width/2:this.height/2}};Cs.id="flow";Cs.defaults={colorFrom:"red",colorTo:"green",colorMode:"gradient",hoverColorFrom:(n,t)=>Gn(t.colorFrom),hoverColorTo:(n,t)=>Gn(t.colorTo)};var Fe=ze(require("obsidian"));var Py={datetime:"MMM D, YYYY, h:mm:ss a",millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm a",hour:"hA",day:"MMM D",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"};Pa._date.override(typeof Fe.moment=="function"?{_id:"moment",formats:function(){return Py},parse:function(n,t){return typeof n=="string"&&typeof t=="string"?n=(0,Fe.moment)(n,t):n instanceof Fe.moment||(n=(0,Fe.moment)(n)),n.isValid()?n.valueOf():null},format:function(n,t){return(0,Fe.moment)(n).format(t)},add:function(n,t,e){return(0,Fe.moment)(n).add(t,e).valueOf()},diff:function(n,t,e){return(0,Fe.moment)(n).diff((0,Fe.moment)(t),e)},startOf:function(n,t,e){return n=(0,Fe.moment)(n),t==="isoWeek"?(e=Math.trunc(Math.min(Math.max(0,e),6)),n.isoWeekday(e).startOf("day").valueOf()):n.startOf(t).valueOf()},endOf:function(n,t){return(0,Fe.moment)(n).endOf(t).valueOf()}}:{});var Qr=ze(require("obsidian"));var Zu=ze(Ku());function ln(n,t=.25){if(typeof t!="number")throw"Provided alpha value is not a number";return n.map(e=>(0,Zu.default)(e.trim()).alpha(t).hex())}function cn(n,t){var i,s;let e=t.createDiv({cls:"chart-error"});e.createEl("b",{text:"Couldn't render Chart:"}),e.createEl("pre").createEl("code",{text:(s=(i=n.toString)==null?void 0:i.call(n))!=null?s:n}),e.createEl("hr"),e.createEl("span").innerHTML="You might also want to look for further Errors in the Console: Press <kbd>CTRL</kbd> + <kbd>SHIFT</kbd> + <kbd>I</kbd> to open it."}function Ty(n){for(var t=window.atob(n),e=t.length,i=new Uint8Array(e),s=0;s<e;s++)i[s]=t.charCodeAt(s);return i.buffer}async function Ju(n,t,e,i,s){let r=await e.imageRenderer(n.getSelection(),s.imageSettings);console.log("image converted");let o=await t.vault.createBinary(await t.vault.getAvailablePathForAttachments(`Chart ${new Date().toDateString()}`,s.imageSettings.format.split("/").last(),i),Ty(r));console.log("Image saved"),n.replaceSelection(t.fileManager.generateMarkdownLink(o,i.path))}var nf=ze(require("obsidian")),sf=ze(ef());async function Ka(n,t){let{labels:e,dataFields:i}=Za(n.getSelection(),t),s=`\`\`\`chart
type: bar
labels: [${e}]
series:
${i.map(r=>`  - title: ${r.dataTitle}
    data: [${r.data}]`).join(`
`)}
width: 80%
beginAtZero: true
\`\`\``;n.replaceSelection(s)}function Za(n,t,e){let i;try{i=sf.Extractor.extractObject(n,t,!1)}catch(o){throw new nf.Notice("Table malformed"),o}let s=Object.keys(Object.values(i)[0]),r=Object.keys(i).map(o=>({dataTitle:o,data:Object.values(i[o])}));return e&&(r=r.filter(o=>e.contains(o.dataTitle))),{labels:s,dataFields:r}}var rf={modes:{point(n,t){return Gr(n,t,{intersect:!0})},nearest(n,t,e){return Ry(n,t,e)},x(n,t,e){return Gr(n,t,{intersect:e.intersect,axis:"x"})},y(n,t,e){return Gr(n,t,{intersect:e.intersect,axis:"y"})}}};function Ja(n,t,e){return(rf.modes[e.mode]||rf.modes.nearest)(n,t,e)}function Oy(n,t,e){return e!=="x"&&e!=="y"?n.inRange(t.x,t.y,"x",!0)||n.inRange(t.x,t.y,"y",!0):n.inRange(t.x,t.y,e,!0)}function Ay(n,t,e){return e==="x"?{x:n.x,y:t.y}:e==="y"?{x:t.x,y:n.y}:t}function Gr(n,t,e){return n.visibleElements.filter(i=>e.intersect?i.inRange(t.x,t.y):Oy(i,t,e.axis))}function Ry(n,t,e){let i=Number.POSITIVE_INFINITY;return Gr(n,t,e).reduce((s,r)=>{let o=r.getCenterPoint(),a=Ay(t,o,e.axis),l=Ke(t,a);return l<i?(s=[r],i=l):l===i&&s.push(r),s},[]).sort((s,r)=>s._index-r._index).slice(0,1)}var Ly=(n,t)=>t>n||n.length>t.length&&n.slice(0,t.length)===t,ni=.001,Ur=(n,t,e)=>Math.min(e,Math.max(t,n));function Fy(n,t,e){for(let i of Object.keys(n))n[i]=Ur(n[i],t,e);return n}function Iy(n,t,e,i){if(!n||!t||e<=0)return!1;let s=i/2;return Math.pow(n.x-t.x,2)+Math.pow(n.y-t.y,2)<=Math.pow(e+s,2)}function of(n,{x:t,y:e,x2:i,y2:s},r,o){let a=o/2,l=n.x>=t-a-ni&&n.x<=i+a+ni,c=n.y>=e-a-ni&&n.y<=s+a+ni;return r==="x"?l:(r==="y"||l)&&c}function Oi(n,t){let{centerX:e,centerY:i}=n.getProps(["centerX","centerY"],t);return{x:e,y:i}}function $y(n,t,e,i=!0){let s=e.split("."),r=0;for(let o of t.split(".")){let a=s[r++];if(parseInt(o,10)<parseInt(a,10))break;if(Ly(a,o)){if(i)throw new Error(`${n} v${e} is not supported. v${t} or newer is required.`);return!1}}return!0}var af=n=>typeof n=="string"&&n.endsWith("%"),lf=n=>parseFloat(n)/100,cf=n=>Ur(lf(n),0,1);function Qa(n,t){return t==="start"?0:t==="end"?n:af(t)?cf(t)*n:n/2}function Dn(n,t,e=!0){return typeof t=="number"?t:af(t)?(e?cf(t):lf(t))*n:n}function jy(n,t){let{x:e,width:i}=n,s=t.textAlign;return s==="center"?e+i/2:s==="end"||s==="right"?e+i:e}function tl(n,t="center"){return at(n)?{x:nt(n.x,t),y:nt(n.y,t)}:(n=nt(n,t),{x:n,y:n})}function hf(n){return n&&(Nt(n.xValue)||Nt(n.yValue))}function Ps(n,t,e,i=!1){let s=e.init;if(s){if(s===!0)return ff(t,i)}else return;return zy(t,i,wt(s,[{chart:n,properties:t,options:e}]))}function uf(n,t,e){let i=!1;return t.forEach(s=>{le(n[s])?(i=!0,e[s]=n[s]):Nt(e[s])&&delete e[s]}),i}function ff({centerX:n,centerY:t},e){return e?{centerX:n,centerY:t,radius:0,width:0,height:0}:{x:n,y:t,x2:n,y2:t,width:0,height:0}}function zy(n,t,e){if(e===!0)return ff(n,t);if(at(e))return e}var el=new Map,By=n=>isNaN(n)||n<=0,Ny=n=>n.reduce(function(t,e){return t+=e.string,t},"");function Kr(n){if(n&&typeof n=="object"){let t=n.toString();return t==="[object HTMLImageElement]"||t==="[object HTMLCanvasElement]"}}function nl(n,{x:t,y:e},i){i&&(n.translate(t,e),n.rotate(Wt(i)),n.translate(-t,-e))}function En(n,t){if(t&&t.borderWidth)return n.lineCap=t.borderCapStyle,n.setLineDash(t.borderDash),n.lineDashOffset=t.borderDashOffset,n.lineJoin=t.borderJoinStyle,n.lineWidth=t.borderWidth,n.strokeStyle=t.borderColor,!0}function Ai(n,t){n.shadowColor=t.backgroundShadowColor,n.shadowBlur=t.shadowBlur,n.shadowOffsetX=t.shadowOffsetX,n.shadowOffsetY=t.shadowOffsetY}function il(n,t){let e=t.content;if(Kr(e))return{width:Dn(e.width,t.width),height:Dn(e.height,t.height)};let i=t.font,s=pt(i)?i.map(l=>Ft(l)):[Ft(i)],r=t.textStrokeWidth,o=pt(e)?e:[e],a=o.join()+Ny(s)+r+(n._measureText?"-spriting":"");return el.has(a)||el.set(a,Xy(n,o,s,r)),el.get(a)}function df(n,t,e){let{x:i,y:s,width:r,height:o}=t;n.save(),Ai(n,e);let a=En(n,e);n.fillStyle=e.backgroundColor,n.beginPath(),en(n,{x:i,y:s,w:r,h:o,radius:Fy(Le(e.borderRadius),0,Math.min(r,o)/2)}),n.closePath(),n.fill(),a&&(n.shadowColor=e.borderShadowColor,n.stroke()),n.restore()}function Hy(n,t,e){let i=e.content;if(Kr(i)){n.save(),n.globalAlpha=Uy(e.opacity,i.style.opacity),n.drawImage(i,t.x,t.y,t.width,t.height),n.restore();return}let s=pt(i)?i:[i],r=e.font,o=pt(r)?r.map(f=>Ft(f)):[Ft(r)],a=e.color,l=pt(a)?a:[a],c=jy(t,e),h=t.y+e.textStrokeWidth/2;n.save(),n.textBaseline="middle",n.textAlign=e.textAlign,Wy(n,e)&&qy(n,{x:c,y:h},s,o),Gy(n,{x:c,y:h},s,{fonts:o,colors:l}),n.restore()}function Wy(n,t){if(t.textStrokeWidth>0)return n.lineJoin="round",n.miterLimit=2,n.lineWidth=t.textStrokeWidth,n.strokeStyle=t.textStrokeColor,!0}function Vy(n,t,e,i){let{radius:s,options:r}=t,o=r.pointStyle,a=r.rotation,l=(a||0)*Xi;if(Kr(o)){n.save(),n.translate(e,i),n.rotate(l),n.drawImage(o,-o.width/2,-o.height/2,o.width,o.height),n.restore();return}By(s)||Yy(n,{x:e,y:i,radius:s,rotation:a,style:o,rad:l})}function Yy(n,{x:t,y:e,radius:i,rotation:s,style:r,rad:o}){let a,l,c,h;switch(n.beginPath(),r){default:n.arc(t,e,i,0,kt),n.closePath();break;case"triangle":n.moveTo(t+Math.sin(o)*i,e-Math.cos(o)*i),o+=xi,n.lineTo(t+Math.sin(o)*i,e-Math.cos(o)*i),o+=xi,n.lineTo(t+Math.sin(o)*i,e-Math.cos(o)*i),n.closePath();break;case"rectRounded":h=i*.516,c=i-h,a=Math.cos(o+Se)*c,l=Math.sin(o+Se)*c,n.arc(t-a,e-l,h,o-gt,o-Rt),n.arc(t+l,e-a,h,o-Rt,o),n.arc(t+a,e+l,h,o,o+Rt),n.arc(t-l,e+a,h,o+Rt,o+gt),n.closePath();break;case"rect":if(!s){c=Math.SQRT1_2*i,n.rect(t-c,e-c,2*c,2*c);break}o+=Se;case"rectRot":a=Math.cos(o)*i,l=Math.sin(o)*i,n.moveTo(t-a,e-l),n.lineTo(t+l,e-a),n.lineTo(t+a,e+l),n.lineTo(t-l,e+a),n.closePath();break;case"crossRot":o+=Se;case"cross":a=Math.cos(o)*i,l=Math.sin(o)*i,n.moveTo(t-a,e-l),n.lineTo(t+a,e+l),n.moveTo(t+l,e-a),n.lineTo(t-l,e+a);break;case"star":a=Math.cos(o)*i,l=Math.sin(o)*i,n.moveTo(t-a,e-l),n.lineTo(t+a,e+l),n.moveTo(t+l,e-a),n.lineTo(t-l,e+a),o+=Se,a=Math.cos(o)*i,l=Math.sin(o)*i,n.moveTo(t-a,e-l),n.lineTo(t+a,e+l),n.moveTo(t+l,e-a),n.lineTo(t-l,e+a);break;case"line":a=Math.cos(o)*i,l=Math.sin(o)*i,n.moveTo(t-a,e-l),n.lineTo(t+a,e+l);break;case"dash":n.moveTo(t,e),n.lineTo(t+Math.cos(o)*i,e+Math.sin(o)*i);break}n.fill()}function Xy(n,t,e,i){n.save();let s=t.length,r=0,o=i;for(let a=0;a<s;a++){let l=e[Math.min(a,e.length-1)];n.font=l.string;let c=t[a];r=Math.max(r,n.measureText(c).width+i),o+=l.lineHeight}return n.restore(),{width:r,height:o}}function qy(n,{x:t,y:e},i,s){n.beginPath();let r=0;i.forEach(function(o,a){let l=s[Math.min(a,s.length-1)],c=l.lineHeight;n.font=l.string,n.strokeText(o,t,e+c/2+r),r+=c}),n.stroke()}function Gy(n,{x:t,y:e},i,{fonts:s,colors:r}){let o=0;i.forEach(function(a,l){let c=r[Math.min(l,r.length-1)],h=s[Math.min(l,s.length-1)],f=h.lineHeight;n.beginPath(),n.font=h.string,n.fillStyle=c,n.fillText(a,t,e+f/2+o),o+=f,n.fill()})}function Uy(n,t){let e=Oe(n)?n:t;return Oe(e)?Ur(e,0,1):1}var pf={xScaleID:{min:"xMin",max:"xMax",start:"left",end:"right",startProp:"x",endProp:"x2"},yScaleID:{min:"yMin",max:"yMax",start:"bottom",end:"top",startProp:"y",endProp:"y2"}};function Ri(n,t,e){return t=typeof t=="number"?t:n.parse(t),jt(t)?n.getPixelForValue(t):e}function ii(n,t,e){let i=t[e];if(i||e==="scaleID")return i;let s=e.charAt(0),r=Object.values(n).filter(o=>o.axis&&o.axis===s);return r.length?r[0].id:s}function gf(n,t){if(n){let e=n.options.reverse,i=Ri(n,t.min,e?t.end:t.start),s=Ri(n,t.max,e?t.start:t.end);return{start:i,end:s}}}function mf(n,t){let{chartArea:e,scales:i}=n,s=i[ii(i,t,"xScaleID")],r=i[ii(i,t,"yScaleID")],o=e.width/2,a=e.height/2;return s&&(o=Ri(s,t.xValue,s.left+s.width/2)),r&&(a=Ri(r,t.yValue,r.top+r.height/2)),{x:o,y:a}}function sl(n,t){let e=n.scales,i=e[ii(e,t,"xScaleID")],s=e[ii(e,t,"yScaleID")];if(!i&&!s)return{};let{left:r,right:o}=i||n.chartArea,{top:a,bottom:l}=s||n.chartArea,c=_f(i,{min:t.xMin,max:t.xMax,start:r,end:o});r=c.start,o=c.end;let h=_f(s,{min:t.yMin,max:t.yMax,start:l,end:a});return a=h.start,l=h.end,{x:r,y:a,x2:o,y2:l,width:o-r,height:l-a,centerX:r+(o-r)/2,centerY:a+(l-a)/2}}function bf(n,t){if(!hf(t)){let e=sl(n,t),i=t.radius;(!i||isNaN(i))&&(i=Math.min(e.width,e.height)/2,t.radius=i);let s=i*2,r=e.centerX+t.xAdjust,o=e.centerY+t.yAdjust;return{x:r-i,y:o-i,x2:r+i,y2:o+i,centerX:r,centerY:o,width:s,height:s,radius:i}}return Zy(n,t)}function Ky(n,t){let{scales:e,chartArea:i}=n,s=e[t.scaleID],r={x:i.left,y:i.top,x2:i.right,y2:i.bottom};return s?Jy(s,r,t):Qy(e,r,t),r}function vf(n,t,e){let i=sl(n,t);return i.initProperties=Ps(n,i,t,e),i.elements=[{type:"label",optionScope:"label",properties:nx(n,i,t),initProperties:i.initProperties}],i}function Zy(n,t){let e=mf(n,t),i=t.radius*2;return{x:e.x-t.radius+t.xAdjust,y:e.y-t.radius+t.yAdjust,x2:e.x+t.radius+t.xAdjust,y2:e.y+t.radius+t.yAdjust,centerX:e.x+t.xAdjust,centerY:e.y+t.yAdjust,radius:t.radius,width:i,height:i}}function _f(n,t){let e=gf(n,t)||t;return{start:Math.min(e.start,e.end),end:Math.max(e.start,e.end)}}function Jy(n,t,e){let i=Ri(n,e.value,NaN),s=Ri(n,e.endValue,i);n.isHorizontal()?(t.x=i,t.x2=s):(t.y=i,t.y2=s)}function Qy(n,t,e){for(let i of Object.keys(pf)){let s=n[ii(n,e,i)];if(s){let{min:r,max:o,start:a,end:l,startProp:c,endProp:h}=pf[i],f=gf(s,{min:e[r],max:e[o],start:s[a],end:s[l]});t[c]=f.start,t[h]=f.end}}}function tx({properties:n,options:t},e,i,s){let{x:r,x2:o,width:a}=n;return yf({start:r,end:o,size:a,borderWidth:t.borderWidth},{position:i.x,padding:{start:s.left,end:s.right},adjust:t.label.xAdjust,size:e.width})}function ex({properties:n,options:t},e,i,s){let{y:r,y2:o,height:a}=n;return yf({start:r,end:o,size:a,borderWidth:t.borderWidth},{position:i.y,padding:{start:s.top,end:s.bottom},adjust:t.label.yAdjust,size:e.height})}function yf(n,t){let{start:e,end:i,borderWidth:s}=n,{position:r,padding:{start:o,end:a},adjust:l}=t,c=i-s-e-o-a-t.size;return e+s/2+l+Qa(c,r)}function nx(n,t,e){let i=e.label;i.backgroundColor="transparent",i.callout.display=!1;let s=tl(i.position),r=Vt(i.padding),o=il(n.ctx,i),a=tx({properties:t,options:e},o,s,r),l=ex({properties:t,options:e},o,s,r),c=o.width+r.width,h=o.height+r.height;return{x:a,y:l,x2:a+c,y2:l+h,width:c,height:h,centerX:a+c/2,centerY:l+h/2,rotation:i.rotation}}function si(n,t,e){let i=Math.cos(e),s=Math.sin(e),r=t.x,o=t.y;return{x:r+i*(n.x-r)-s*(n.y-o),y:o+s*(n.x-r)+i*(n.y-o)}}var rl=["enter","leave"],ol=rl.concat("click");function ix(n,t,e){t.listened=uf(e,ol,t.listeners),t.moveListened=!1,t._getElements=Ja,rl.forEach(i=>{le(e[i])&&(t.moveListened=!0)}),(!t.listened||!t.moveListened)&&t.annotations.forEach(i=>{!t.listened&&le(i.click)&&(t.listened=!0),t.moveListened||rl.forEach(s=>{le(i[s])&&(t.listened=!0,t.moveListened=!0)})})}function sx(n,t,e){if(n.listened)switch(t.type){case"mousemove":case"mouseout":return rx(n,t,e);case"click":return ox(n,t,e)}}function rx(n,t,e){if(!n.moveListened)return;let i;t.type==="mousemove"?i=Ja(n,t,e.interaction):i=[];let s=n.hovered;n.hovered=i;let r={state:n,event:t},o=xf(r,"leave",s,i);return xf(r,"enter",i,s)||o}function xf({state:n,event:t},e,i,s){let r;for(let o of i)s.indexOf(o)<0&&(r=wf(o.options[e]||n.listeners[e],o,t)||r);return r}function ox(n,t,e){let i=n.listeners,s=Ja(n,t,e.interaction),r;for(let o of s)r=wf(o.options.click||i.click,o,t)||r;return r}function wf(n,t,e){return wt(n,[t.$context,e])===!0}var Zr=["afterDraw","beforeDraw"];function ax(n,t,e){let i=t.visibleElements;t.hooked=uf(e,Zr,t.hooks),t.hooked||i.forEach(s=>{t.hooked||Zr.forEach(r=>{le(s.options[r])&&(t.hooked=!0)})})}function kf(n,t,e){if(n.hooked){let i=t.options[e]||n.hooks[e];return wt(i,[t.$context])}}function lx(n,t,e){let i=dx(n.scales,t,e),s=Sf(t,i,"min","suggestedMin");s=Sf(t,i,"max","suggestedMax")||s,s&&le(t.handleTickRangeOptions)&&t.handleTickRangeOptions()}function cx(n,t){for(let e of n)ux(e,t)}function Sf(n,t,e,i){if(jt(t[e])&&!hx(n.options,e,i)){let s=n[e]!==t[e];return n[e]=t[e],s}}function hx(n,t,e){return Nt(n[t])||Nt(n[e])}function ux(n,t){for(let e of["scaleID","xScaleID","yScaleID"]){let i=ii(t,n,e);i&&!t[i]&&fx(n,e)&&console.warn(`No scale found with id '${i}' for annotation '${n.id}'`)}}function fx(n,t){if(t==="scaleID")return!0;let e=t.charAt(0);for(let i of["Min","Max","Value"])if(Nt(n[e+i]))return!0;return!1}function dx(n,t,e){let i=t.axis,s=t.id,r=i+"ScaleID",o={min:nt(t.min,Number.NEGATIVE_INFINITY),max:nt(t.max,Number.POSITIVE_INFINITY)};for(let a of e)a.scaleID===s?Mf(a,t,["value","endValue"],o):ii(n,a,r)===s&&Mf(a,t,[i+"Min",i+"Max",i+"Value"],o);return o}function Mf(n,t,e,i){for(let s of e){let r=n[s];if(Nt(r)){let o=t.parse(r);i.min=Math.min(i.min,o),i.max=Math.max(i.max,o)}}}var ri=class extends qt{inRange(t,e,i,s){let{x:r,y:o}=si({x:t,y:e},this.getCenterPoint(s),Wt(-this.options.rotation));return of({x:r,y:o},this.getProps(["x","y","x2","y2"],s),i,this.options.borderWidth)}getCenterPoint(t){return Oi(this,t)}draw(t){t.save(),nl(t,this.getCenterPoint(),this.options.rotation),df(t,this,this.options),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){return vf(t,e)}};ri.id="boxAnnotation";ri.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,label:{backgroundColor:"transparent",borderWidth:0,callout:{display:!1},color:"black",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,opacity:void 0,padding:6,position:"center",rotation:void 0,textAlign:"start",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0};ri.defaultRoutes={borderColor:"color",backgroundColor:"color"};ri.descriptors={label:{_fallback:!0}};var Cf=["left","bottom","top","right"],Li=class extends qt{inRange(t,e,i,s){let{x:r,y:o}=si({x:t,y:e},this.getCenterPoint(s),Wt(-this.rotation));return of({x:r,y:o},this.getProps(["x","y","x2","y2"],s),i,this.options.borderWidth)}getCenterPoint(t){return Oi(this,t)}draw(t){let e=this.options,i=!Nt(this._visible)||this._visible;!e.display||!e.content||!i||(t.save(),nl(t,this.getCenterPoint(),this.rotation),gx(t,this),df(t,this,e),Hy(t,wx(this),e),t.restore())}resolveElementProperties(t,e){let i;if(hf(e))i=mf(t,e);else{let{centerX:a,centerY:l}=sl(t,e);i={x:a,y:l}}let s=Vt(e.padding),r=il(t.ctx,e),o=px(i,r,e,s);return{initProperties:Ps(t,o,e),pointX:i.x,pointY:i.y,...o,rotation:e.rotation}}};Li.id="labelAnnotation";Li.defaults={adjustScaleRange:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:0,callout:{borderCapStyle:"butt",borderColor:void 0,borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:1,display:!1,margin:5,position:"auto",side:5,start:"50%"},color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,init:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0};Li.defaultRoutes={borderColor:"color"};function px(n,t,e,i){let s=t.width+i.width+e.borderWidth,r=t.height+i.height+e.borderWidth,o=tl(e.position,"center"),a=Pf(n.x,s,e.xAdjust,o.x),l=Pf(n.y,r,e.yAdjust,o.y);return{x:a,y:l,x2:a+s,y2:l+r,width:s,height:r,centerX:a+s/2,centerY:l+r/2}}function Pf(n,t,e=0,i){return n-Qa(t,i)+e}function gx(n,t){let{pointX:e,pointY:i,options:s}=t,r=s.callout,o=r&&r.display&&yx(t,r);if(!o||kx(t,r,o))return;if(n.save(),n.beginPath(),!En(n,r))return n.restore();let{separatorStart:l,separatorEnd:c}=mx(t,o),{sideStart:h,sideEnd:f}=vx(t,o,l);(r.margin>0||s.borderWidth===0)&&(n.moveTo(l.x,l.y),n.lineTo(c.x,c.y)),n.moveTo(h.x,h.y),n.lineTo(f.x,f.y);let g=si({x:e,y:i},t.getCenterPoint(),Wt(-t.rotation));n.lineTo(g.x,g.y),n.stroke(),n.restore()}function mx(n,t){let{x:e,y:i,x2:s,y2:r}=n,o=bx(n,t),a,l;return t==="left"||t==="right"?(a={x:e+o,y:i},l={x:a.x,y:r}):(a={x:e,y:i+o},l={x:s,y:a.y}),{separatorStart:a,separatorEnd:l}}function bx(n,t){let{width:e,height:i,options:s}=n,r=s.callout.margin+s.borderWidth/2;return t==="right"?e+r:t==="bottom"?i+r:-r}function vx(n,t,e){let{y:i,width:s,height:r,options:o}=n,a=o.callout.start,l=_x(t,o.callout),c,h;return t==="left"||t==="right"?(c={x:e.x,y:i+Dn(r,a)},h={x:c.x+l,y:c.y}):(c={x:e.x+Dn(s,a),y:e.y},h={x:c.x,y:c.y+l}),{sideStart:c,sideEnd:h}}function _x(n,t){let e=t.side;return n==="left"||n==="top"?-e:e}function yx(n,t){let e=t.position;return Cf.includes(e)?e:xx(n,t)}function xx(n,t){let{x:e,y:i,x2:s,y2:r,width:o,height:a,pointX:l,pointY:c,centerX:h,centerY:f,rotation:g}=n,p={x:h,y:f},m=t.start,y=Dn(o,m),S=Dn(a,m),M=[e,e+y,e+y,s],C=[i+S,r,i,r],F=[];for(let D=0;D<4;D++){let I=si({x:M[D],y:C[D]},p,Wt(g));F.push({position:Cf[D],distance:Ke(I,{x:l,y:c})})}return F.sort((D,I)=>D.distance-I.distance)[0].position}function wx({x:n,y:t,width:e,height:i,options:s}){let r=s.borderWidth/2,o=Vt(s.padding);return{x:n+o.left+r,y:t+o.top+r,width:e-o.left-o.right-s.borderWidth,height:i-o.top-o.bottom-s.borderWidth}}function kx(n,t,e){let{pointX:i,pointY:s}=n,r=t.margin,o=i,a=s;return e==="left"?o+=r:e==="right"?o-=r:e==="top"?a+=r:e==="bottom"&&(a-=r),n.inRange(o,a)}var al=(n,t,e)=>({x:n.x+e*(t.x-n.x),y:n.y+e*(t.y-n.y)}),ll=(n,t,e)=>al(t,e,Math.abs((n-t.y)/(e.y-t.y))).x,Tf=(n,t,e)=>al(t,e,Math.abs((n-t.x)/(e.x-t.x))).y,Ts=n=>n*n,Sx=(n,t,{x:e,y:i,x2:s,y2:r},o)=>o==="y"?{start:Math.min(i,r),end:Math.max(i,r),value:t}:{start:Math.min(e,s),end:Math.max(e,s),value:n},Df=(n,t,e,i)=>(1-i)*(1-i)*n+2*(1-i)*i*t+i*i*e,cl=(n,t,e,i)=>({x:Df(n.x,t.x,e.x,i),y:Df(n.y,t.y,e.y,i)}),Ef=(n,t,e,i)=>2*(1-i)*(t-n)+2*i*(e-t),Of=(n,t,e,i)=>-Math.atan2(Ef(n.x,t.x,e.x,i),Ef(n.y,t.y,e.y,i))+.5*gt,Fi=class extends qt{inRange(t,e,i,s){let r=this.options.borderWidth/2;if(i!=="x"&&i!=="y"){let o={mouseX:t,mouseY:e},{path:a,ctx:l}=this;if(a){En(l,this.options);let{chart:h}=this.$context,f=t*h.currentDevicePixelRatio,g=e*h.currentDevicePixelRatio,p=l.isPointInStroke(a,f,g)||hl(this,o,s);return l.restore(),p}let c=Ts(r);return Tx(this,o,c,s)||hl(this,o,s)}return Mx(this,{mouseX:t,mouseY:e},i,{hBorderWidth:r,useFinalPosition:s})}getCenterPoint(t){return Oi(this,t)}draw(t){let{x:e,y:i,x2:s,y2:r,cp:o,options:a}=this;if(t.save(),!En(t,a))return t.restore();Ai(t,a);let l=Math.sqrt(Math.pow(s-e,2)+Math.pow(r-i,2));if(a.curve&&o)return Ix(t,this,o,l),t.restore();let{startOpts:c,endOpts:h,startAdjust:f,endAdjust:g}=If(this),p=Math.atan2(r-i,s-e);t.translate(e,i),t.rotate(p),t.beginPath(),t.moveTo(0+f,0),t.lineTo(l-g,0),t.shadowColor=a.borderShadowColor,t.stroke(),ul(t,0,f,c),ul(t,l,-g,h),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){let i=Ky(t,e),{x:s,y:r,x2:o,y2:a}=i,l=Cx(i,t.chartArea),c=l?Px({x:s,y:r},{x:o,y:a},t.chartArea):{x:s,y:r,x2:o,y2:a,width:Math.abs(o-s),height:Math.abs(a-r)};if(c.centerX=(o+s)/2,c.centerY=(a+r)/2,c.initProperties=Ps(t,c,e),e.curve){let f={x:c.x,y:c.y},g={x:c.x2,y:c.y2};c.cp=Fx(c,e,Ke(f,g))}let h=Dx(t,c,e.label);return h._visible=l,c.elements=[{type:"label",optionScope:"label",properties:h,initProperties:c.initProperties}],c}};Fi.id="lineAnnotation";var Af={backgroundColor:void 0,backgroundShadowColor:void 0,borderColor:void 0,borderDash:void 0,borderDashOffset:void 0,borderShadowColor:void 0,borderWidth:void 0,display:void 0,fill:void 0,length:void 0,shadowBlur:void 0,shadowOffsetX:void 0,shadowOffsetY:void 0,width:void 0};Fi.defaults={adjustScaleRange:!0,arrowHeads:{display:!1,end:Object.assign({},Af),fill:!1,length:12,start:Object.assign({},Af),width:6},borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:2,curve:!1,controlPoint:{y:"-50%"},display:!0,endValue:void 0,init:void 0,label:{backgroundColor:"rgba(0,0,0,0.8)",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderColor:"black",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:6,borderShadowColor:"transparent",borderWidth:0,callout:Object.assign({},Li.defaults.callout),color:"#fff",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},scaleID:void 0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,value:void 0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0};Fi.descriptors={arrowHeads:{start:{_fallback:!0},end:{_fallback:!0},_fallback:!0}};Fi.defaultRoutes={borderColor:"color"};function Mx(n,{mouseX:t,mouseY:e},i,{hBorderWidth:s,useFinalPosition:r}){let o=Sx(t,e,n.getProps(["x","y","x2","y2"],r),i);return o.value>=o.start-s&&o.value<=o.end+s||hl(n,{mouseX:t,mouseY:e},r,i)}function Cx({x:n,y:t,x2:e,y2:i},{top:s,right:r,bottom:o,left:a}){return!(n<a&&e<a||n>r&&e>r||t<s&&i<s||t>o&&i>o)}function Rf({x:n,y:t},e,{top:i,right:s,bottom:r,left:o}){return n<o&&(t=Tf(o,{x:n,y:t},e),n=o),n>s&&(t=Tf(s,{x:n,y:t},e),n=s),t<i&&(n=ll(i,{x:n,y:t},e),t=i),t>r&&(n=ll(r,{x:n,y:t},e),t=r),{x:n,y:t}}function Px(n,t,e){let{x:i,y:s}=Rf(n,t,e),{x:r,y:o}=Rf(t,n,e);return{x:i,y:s,x2:r,y2:o,width:Math.abs(r-i),height:Math.abs(o-s)}}function Tx(n,{mouseX:t,mouseY:e},i=ni,s){let{x:r,y:o,x2:a,y2:l}=n.getProps(["x","y","x2","y2"],s),c=a-r,h=l-o,f=Ts(c)+Ts(h),g=f===0?-1:((t-r)*c+(e-o)*h)/f,p,m;return g<0?(p=r,m=o):g>1?(p=a,m=l):(p=r+g*c,m=o+g*h),Ts(t-p)+Ts(e-m)<=i}function hl(n,{mouseX:t,mouseY:e},i,s){let r=n.label;return r.options.display&&r.inRange(t,e,s,i)}function Dx(n,t,e){let i=e.borderWidth,s=Vt(e.padding),r=il(n.ctx,e),o=r.width+s.width+i,a=r.height+s.height+i;return Ox(t,e,{width:o,height:a,padding:s},n.chartArea)}function Ex(n){let{x:t,y:e,x2:i,y2:s}=n,r=Math.atan2(s-e,i-t);return r>gt/2?r-gt:r<gt/-2?r+gt:r}function Ox(n,t,e,i){let{width:s,height:r,padding:o}=e,{xAdjust:a,yAdjust:l}=t,c={x:n.x,y:n.y},h={x:n.x2,y:n.y2},f=t.rotation==="auto"?Ex(n):Wt(t.rotation),g=Ax(s,r,f),p=Rx(n,t,{labelSize:g,padding:o},i),m=n.cp?cl(c,n.cp,h,p):al(c,h,p),y={size:g.w,min:i.left,max:i.right,padding:o.left},S={size:g.h,min:i.top,max:i.bottom,padding:o.top},M=Ff(m.x,y)+a,C=Ff(m.y,S)+l;return{x:M-s/2,y:C-r/2,x2:M+s/2,y2:C+r/2,centerX:M,centerY:C,pointX:m.x,pointY:m.y,width:s,height:r,rotation:Yn(f)}}function Ax(n,t,e){let i=Math.cos(e),s=Math.sin(e);return{w:Math.abs(n*i)+Math.abs(t*s),h:Math.abs(n*s)+Math.abs(t*i)}}function Rx(n,t,e,i){let s,r=Lx(n,i);return t.position==="start"?s=Lf({w:n.x2-n.x,h:n.y2-n.y},e,t,r):t.position==="end"?s=1-Lf({w:n.x-n.x2,h:n.y-n.y2},e,t,r):s=Qa(1,t.position),s}function Lf(n,t,e,i){let{labelSize:s,padding:r}=t,o=n.w*i.dx,a=n.h*i.dy,l=o>0&&(s.w/2+r.left-i.x)/o,c=a>0&&(s.h/2+r.top-i.y)/a;return Ur(Math.max(l,c),0,.25)}function Lx(n,t){let{x:e,x2:i,y:s,y2:r}=n,o=Math.min(s,r)-t.top,a=Math.min(e,i)-t.left,l=t.bottom-Math.max(s,r),c=t.right-Math.max(e,i);return{x:Math.min(a,c),y:Math.min(o,l),dx:a<=c?1:-1,dy:o<=l?1:-1}}function Ff(n,t){let{size:e,min:i,max:s,padding:r}=t,o=e/2;return e>s-i?(s+i)/2:(i>=n-r-o&&(n=i+r+o),s<=n+r+o&&(n=s-r-o),n)}function If(n){let t=n.options,e=t.arrowHeads&&t.arrowHeads.start,i=t.arrowHeads&&t.arrowHeads.end;return{startOpts:e,endOpts:i,startAdjust:$f(n,e),endAdjust:$f(n,i)}}function $f(n,t){if(!t||!t.display)return 0;let{length:e,width:i}=t,s=n.options.borderWidth/2,r={x:e,y:i+s};return Math.abs(ll(0,r,{x:0,y:s}))}function ul(n,t,e,i){if(!i||!i.display)return;let{length:s,width:r,fill:o,backgroundColor:a,borderColor:l}=i,c=Math.abs(t-s)+e;n.beginPath(),Ai(n,i),En(n,i),n.moveTo(c,-r),n.lineTo(t+e,0),n.lineTo(c,r),o===!0?(n.fillStyle=a||l,n.closePath(),n.fill(),n.shadowColor="transparent"):n.shadowColor=i.borderShadowColor,n.stroke()}function Fx(n,t,e){let{x:i,y:s,x2:r,y2:o,centerX:a,centerY:l}=n,c=Math.atan2(o-s,r-i),h=tl(t.controlPoint,0),f={x:a+Dn(e,h.x,!1),y:l+Dn(e,h.y,!1)};return si(f,{x:a,y:l},c)}function jf(n,{x:t,y:e},{angle:i,adjust:s},r){!r||!r.display||(n.save(),n.translate(t,e),n.rotate(i),ul(n,0,-s,r),n.restore())}function Ix(n,t,e,i){let{x:s,y:r,x2:o,y2:a,options:l}=t,{startOpts:c,endOpts:h,startAdjust:f,endAdjust:g}=If(t),p={x:s,y:r},m={x:o,y:a},y=Of(p,e,m,0),S=Of(p,e,m,1)-gt,M=cl(p,e,m,f/i),C=cl(p,e,m,1-g/i),F=new Path2D;n.beginPath(),F.moveTo(M.x,M.y),F.quadraticCurveTo(e.x,e.y,C.x,C.y),n.shadowColor=l.borderShadowColor,n.stroke(F),t.path=F,t.ctx=n,jf(n,M,{angle:y,adjust:f},c),jf(n,C,{angle:S,adjust:g},h)}var Ii=class extends qt{inRange(t,e,i,s){let r=this.options.rotation,o=this.options.borderWidth;if(i!=="x"&&i!=="y")return $x({x:t,y:e},this.getProps(["width","height","centerX","centerY"],s),r,o);let{x:a,y:l,x2:c,y2:h}=this.getProps(["x","y","x2","y2"],s),f=o/2,g=i==="y"?{start:l,end:h}:{start:a,end:c},p=si({x:t,y:e},this.getCenterPoint(s),Wt(-r));return p[i]>=g.start-f-ni&&p[i]<=g.end+f+ni}getCenterPoint(t){return Oi(this,t)}draw(t){let{width:e,height:i,centerX:s,centerY:r,options:o}=this;t.save(),nl(t,this.getCenterPoint(),o.rotation),Ai(t,this.options),t.beginPath(),t.fillStyle=o.backgroundColor;let a=En(t,o);t.ellipse(s,r,i/2,e/2,gt/2,0,2*gt),t.fill(),a&&(t.shadowColor=o.borderShadowColor,t.stroke()),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){return vf(t,e,!0)}};Ii.id="ellipseAnnotation";Ii.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,label:Object.assign({},ri.defaults.label),rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0};Ii.defaultRoutes={borderColor:"color",backgroundColor:"color"};Ii.descriptors={label:{_fallback:!0}};function $x(n,t,e,i){let{width:s,height:r,centerX:o,centerY:a}=t,l=s/2,c=r/2;if(l<=0||c<=0)return!1;let h=Wt(e||0),f=i/2||0,g=Math.cos(h),p=Math.sin(h),m=Math.pow(g*(n.x-o)+p*(n.y-a),2),y=Math.pow(p*(n.x-o)-g*(n.y-a),2);return m/Math.pow(l+f,2)+y/Math.pow(c+f,2)<=1.0001}var Ds=class extends qt{inRange(t,e,i,s){let{x:r,y:o,x2:a,y2:l,width:c}=this.getProps(["x","y","x2","y2","width"],s),h=this.options.borderWidth;if(i!=="x"&&i!=="y")return Iy({x:t,y:e},this.getCenterPoint(s),c/2,h);let f=h/2,g=i==="y"?{start:o,end:l,value:e}:{start:r,end:a,value:t};return g.value>=g.start-f&&g.value<=g.end+f}getCenterPoint(t){return Oi(this,t)}draw(t){let e=this.options,i=e.borderWidth;if(e.radius<.1)return;t.save(),t.fillStyle=e.backgroundColor,Ai(t,e);let s=En(t,e);Vy(t,this,this.centerX,this.centerY),s&&!Kr(e.pointStyle)&&(t.shadowColor=e.borderShadowColor,t.stroke()),t.restore(),e.borderWidth=i}resolveElementProperties(t,e){let i=bf(t,e);return i.initProperties=Ps(t,i,e,!0),i}};Ds.id="pointAnnotation";Ds.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,pointStyle:"circle",radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0};Ds.defaultRoutes={borderColor:"color",backgroundColor:"color"};var Es=class extends qt{inRange(t,e,i,s){if(i!=="x"&&i!=="y")return this.options.radius>=.1&&this.elements.length>1&&zx(this.elements,t,e,s);let r=si({x:t,y:e},this.getCenterPoint(s),Wt(-this.options.rotation)),o=this.elements.map(c=>i==="y"?c.bY:c.bX),a=Math.min(...o),l=Math.max(...o);return r[i]>=a&&r[i]<=l}getCenterPoint(t){return Oi(this,t)}draw(t){let{elements:e,options:i}=this;t.save(),t.beginPath(),t.fillStyle=i.backgroundColor,Ai(t,i);let s=En(t,i),r=!0;for(let o of e)r?(t.moveTo(o.x,o.y),r=!1):t.lineTo(o.x,o.y);t.closePath(),t.fill(),s&&(t.shadowColor=i.borderShadowColor,t.stroke()),t.restore()}resolveElementProperties(t,e){let i=bf(t,e),{sides:s,rotation:r}=e,o=[],a=2*gt/s,l=r*Xi;for(let c=0;c<s;c++,l+=a){let h=jx(i,e,l);h.initProperties=Ps(t,i,e),o.push(h)}return i.elements=o,i}};Es.id="polygonAnnotation";Es.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,point:{radius:0},radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,sides:3,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0};Es.defaultRoutes={borderColor:"color",backgroundColor:"color"};function jx({centerX:n,centerY:t},{radius:e,borderWidth:i},s){let r=i/2,o=Math.sin(s),a=Math.cos(s),l={x:n+o*e,y:t-a*e};return{type:"point",optionScope:"point",properties:{x:l.x,y:l.y,centerX:l.x,centerY:l.y,bX:n+o*(e+r),bY:t-a*(e+r)}}}function zx(n,t,e,i){let s=!1,r=n[n.length-1].getProps(["bX","bY"],i);for(let o of n){let a=o.getProps(["bX","bY"],i);a.bY>e!=r.bY>e&&t<(r.bX-a.bX)*(e-a.bY)/(r.bY-a.bY)+a.bX&&(s=!s),r=a}return s}var On={box:ri,ellipse:Ii,label:Li,line:Fi,point:Ds,polygon:Es};Object.keys(On).forEach(n=>{ft.describe(`elements.${On[n].id}`,{_fallback:"plugins.annotation.common"})});var Bx={update:Object.assign},Nx=ol.concat(Zr),zf=(n,t)=>at(t)?pl(n,t):n,fl=n=>n==="color"||n==="font";function dl(n="line"){return On[n]?n:(console.warn(`Unknown annotation type: '${n}', defaulting to 'line'`),"line")}function Hx(n,t,e,i){let s=Vx(n,e.animations,i),r=t.annotations,o=qx(t.elements,r);for(let a=0;a<r.length;a++){let l=r[a],c=Bf(o,a,l.type),h=l.setContext(Xx(n,c,l)),f=c.resolveElementProperties(n,h);f.skip=Wx(f),"elements"in f&&(Yx(c,f.elements,h,s),delete f.elements),Nt(c.x)||Object.assign(c,f),Object.assign(c,f.initProperties),f.options=Nf(h),s.update(c,f)}}function Wx(n){return isNaN(n.x)||isNaN(n.y)}function Vx(n,t,e){return e==="reset"||e==="none"||e==="resize"?Bx:new is(n,t)}function Yx(n,t,e,i){let s=n.elements||(n.elements=[]);s.length=t.length;for(let r=0;r<t.length;r++){let o=t[r],a=o.properties,l=Bf(s,r,o.type,o.initProperties),c=e[o.optionScope].override(o);a.options=Nf(c),i.update(l,a)}}function Bf(n,t,e,i){let s=On[dl(e)],r=n[t];return(!r||!(r instanceof s))&&(r=n[t]=new s,Object.assign(r,i)),r}function Nf(n){let t=On[dl(n.type)],e={};e.id=n.id,e.type=n.type,e.drawTime=n.drawTime,Object.assign(e,pl(n,t.defaults),pl(n,t.defaultRoutes));for(let i of Nx)e[i]=n[i];return e}function pl(n,t){let e={};for(let i of Object.keys(t)){let s=t[i],r=n[i];fl(i)&&pt(r)?e[i]=r.map(o=>zf(o,s)):e[i]=zf(r,s)}return e}function Xx(n,t,e){return t.$context||(t.$context=Object.assign(Object.create(n.getContext()),{element:t,id:e.id,type:"annotation"}))}function qx(n,t){let e=t.length,i=n.length;if(i<e){let s=e-i;n.splice(i,0,...new Array(s))}else i>e&&n.splice(e,i-e);return n}var Gx="2.2.1",An=new Map,Ux=ol.concat(Zr),Hf={id:"annotation",version:Gx,beforeRegister(){$y("chart.js","3.7",se.version)},afterRegister(){se.register(On)},afterUnregister(){se.unregister(On)},beforeInit(n){An.set(n,{annotations:[],elements:[],visibleElements:[],listeners:{},listened:!1,moveListened:!1,hooks:{},hooked:!1,hovered:[]})},beforeUpdate(n,t,e){let i=An.get(n),s=i.annotations=[],r=e.annotations;at(r)?Object.keys(r).forEach(o=>{let a=r[o];at(a)&&(a.id=o,s.push(a))}):pt(r)&&s.push(...r),cx(s,n.scales)},afterDataLimits(n,t){let e=An.get(n);lx(n,t.scale,e.annotations.filter(i=>i.display&&i.adjustScaleRange))},afterUpdate(n,t,e){let i=An.get(n);ix(n,i,e),Hx(n,i,e,t.mode),i.visibleElements=i.elements.filter(s=>!s.skip&&s.options.display),ax(n,i,e)},beforeDatasetsDraw(n,t,e){Jr(n,"beforeDatasetsDraw",e.clip)},afterDatasetsDraw(n,t,e){Jr(n,"afterDatasetsDraw",e.clip)},beforeDraw(n,t,e){Jr(n,"beforeDraw",e.clip)},afterDraw(n,t,e){Jr(n,"afterDraw",e.clip)},beforeEvent(n,t,e){let i=An.get(n);sx(i,t.event,e)&&(t.changed=!0)},afterDestroy(n){An.delete(n)},_getState(n){return An.get(n)},defaults:{animations:{numbers:{properties:["x","y","x2","y2","width","height","centerX","centerY","pointX","pointY","radius"],type:"number"}},clip:!0,interaction:{mode:void 0,axis:void 0,intersect:void 0},common:{drawTime:"afterDatasetsDraw",init:!1,label:{}}},descriptors:{_indexable:!1,_scriptable:n=>!Ux.includes(n)&&n!=="init",annotations:{_allKeys:!1,_fallback:(n,t)=>`elements.${On[dl(t.type)].id}`},interaction:{_fallback:!0},common:{label:{_indexable:fl,_fallback:!0},_indexable:fl}},additionalOptionScopes:[""]};function Jr(n,t,e){let{ctx:i,chartArea:s}=n,r=An.get(n);e&&xn(i,s);let o=Kx(r.visibleElements,t).sort((a,l)=>a.element.options.z-l.element.options.z);for(let a of o)Zx(i,s,r,a);e&&wn(i)}function Kx(n,t){let e=[];for(let i of n)if(i.options.drawTime===t&&e.push({element:i,main:!0}),i.elements&&i.elements.length)for(let s of i.elements)s.options.display&&s.options.drawTime===t&&e.push({element:s});return e}function Zx(n,t,e,i){let s=i.element;i.main?(kf(e,s,"beforeDraw"),s.draw(n,t),kf(e,s,"afterDraw")):s.draw(n,t)}se.register(...Xu,Hf,Ti,Cs);var to=class{constructor(t){this.plugin=t}async datasetPrep(t,e,i=!1){var h,f,g;let s=[];if(!t.id){let p=[];if(this.plugin.settings.themeable||i){let m=1;for(;;){let y=getComputedStyle(e).getPropertyValue(`--chart-color-${m}`);if(y)p.push(y),m++;else break}}for(let m=0;t.series.length>m;m++){let c=t.series[m],{title:y}=c,S=qc(c,["title"]),M=Nn({label:y!=null?y:"",backgroundColor:t.labelColors?p.length?ln(p,t.transparency):ln(this.plugin.settings.colors,t.transparency):p.length?ln(p,t.transparency)[m]:ln(this.plugin.settings.colors,t.transparency)[m],borderColor:t.labelColors?p.length?p:this.plugin.settings.colors:p.length?p[m]:this.plugin.settings.colors[m],borderWidth:1,fill:t.fill?t.stacked?m==0?"origin":"-1":!0:!1,tension:(h=t.tension)!=null?h:0},S);t.type==="sankey"&&(M.colorFrom&&(M.colorFrom=C=>{var F,D;return(D=(F=t.series[m].colorFrom[C.dataset.data[C.dataIndex].from])!=null?F:p[m])!=null?D:"green"}),M.colorTo&&(M.colorTo=C=>{var F,D;return(D=(F=t.series[m].colorTo[C.dataset.data[C.dataIndex].to])!=null?F:p[m])!=null?D:"green"})),s.push(M)}}let r=t.time?{type:"time",time:{unit:t.time}}:null,o=t.labels,a=getComputedStyle(e).getPropertyValue("--background-modifier-border"),l;return se.defaults.color=t.textColor||getComputedStyle(e).getPropertyValue("--text-muted"),se.defaults.font.family=getComputedStyle(e).getPropertyValue("--mermaid-font"),se.defaults.plugins=_i(Nn({},se.defaults.plugins),{legend:_i(Nn({},se.defaults.plugins.legend),{display:(f=t.legend)!=null?f:!0,position:(g=t.legendPosition)!=null?g:"top"})}),se.defaults.layout.padding=t.padding,t.type=="radar"||t.type=="polarArea"?l={type:t.type,data:{labels:o,datasets:s},options:{animation:{duration:0},scales:{r:_i(Nn({},r),{grid:{color:a},beginAtZero:t.beginAtZero,max:t.rMax,min:t.rMin,ticks:{backdropColor:a}})}}}:t.type=="bar"||t.type=="line"?l={type:t.type,data:{labels:o,datasets:s},options:{animation:{duration:0},indexAxis:t.indexAxis,spanGaps:t.spanGaps,scales:{y:{min:t.yMin,max:t.yMax,reverse:t.yReverse,ticks:{display:t.yTickDisplay,padding:t.yTickPadding},display:t.yDisplay,stacked:t.stacked,beginAtZero:t.beginAtZero,grid:{color:a},title:{display:t.yTitle,text:t.yTitle}},x:_i(Nn({},r),{min:t.xMin,max:t.xMax,reverse:t.xReverse,ticks:{display:t.xTickDisplay,padding:t.xTickPadding},display:t.xDisplay,stacked:t.stacked,grid:{color:a},title:{display:t.xTitle,text:t.xTitle}})}}}:t.type==="sankey"?(s=s.map(p=>_i(Nn({},p),{data:p.data.map(m=>Array.isArray(m)&&m.length===3?{from:m[0],flow:m[1],to:m[2]}:m)})),l={type:t.type,data:{labels:o,datasets:s},options:{animation:{duration:0}}}):l={type:t.type,data:{labels:o,datasets:s},options:{animation:{duration:0},spanGaps:t.spanGaps}},{chartOptions:l,width:t.width}}async imageRenderer(t,e){let i=l=>new Promise(c=>setTimeout(c,l)),s=document.createElement("canvas"),r=s.getContext("2d"),o=await this.datasetPrep(await(0,Qr.parseYaml)(t.replace("```chart","").replace("```","").replace(/\t/g,"    ")),document.body);new se(r,o.chartOptions),document.body.append(s),await i(250);let a=s.toDataURL(e.format,e.quality);return document.body.removeChild(s),a.substring(a.indexOf(",")+1)}renderRaw(t,e){var s;let i=e.createEl("canvas");if(t.chartOptions)try{let r=new se(i.getContext("2d"),t.chartOptions);return i.parentElement.style.width=(s=t.width)!=null?s:"100%",i.parentElement.style.margin="auto",r}catch(r){return cn(r,e),null}else try{return new se(i.getContext("2d"),t)}catch(r){return cn(r,e),null}}async renderFromYaml(t,e,i){this.plugin.app.workspace.onLayoutReady(()=>i.addChild(new Wf(t,e,this,i.sourcePath)))}},Wf=class extends Qr.MarkdownRenderChild{constructor(t,e,i,s){super(e);this.el=e,this.data=t,this.renderer=i,this.ownPath=s,this.changeHandler=this.changeHandler.bind(this),this.reload=this.reload.bind(this)}async onload(){var t,e,i,s;try{let r=await this.renderer.datasetPrep(this.data,this.el),o={};if(this.data.id){let a=[];if(this.renderer.plugin.settings.themeable){let g=1;for(;;){let p=getComputedStyle(this.el).getPropertyValue(`--chart-color-${g}`);if(p)a.push(p),g++;else break}}o.datasets=[];let l;this.data.file&&(l=this.renderer.plugin.app.metadataCache.getFirstLinkpathDest(this.data.file,this.renderer.plugin.app.workspace.getActiveFile().path));let c=(t=this.renderer.plugin.app.metadataCache.getFileCache(l!=null?l:this.renderer.plugin.app.vault.getAbstractFileByPath(this.ownPath)).sections.find(g=>g.id===this.data.id))==null?void 0:t.position;if(!c)throw"Invalid id and/or file";let h=(await this.renderer.plugin.app.vault.cachedRead(this.data.file?l:this.renderer.plugin.app.vault.getAbstractFileByPath(this.ownPath))).substring(c.start.offset,c.end.offset),f;try{f=Za(h,(e=this.data.layout)!=null?e:"columns",this.data.select)}catch(g){throw"There is no table at that id and/or file"}o.labels=f.labels;for(let g=0;f.dataFields.length>g;g++)o.datasets.push({label:(i=f.dataFields[g].dataTitle)!=null?i:"",data:f.dataFields[g].data,backgroundColor:this.data.labelColors?a.length?ln(a,this.data.transparency):ln(this.renderer.plugin.settings.colors,this.data.transparency):a.length?ln(a,this.data.transparency)[g]:ln(this.renderer.plugin.settings.colors,this.data.transparency)[g],borderColor:this.data.labelColors?a.length?a:this.renderer.plugin.settings.colors:a.length?a[g]:this.renderer.plugin.settings.colors[g],borderWidth:1,fill:this.data.fill?this.data.stacked?g==0?"origin":"-1":!0:!1,tension:(s=this.data.tension)!=null?s:0});r.chartOptions.data.labels=o.labels,r.chartOptions.data.datasets=o.datasets}this.chart=this.renderer.renderRaw(r,this.containerEl)}catch(r){cn(r,this.el)}this.data.id&&this.renderer.plugin.app.metadataCache.on("changed",this.changeHandler),this.renderer.plugin.app.workspace.on("css-change",this.reload)}changeHandler(t){(this.data.file?t.basename===this.data.file:t.path===this.ownPath)&&this.reload()}reload(){this.onunload(),this.onload()}onunload(){this.renderer.plugin.app.metadataCache.off("changed",this.changeHandler),this.renderer.plugin.app.workspace.off("css-change",this.reload),this.el.empty(),this.chart&&this.chart.destroy(),this.chart=null}};var eo={colors:["rgba(255, 99, 132, 1)","rgba(54, 162, 235, 1)","rgba(255, 206, 86, 1)","rgba(75, 192, 192, 1)","rgba(153, 102, 255, 1)","rgba(255, 159, 64, 1)"],contextMenu:!0,imageSettings:{format:"image/png",quality:.92},themeable:!1};var Ye=ze(require("obsidian"));var gl=function(n,t){if(!(n instanceof t))throw new TypeError("Cannot call a class as a function")},ml=function(){function n(t,e){for(var i=0;i<e.length;i++){var s=e[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,s.key,s)}}return function(t,e,i){return e&&n(t.prototype,e),i&&n(t,i),t}}(),no=function(){function n(t,e){var i=[],s=!0,r=!1,o=void 0;try{for(var a=t[Symbol.iterator](),l;!(s=(l=a.next()).done)&&(i.push(l.value),!(e&&i.length===e));s=!0);}catch(c){r=!0,o=c}finally{try{!s&&a.return&&a.return()}finally{if(r)throw o}}return i}return function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return n(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}();String.prototype.startsWith=String.prototype.startsWith||function(n){return this.indexOf(n)===0};String.prototype.padStart=String.prototype.padStart||function(n,t){for(var e=this;e.length<n;)e=t+e;return e};var Jx={cb:"0f8ff",tqw:"aebd7",q:"-ffff",qmrn:"7fffd4",zr:"0ffff",bg:"5f5dc",bsq:"e4c4",bck:"---",nch:"ebcd",b:"--ff",bvt:"8a2be2",brwn:"a52a2a",brw:"deb887",ctb:"5f9ea0",hrt:"7fff-",chcT:"d2691e",cr:"7f50",rnw:"6495ed",crns:"8dc",crms:"dc143c",cn:"-ffff",Db:"--8b",Dcn:"-8b8b",Dgnr:"b8860b",Dgr:"a9a9a9",Dgrn:"-64-",Dkhk:"bdb76b",Dmgn:"8b-8b",Dvgr:"556b2f",Drng:"8c-",Drch:"9932cc",Dr:"8b--",Dsmn:"e9967a",Dsgr:"8fbc8f",DsTb:"483d8b",DsTg:"2f4f4f",Dtrq:"-ced1",Dvt:"94-d3",ppnk:"1493",pskb:"-bfff",mgr:"696969",grb:"1e90ff",rbrc:"b22222",rwht:"af0",stg:"228b22",chs:"-ff",gnsb:"dcdcdc",st:"8f8ff",g:"d7-",gnr:"daa520",gr:"808080",grn:"-8-0",grnw:"adff2f",hnw:"0fff0",htpn:"69b4",nnr:"cd5c5c",ng:"4b-82",vr:"0",khk:"0e68c",vnr:"e6e6fa",nrb:"0f5",wngr:"7cfc-",mnch:"acd",Lb:"add8e6",Lcr:"08080",Lcn:"e0ffff",Lgnr:"afad2",Lgr:"d3d3d3",Lgrn:"90ee90",Lpnk:"b6c1",Lsmn:"a07a",Lsgr:"20b2aa",Lskb:"87cefa",LsTg:"778899",Lstb:"b0c4de",Lw:"e0",m:"-ff-",mgrn:"32cd32",nn:"af0e6",mgnt:"-ff",mrn:"8--0",mqm:"66cdaa",mmb:"--cd",mmrc:"ba55d3",mmpr:"9370db",msg:"3cb371",mmsT:"7b68ee","":"-fa9a",mtr:"48d1cc",mmvt:"c71585",mnLb:"191970",ntc:"5fffa",mstr:"e4e1",mccs:"e4b5",vjw:"dead",nv:"--80",c:"df5e6",v:"808-0",vrb:"6b8e23",rng:"a5-",rngr:"45-",rch:"da70d6",pgnr:"eee8aa",pgrn:"98fb98",ptrq:"afeeee",pvtr:"db7093",ppwh:"efd5",pchp:"dab9",pr:"cd853f",pnk:"c0cb",pm:"dda0dd",pwrb:"b0e0e6",prp:"8-080",cc:"663399",r:"--",sbr:"bc8f8f",rb:"4169e1",sbrw:"8b4513",smn:"a8072",nbr:"4a460",sgrn:"2e8b57",ssh:"5ee",snn:"a0522d",svr:"c0c0c0",skb:"87ceeb",sTb:"6a5acd",sTgr:"708090",snw:"afa",n:"-ff7f",stb:"4682b4",tn:"d2b48c",t:"-8080",thst:"d8bfd8",tmT:"6347",trqs:"40e0d0",vt:"ee82ee",whT:"5deb3",wht:"",hts:"5f5f5",w:"-",wgrn:"9acd32"};function Vf(n){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,e=t>0?n.toFixed(t).replace(/0+$/,"").replace(/\.$/,""):n.toString();return e||"0"}var Qx=function(){function n(t,e,i,s){gl(this,n);var r=this;function o(l){if(l.startsWith("hsl")){var c=l.match(/([\-\d\.e]+)/g).map(Number),h=no(c,4),f=h[0],g=h[1],p=h[2],m=h[3];m===void 0&&(m=1),f/=360,g/=100,p/=100,r.hsla=[f,g,p,m]}else if(l.startsWith("rgb")){var y=l.match(/([\-\d\.e]+)/g).map(Number),S=no(y,4),M=S[0],C=S[1],F=S[2],D=S[3];D===void 0&&(D=1),r.rgba=[M,C,F,D]}else l.startsWith("#")?r.rgba=n.hexToRgb(l):r.rgba=n.nameToRgb(l)||n.hexToRgb(l)}if(t!==void 0)if(Array.isArray(t))this.rgba=t;else if(i===void 0){var a=t&&""+t;a&&o(a.toLowerCase())}else this.rgba=[t,e,i,s===void 0?1:s]}return ml(n,[{key:"printRGB",value:function(e){var i=e?this.rgba:this.rgba.slice(0,3),s=i.map(function(r,o){return Vf(r,o===3?3:0)});return e?"rgba("+s+")":"rgb("+s+")"}},{key:"printHSL",value:function(e){var i=[360,100,100,1],s=["","%","%",""],r=e?this.hsla:this.hsla.slice(0,3),o=r.map(function(a,l){return Vf(a*i[l],l===3?3:1)+s[l]});return e?"hsla("+o+")":"hsl("+o+")"}},{key:"printHex",value:function(e){var i=this.hex;return e?i:i.substring(0,7)}},{key:"rgba",get:function(){if(this._rgba)return this._rgba;if(!this._hsla)throw new Error("No color is set");return this._rgba=n.hslToRgb(this._hsla)},set:function(e){e.length===3&&(e[3]=1),this._rgba=e,this._hsla=null}},{key:"rgbString",get:function(){return this.printRGB()}},{key:"rgbaString",get:function(){return this.printRGB(!0)}},{key:"hsla",get:function(){if(this._hsla)return this._hsla;if(!this._rgba)throw new Error("No color is set");return this._hsla=n.rgbToHsl(this._rgba)},set:function(e){e.length===3&&(e[3]=1),this._hsla=e,this._rgba=null}},{key:"hslString",get:function(){return this.printHSL()}},{key:"hslaString",get:function(){return this.printHSL(!0)}},{key:"hex",get:function(){var e=this.rgba,i=e.map(function(s,r){return r<3?s.toString(16):Math.round(s*255).toString(16)});return"#"+i.map(function(s){return s.padStart(2,"0")}).join("")},set:function(e){this.rgba=n.hexToRgb(e)}}],[{key:"hexToRgb",value:function(e){var i=(e.startsWith("#")?e.slice(1):e).replace(/^(\w{3})$/,"$1F").replace(/^(\w)(\w)(\w)(\w)$/,"$1$1$2$2$3$3$4$4").replace(/^(\w{6})$/,"$1FF");if(!i.match(/^([0-9a-fA-F]{8})$/))throw new Error("Unknown hex color; "+e);var s=i.match(/^(\w\w)(\w\w)(\w\w)(\w\w)$/).slice(1).map(function(r){return parseInt(r,16)});return s[3]=s[3]/255,s}},{key:"nameToRgb",value:function(e){var i=e.toLowerCase().replace("at","T").replace(/[aeiouyldf]/g,"").replace("ght","L").replace("rk","D").slice(-5,4),s=Jx[i];return s===void 0?s:n.hexToRgb(s.replace(/\-/g,"00").padStart(6,"f"))}},{key:"rgbToHsl",value:function(e){var i=no(e,4),s=i[0],r=i[1],o=i[2],a=i[3];s/=255,r/=255,o/=255;var l=Math.max(s,r,o),c=Math.min(s,r,o),h=void 0,f=void 0,g=(l+c)/2;if(l===c)h=f=0;else{var p=l-c;switch(f=g>.5?p/(2-l-c):p/(l+c),l){case s:h=(r-o)/p+(r<o?6:0);break;case r:h=(o-s)/p+2;break;case o:h=(s-r)/p+4;break}h/=6}return[h,f,g,a]}},{key:"hslToRgb",value:function(e){var i=no(e,4),s=i[0],r=i[1],o=i[2],a=i[3],l=void 0,c=void 0,h=void 0;if(r===0)l=c=h=o;else{var f=function(S,M,C){return C<0&&(C+=1),C>1&&(C-=1),C<1/6?S+(M-S)*6*C:C<1/2?M:C<2/3?S+(M-S)*(2/3-C)*6:S},g=o<.5?o*(1+r):o+r-o*r,p=2*o-g;l=f(p,g,s+1/3),c=f(p,g,s),h=f(p,g,s-1/3)}var m=[l*255,c*255,h*255].map(Math.round);return m[3]=a,m}}]),n}(),t1=function(){function n(){gl(this,n),this._events=[]}return ml(n,[{key:"add",value:function(e,i,s){e.addEventListener(i,s,!1),this._events.push({target:e,type:i,handler:s})}},{key:"remove",value:function(e,i,s){this._events=this._events.filter(function(r){var o=!0;return e&&e!==r.target&&(o=!1),i&&i!==r.type&&(o=!1),s&&s!==r.handler&&(o=!1),o&&n._doRemove(r.target,r.type,r.handler),!o})}},{key:"destroy",value:function(){this._events.forEach(function(e){return n._doRemove(e.target,e.type,e.handler)}),this._events=[]}}],[{key:"_doRemove",value:function(e,i,s){e.removeEventListener(i,s,!1)}}]),n}();function e1(n){var t=document.createElement("div");return t.innerHTML=n,t.firstElementChild}function bl(n,t,e){var i=!1;function s(l,c,h){return Math.max(c,Math.min(l,h))}function r(l,c,h){if(h&&(i=!0),!!i){l.preventDefault();var f=t.getBoundingClientRect(),g=f.width,p=f.height,m=c.clientX,y=c.clientY,S=s(m-f.left,0,g),M=s(y-f.top,0,p);e(S/g,M/p)}}function o(l,c){var h=l.buttons===void 0?l.which:l.buttons;h===1?r(l,l,c):i=!1}function a(l,c){l.touches.length===1?r(l,l.touches[0],c):i=!1}n.add(t,"mousedown",function(l){o(l,!0)}),n.add(t,"touchstart",function(l){a(l,!0)}),n.add(window,"mousemove",o),n.add(t,"touchmove",a),n.add(window,"mouseup",function(l){i=!1}),n.add(t,"touchend",function(l){i=!1}),n.add(t,"touchcancel",function(l){i=!1})}var n1=`linear-gradient(45deg, lightgrey 25%, transparent 25%, transparent 75%, lightgrey 75%) 0 0 / 2em 2em,
                   linear-gradient(45deg, lightgrey 25%,       white 25%,       white 75%, lightgrey 75%) 1em 1em / 2em 2em`,i1=360,Yf="keydown",io="mousedown",vl="focusin";function Ve(n,t){return(t||document).querySelector(n)}function Xf(n){n.preventDefault(),n.stopPropagation()}function _l(n,t,e,i,s){n.add(t,Yf,function(r){e.indexOf(r.key)>=0&&(s&&Xf(r),i(r))})}var yl=function(){function n(t){gl(this,n),this.settings={popup:"right",layout:"default",alpha:!0,editor:!0,editorFormat:"hex",cancelButton:!1,defaultColor:"#0cf"},this._events=new t1,this.onChange=null,this.onDone=null,this.onOpen=null,this.onClose=null,this.setOptions(t)}return ml(n,[{key:"setOptions",value:function(e){var i=this;if(!e)return;var s=this.settings;function r(c,h,f){for(var g in c)f&&f.indexOf(g)>=0||(h[g]=c[g])}if(e instanceof HTMLElement)s.parent=e;else{s.parent&&e.parent&&s.parent!==e.parent&&(this._events.remove(s.parent),this._popupInited=!1),r(e,s),e.onChange&&(this.onChange=e.onChange),e.onDone&&(this.onDone=e.onDone),e.onOpen&&(this.onOpen=e.onOpen),e.onClose&&(this.onClose=e.onClose);var o=e.color||e.colour;o&&this._setColor(o)}var a=s.parent;if(a&&s.popup&&!this._popupInited){var l=function(h){return i.openHandler(h)};this._events.add(a,"click",l),_l(this._events,a,[" ","Spacebar","Enter"],l),this._popupInited=!0}else e.parent&&!s.popup&&this.show()}},{key:"openHandler",value:function(e){if(this.show()){e&&e.preventDefault(),this.settings.parent.style.pointerEvents="none";var i=e&&e.type===Yf?this._domEdit:this.domElement;setTimeout(function(){return i.focus()},100),this.onOpen&&this.onOpen(this.colour)}}},{key:"closeHandler",value:function(e){var i=e&&e.type,s=!1;if(!e)s=!0;else if(i===io||i===vl){var r=(this.__containedEvent||0)+100;e.timeStamp>r&&(s=!0)}else Xf(e),s=!0;s&&this.hide()&&(this.settings.parent.style.pointerEvents="",i!==io&&this.settings.parent.focus(),this.onClose&&this.onClose(this.colour))}},{key:"movePopup",value:function(e,i){this.closeHandler(),this.setOptions(e),i&&this.openHandler()}},{key:"setColor",value:function(e,i){this._setColor(e,{silent:i})}},{key:"_setColor",value:function(e,i){if(typeof e=="string"&&(e=e.trim()),!!e){i=i||{};var s=void 0;try{s=new Qx(e)}catch(o){if(i.failSilently)return;throw o}if(!this.settings.alpha){var r=s.hsla;r[3]=1,s.hsla=r}this.colour=this.color=s,this._setHSLA(null,null,null,null,i)}}},{key:"setColour",value:function(e,i){this.setColor(e,i)}},{key:"show",value:function(){var e=this.settings.parent;if(!e)return!1;if(this.domElement){var i=this._toggleDOM(!0);return this._setPosition(),i}var s=this.settings.template||'<div class="picker_wrapper" tabindex="-1"><div class="picker_arrow"></div><div class="picker_hue picker_slider"><div class="picker_selector"></div></div><div class="picker_sl"><div class="picker_selector"></div></div><div class="picker_alpha picker_slider"><div class="picker_selector"></div></div><div class="picker_editor"><input aria-label="Type a color name or hex value"/></div><div class="picker_sample"></div><div class="picker_done"><button>Ok</button></div><div class="picker_cancel"><button>Cancel</button></div></div>',r=e1(s);return this.domElement=r,this._domH=Ve(".picker_hue",r),this._domSL=Ve(".picker_sl",r),this._domA=Ve(".picker_alpha",r),this._domEdit=Ve(".picker_editor input",r),this._domSample=Ve(".picker_sample",r),this._domOkay=Ve(".picker_done button",r),this._domCancel=Ve(".picker_cancel button",r),r.classList.add("layout_"+this.settings.layout),this.settings.alpha||r.classList.add("no_alpha"),this.settings.editor||r.classList.add("no_editor"),this.settings.cancelButton||r.classList.add("no_cancel"),this._ifPopup(function(){return r.classList.add("popup")}),this._setPosition(),this.colour?this._updateUI():this._setColor(this.settings.defaultColor),this._bindEvents(),!0}},{key:"hide",value:function(){return this._toggleDOM(!1)}},{key:"destroy",value:function(){this._events.destroy(),this.domElement&&this.settings.parent.removeChild(this.domElement)}},{key:"_bindEvents",value:function(){var e=this,i=this,s=this.domElement,r=this._events;function o(c,h,f){r.add(c,h,f)}o(s,"click",function(c){return c.preventDefault()}),bl(r,this._domH,function(c,h){return i._setHSLA(c)}),bl(r,this._domSL,function(c,h){return i._setHSLA(null,c,1-h)}),this.settings.alpha&&bl(r,this._domA,function(c,h){return i._setHSLA(null,null,null,1-h)});var a=this._domEdit;o(a,"input",function(c){i._setColor(this.value,{fromEditor:!0,failSilently:!0})}),o(a,"focus",function(c){var h=this;h.selectionStart===h.selectionEnd&&h.select()}),this._ifPopup(function(){var c=function(g){return e.closeHandler(g)};o(window,io,c),o(window,vl,c),_l(r,s,["Esc","Escape"],c);var h=function(g){e.__containedEvent=g.timeStamp};o(s,io,h),o(s,vl,h),o(e._domCancel,"click",c)});var l=function(h){e._ifPopup(function(){return e.closeHandler(h)}),e.onDone&&e.onDone(e.colour)};o(this._domOkay,"click",l),_l(r,s,["Enter"],l)}},{key:"_setPosition",value:function(){var e=this.settings.parent,i=this.domElement;e!==i.parentNode&&e.appendChild(i),this._ifPopup(function(s){getComputedStyle(e).position==="static"&&(e.style.position="relative");var r=s===!0?"popup_right":"popup_"+s;["popup_top","popup_bottom","popup_left","popup_right"].forEach(function(o){o===r?i.classList.add(o):i.classList.remove(o)}),i.classList.add(r)})}},{key:"_setHSLA",value:function(e,i,s,r,o){o=o||{};var a=this.colour,l=a.hsla;[e,i,s,r].forEach(function(c,h){(c||c===0)&&(l[h]=c)}),a.hsla=l,this._updateUI(o),this.onChange&&!o.silent&&this.onChange(a)}},{key:"_updateUI",value:function(e){if(!this.domElement)return;e=e||{};var i=this.colour,s=i.hsla,r="hsl("+s[0]*i1+", 100%, 50%)",o=i.hslString,a=i.hslaString,l=this._domH,c=this._domSL,h=this._domA,f=Ve(".picker_selector",l),g=Ve(".picker_selector",c),p=Ve(".picker_selector",h);function m($,N,G){N.style.left=G*100+"%"}function y($,N,G){N.style.top=G*100+"%"}m(l,f,s[0]),this._domSL.style.backgroundColor=this._domH.style.color=r,m(c,g,s[1]),y(c,g,1-s[2]),c.style.color=o,y(h,p,1-s[3]);var S=o,M=S.replace("hsl","hsla").replace(")",", 0)"),C="linear-gradient("+[S,M]+")";if(this._domA.style.background=C+", "+n1,!e.fromEditor){var F=this.settings.editorFormat,D=this.settings.alpha,I=void 0;switch(F){case"rgb":I=i.printRGB(D);break;case"hsl":I=i.printHSL(D);break;default:I=i.printHex(D)}this._domEdit.value=I}this._domSample.style.color=a}},{key:"_ifPopup",value:function(e,i){this.settings.parent&&this.settings.popup?e&&e(this.settings.popup):i&&i()}},{key:"_toggleDOM",value:function(e){var i=this.domElement;if(!i)return!1;var s=e?"":"none",r=i.style.display!==s;return r&&(i.style.display=s),r}}]),n}();so=document.createElement("style"),so.textContent='.picker_wrapper.no_alpha .picker_alpha{display:none}.picker_wrapper.no_editor .picker_editor{position:absolute;z-index:-1;opacity:0}.picker_wrapper.no_cancel .picker_cancel{display:none}.layout_default.picker_wrapper{display:flex;flex-flow:row wrap;justify-content:space-between;align-items:stretch;font-size:10px;width:25em;padding:.5em}.layout_default.picker_wrapper input,.layout_default.picker_wrapper button{font-size:1rem}.layout_default.picker_wrapper>*{margin:.5em}.layout_default.picker_wrapper::before{content:"";display:block;width:100%;height:0;order:1}.layout_default .picker_slider,.layout_default .picker_selector{padding:1em}.layout_default .picker_hue{width:100%}.layout_default .picker_sl{flex:1 1 auto}.layout_default .picker_sl::before{content:"";display:block;padding-bottom:100%}.layout_default .picker_editor{order:1;width:6.5rem}.layout_default .picker_editor input{width:100%;height:100%}.layout_default .picker_sample{order:1;flex:1 1 auto}.layout_default .picker_done,.layout_default .picker_cancel{order:1}.picker_wrapper{box-sizing:border-box;background:#f2f2f2;box-shadow:0 0 0 1px silver;cursor:default;font-family:sans-serif;color:#444;pointer-events:auto}.picker_wrapper:focus{outline:none}.picker_wrapper button,.picker_wrapper input{box-sizing:border-box;border:none;box-shadow:0 0 0 1px silver;outline:none}.picker_wrapper button:focus,.picker_wrapper button:active,.picker_wrapper input:focus,.picker_wrapper input:active{box-shadow:0 0 2px 1px #1e90ff}.picker_wrapper button{padding:.4em .6em;cursor:pointer;background-color:#f5f5f5;background-image:linear-gradient(0deg, gainsboro, transparent)}.picker_wrapper button:active{background-image:linear-gradient(0deg, transparent, gainsboro)}.picker_wrapper button:hover{background-color:#fff}.picker_selector{position:absolute;z-index:1;display:block;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);border:2px solid #fff;border-radius:100%;box-shadow:0 0 3px 1px #67b9ff;background:currentColor;cursor:pointer}.picker_slider .picker_selector{border-radius:2px}.picker_hue{position:relative;background-image:linear-gradient(90deg, red, yellow, lime, cyan, blue, magenta, red);box-shadow:0 0 0 1px silver}.picker_sl{position:relative;box-shadow:0 0 0 1px silver;background-image:linear-gradient(180deg, white, rgba(255, 255, 255, 0) 50%),linear-gradient(0deg, black, rgba(0, 0, 0, 0) 50%),linear-gradient(90deg, #808080, rgba(128, 128, 128, 0))}.picker_alpha,.picker_sample{position:relative;background:linear-gradient(45deg, lightgrey 25%, transparent 25%, transparent 75%, lightgrey 75%) 0 0/2em 2em,linear-gradient(45deg, lightgrey 25%, white 25%, white 75%, lightgrey 75%) 1em 1em/2em 2em;box-shadow:0 0 0 1px silver}.picker_alpha .picker_selector,.picker_sample .picker_selector{background:none}.picker_editor input{font-family:monospace;padding:.2em .4em}.picker_sample::before{content:"";position:absolute;display:block;width:100%;height:100%;background:currentColor}.picker_arrow{position:absolute;z-index:-1}.picker_wrapper.popup{position:absolute;z-index:2;margin:1.5em}.picker_wrapper.popup,.picker_wrapper.popup .picker_arrow::before,.picker_wrapper.popup .picker_arrow::after{background:#f2f2f2;box-shadow:0 0 10px 1px rgba(0,0,0,.4)}.picker_wrapper.popup .picker_arrow{width:3em;height:3em;margin:0}.picker_wrapper.popup .picker_arrow::before,.picker_wrapper.popup .picker_arrow::after{content:"";display:block;position:absolute;top:0;left:0;z-index:-99}.picker_wrapper.popup .picker_arrow::before{width:100%;height:100%;-webkit-transform:skew(45deg);transform:skew(45deg);-webkit-transform-origin:0 100%;transform-origin:0 100%}.picker_wrapper.popup .picker_arrow::after{width:150%;height:150%;box-shadow:none}.popup.popup_top{bottom:100%;left:0}.popup.popup_top .picker_arrow{bottom:0;left:0;-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}.popup.popup_bottom{top:100%;left:0}.popup.popup_bottom .picker_arrow{top:0;left:0;-webkit-transform:rotate(90deg) scale(1, -1);transform:rotate(90deg) scale(1, -1)}.popup.popup_left{top:0;right:100%}.popup.popup_left .picker_arrow{top:0;right:0;-webkit-transform:scale(-1, 1);transform:scale(-1, 1)}.popup.popup_right{top:0;left:100%}.popup.popup_right .picker_arrow{top:0;left:0}',document.documentElement.firstElementChild.appendChild(so),yl.StyleElement=so;var so;var xl=class extends Ye.PluginSettingTab{constructor(t,e){super(t,e);this.plugin=e}isColor(t){var e=new Option().style;return e.color=t,e.color==t}display(){let{containerEl:t,plugin:e}=this;t.empty(),t.createEl("h2",{text:"Settings - Charts"}),t.createEl("h3",{text:"General"}),new Ye.Setting(t).setName("Show Button in Context Menu").setDesc("If enabled, you will se a Button in your Editor Context Menu to open the Chart Creator.").addToggle(r=>{r.setValue(this.plugin.settings.contextMenu).onChange(async o=>{e.settings.contextMenu=o,await e.saveSettings()})}),new Ye.Setting(t).setName("Donate").setDesc("If you like this Plugin, consider donating to support continued development:").addButton(r=>{r.buttonEl.outerHTML='<a href="https://ko-fi.com/phibr0"><img src="https://uploads-ssl.webflow.com/5c14e387dab576fe667689cf/61e11e22d8ff4a5b4a1b3346_Supportbutton-1.png"></a>'}),t.createEl("h3",{text:"Colors",attr:{style:"margin-bottom: 0"}});let i=t.createEl("p",{cls:"setting-item-description"});i.append("Set the Colors for your Charts. This will set the border Color and the inner Color will be the same, but with less opacity. This ensures better compatibility with Dark and Light Mode. ","You can use any ",i.createEl("a",{href:"https://www.w3schools.com/cssref/css_colors.asp",text:"valid CSS Color."})),new Ye.Setting(t).setName("Enable Theme Colors").setDesc("If your Obsidian Theme (or snippet) provides Colors you can use them instead.").addToggle(r=>{r.setValue(e.settings.themeable).onChange(async o=>{e.settings.themeable=o,await e.saveSettings(),this.display()})}),e.settings.themeable||(e.settings.colors.forEach((r,o)=>{let a=document.createDocumentFragment();a.createSpan({text:"\u25CF",attr:{style:`color: ${r}`}}),a.appendText(` Color #${o+1}`),new Ye.Setting(t).setName(a).setDesc("This will be the border Color used in the Charts you create.").addButton(l=>{l.setButtonText("Change Color"),new yl({parent:l.buttonEl,onDone:async c=>{this.plugin.settings.colors[o]=c.hex,await this.plugin.saveSettings(),this.display()},popup:"left",color:r,alpha:!1})}).addExtraButton(l=>{l.setIcon("trash").setTooltip("Remove").onClick(async()=>{this.plugin.settings.colors.remove(r),await this.plugin.saveSettings(),this.display()}),this.plugin.settings.colors.length===1&&l.setDisabled(!0)}).addExtraButton(l=>{l.setIcon("reset").setTooltip("Reset to default").onClick(async()=>{var c;this.plugin.settings.colors[o]=(c=eo.colors[o])!=null?c:"#ffffff",await this.plugin.saveSettings(),this.display()})})}),new Ye.Setting(t).addButton(r=>{r.setButtonText("Add Color").onClick(async()=>{this.plugin.settings.colors.push("#ffffff"),await this.plugin.saveSettings(),this.display()})})),t.createEl("h3",{text:"Chart to Image Converter"});let s=t.createEl("details");s.createEl("summary",{text:"How to use"}),s.createEl("img",{attr:{src:"https://media.discordapp.net/attachments/855181471643861002/897811615037136966/charttoimage.gif"}}),new Ye.Setting(t).setName("Image Format").setDesc("The Format to be used, when generating a Image from a Chart.").addDropdown(r=>{r.addOptions({"image/jpeg":"jpeg","image/png":"png","image/webp":"webp"}),r.setValue(e.settings.imageSettings.format),r.onChange(async o=>{e.settings.imageSettings.format=o,await e.saveSettings()})}),new Ye.Setting(t).setName("Image Quality").setDesc("If using a lossy format, set the Image Quality.").addSlider(r=>{r.setDynamicTooltip().setLimits(.01,1,.01).setValue(e.settings.imageSettings.quality).onChange(async o=>{e.settings.imageSettings.quality=o,await e.saveSettings()})})}};var pd=ze(require("obsidian"));function wl(){}function s1(n,t){for(let e in t)n[e]=t[e];return n}function kl(n){return n()}function qf(){return Object.create(null)}function hn(n){n.forEach(kl)}function Gf(n){return typeof n=="function"}function ro(n,t){return n!=n?t==t:n!==t||n&&typeof n=="object"||typeof n=="function"}function Uf(n){return Object.keys(n).length===0}function Kf(n,t,e,i){if(n){let s=Zf(n,t,e,i);return n[0](s)}}function Zf(n,t,e,i){return n[1]&&i?s1(e.ctx.slice(),n[1](i(t))):e.ctx}function Jf(n,t,e,i){if(n[2]&&i){let s=n[2](i(e));if(t.dirty===void 0)return s;if(typeof s=="object"){let r=[],o=Math.max(t.dirty.length,s.length);for(let a=0;a<o;a+=1)r[a]=t.dirty[a]|s[a];return r}return t.dirty|s}return t.dirty}function Qf(n,t,e,i,s,r){if(s){let o=Zf(t,e,i,r);n.p(o,s)}}function td(n){if(n.ctx.length>32){let t=[],e=n.ctx.length/32;for(let i=0;i<e;i++)t[i]=-1;return t}return-1}var mw=new Set;var ed=!1;function r1(){ed=!0}function o1(){ed=!1}function z(n,t){n.appendChild(t)}function oo(n,t,e){let i=a1(n);if(!i.getElementById(t)){let s=Y("style");s.id=t,s.textContent=e,l1(i,s)}}function a1(n){if(!n)return document;let t=n.getRootNode?n.getRootNode():n.ownerDocument;return t&&t.host?t:n.ownerDocument}function l1(n,t){z(n.head||n,t)}function Xe(n,t,e){n.insertBefore(t,e||null)}function Ie(n){n.parentNode.removeChild(n)}function nd(n,t){for(let e=0;e<n.length;e+=1)n[e]&&n[e].d(t)}function Y(n){return document.createElement(n)}function ao(n){return document.createElementNS("http://www.w3.org/2000/svg",n)}function Sl(n){return document.createTextNode(n)}function It(){return Sl(" ")}function Jt(n,t,e,i){return n.addEventListener(t,e,i),()=>n.removeEventListener(t,e,i)}function X(n,t,e){e==null?n.removeAttribute(t):n.getAttribute(t)!==e&&n.setAttribute(t,e)}function Ml(n){return n===""?null:+n}function c1(n){return Array.from(n.childNodes)}function id(n,t){t=""+t,n.wholeText!==t&&(n.data=t)}function de(n,t){n.value=t??""}function Gt(n,t,e,i){e===null?n.style.removeProperty(t):n.style.setProperty(t,e,i?"important":"")}function Cl(n,t){for(let e=0;e<n.options.length;e+=1){let i=n.options[e];if(i.__value===t){i.selected=!0;return}}n.selectedIndex=-1}function sd(n){let t=n.querySelector(":checked")||n.options[0];return t&&t.__value}function h1(n,t,{bubbles:e=!1,cancelable:i=!1}={}){let s=document.createEvent("CustomEvent");return s.initCustomEvent(n,e,i,t),s}var bw=new Map;var Os;function As(n){Os=n}function u1(){if(!Os)throw new Error("Function called outside component initialization");return Os}function Pl(){let n=u1();return(t,e,{cancelable:i=!1}={})=>{let s=n.$$.callbacks[t];if(s){let r=h1(t,e,{cancelable:i});return s.slice().forEach(o=>{o.call(n,r)}),!r.defaultPrevented}return!0}}var Rs=[];var lo=[],co=[],rd=[],f1=Promise.resolve(),Tl=!1;function d1(){Tl||(Tl=!0,f1.then(od))}function Ls(n){co.push(n)}var Dl=new Set,ho=0;function od(){let n=Os;do{for(;ho<Rs.length;){let t=Rs[ho];ho++,As(t),p1(t.$$)}for(As(null),Rs.length=0,ho=0;lo.length;)lo.pop()();for(let t=0;t<co.length;t+=1){let e=co[t];Dl.has(e)||(Dl.add(e),e())}co.length=0}while(Rs.length);for(;rd.length;)rd.pop()();Tl=!1,Dl.clear(),As(n)}function p1(n){if(n.fragment!==null){n.update(),hn(n.before_update);let t=n.dirty;n.dirty=[-1],n.fragment&&n.fragment.p(n.ctx,t),n.after_update.forEach(Ls)}}var uo=new Set,g1;function Fs(n,t){n&&n.i&&(uo.delete(n),n.i(t))}function fo(n,t,e,i){if(n&&n.o){if(uo.has(n))return;uo.add(n),g1.c.push(()=>{uo.delete(n),i&&(e&&n.d(1),i())}),n.o(t)}else i&&i()}var vw=typeof window!="undefined"?window:typeof globalThis!="undefined"?globalThis:global;var _w=new Set(["allowfullscreen","allowpaymentrequest","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","hidden","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected"]);function ad(n){n&&n.c()}function El(n,t,e,i){let{fragment:s,on_mount:r,on_destroy:o,after_update:a}=n.$$;s&&s.m(t,e),i||Ls(()=>{let l=r.map(kl).filter(Gf);o?o.push(...l):hn(l),n.$$.on_mount=[]}),a.forEach(Ls)}function po(n,t){let e=n.$$;e.fragment!==null&&(hn(e.on_destroy),e.fragment&&e.fragment.d(t),e.on_destroy=e.fragment=null,e.ctx=[])}function m1(n,t){n.$$.dirty[0]===-1&&(Rs.push(n),d1(),n.$$.dirty.fill(0)),n.$$.dirty[t/31|0]|=1<<t%31}function go(n,t,e,i,s,r,o,a=[-1]){let l=Os;As(n);let c=n.$$={fragment:null,ctx:null,props:r,update:wl,not_equal:s,bound:qf(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(t.context||(l?l.$$.context:[])),callbacks:qf(),dirty:a,skip_bound:!1,root:t.target||l.$$.root};o&&o(c.root);let h=!1;if(c.ctx=e?e(n,t.props||{},(f,g,...p)=>{let m=p.length?p[0]:g;return c.ctx&&s(c.ctx[f],c.ctx[f]=m)&&(!c.skip_bound&&c.bound[f]&&c.bound[f](m),h&&m1(n,f)),g}):[],c.update(),h=!0,hn(c.before_update),c.fragment=i?i(c.ctx):!1,t.target){if(t.hydrate){r1();let f=c1(t.target);c.fragment&&c.fragment.l(f),f.forEach(Ie)}else c.fragment&&c.fragment.c();t.intro&&Fs(n.$$.fragment),El(n,t.target,t.anchor,t.customElement),o1(),od()}As(l)}var b1;typeof HTMLElement=="function"&&(b1=class extends HTMLElement{constructor(){super();this.attachShadow({mode:"open"})}connectedCallback(){let{on_mount:n}=this.$$;this.$$.on_disconnect=n.map(kl).filter(Gf);for(let t in this.$$.slotted)this.appendChild(this.$$.slotted[t])}attributeChangedCallback(n,t,e){this[n]=e}disconnectedCallback(){hn(this.$$.on_disconnect)}$destroy(){po(this,1),this.$destroy=wl}$on(n,t){let e=this.$$.callbacks[n]||(this.$$.callbacks[n]=[]);return e.push(t),()=>{let i=e.indexOf(t);i!==-1&&e.splice(i,1)}}$set(n){this.$$set&&!Uf(n)&&(this.$$.skip_bound=!0,this.$$set(n),this.$$.skip_bound=!1)}});var Is=class{$destroy(){po(this,1),this.$destroy=wl}$on(t,e){let i=this.$$.callbacks[t]||(this.$$.callbacks[t]=[]);return i.push(e),()=>{let s=i.indexOf(e);s!==-1&&i.splice(s,1)}}$set(t){this.$$set&&!Uf(t)&&(this.$$.skip_bound=!0,this.$$set(t),this.$$.skip_bound=!1)}};var mo=ze(require("obsidian"));function v1(n){oo(n,"svelte-1lboqqp","h3.svelte-1lboqqp{margin:0}button.svelte-1lboqqp{display:flex;justify-content:space-between;width:100%;border:none;margin:0;padding:1em 0.5em}button.svelte-1lboqqp{fill:currentColor}svg.svelte-1lboqqp{height:0.7em;width:0.7em}")}function _1(n){let t,e,i,s,r,o,a,l,c,h,f,g,p,m,y=n[3].default,S=Kf(y,n,n[2],null);return{c(){t=Y("div"),e=Y("h3"),i=Y("button"),s=Sl(n[0]),r=It(),o=ao("svg"),a=ao("path"),l=ao("path"),c=It(),h=Y("div"),S&&S.c(),X(a,"class","vert"),X(a,"d","M10 1V19"),X(a,"stroke","black"),X(a,"stroke-width","2"),X(l,"d","M1 10L19 10"),X(l,"stroke","black"),X(l,"stroke-width","2"),X(o,"viewBox","0 0 20 20"),X(o,"fill","none"),X(o,"class","svelte-1lboqqp"),X(i,"aria-expanded",n[1]),X(i,"class","svelte-1lboqqp"),X(e,"class","svelte-1lboqqp"),X(h,"class","contents"),h.hidden=f=!n[1],X(t,"class","collapsible")},m(M,C){Xe(M,t,C),z(t,e),z(e,i),z(i,s),z(i,r),z(i,o),z(o,a),z(o,l),z(t,c),z(t,h),S&&S.m(h,null),g=!0,p||(m=Jt(i,"click",n[4]),p=!0)},p(M,[C]){(!g||C&1)&&id(s,M[0]),(!g||C&2)&&X(i,"aria-expanded",M[1]),S&&S.p&&(!g||C&4)&&Qf(S,y,M,M[2],g?Jf(y,M[2],C,null):td(M[2]),null),(!g||C&2&&f!==(f=!M[1]))&&(h.hidden=f)},i(M){g||(Fs(S,M),g=!0)},o(M){fo(S,M),g=!1},d(M){M&&Ie(t),S&&S.d(M),p=!1,m()}}}function y1(n,t,e){let{$$slots:i={},$$scope:s}=t,{headerText:r}=t,o=!1,a=()=>e(1,o=!o);return n.$$set=l=>{"headerText"in l&&e(0,r=l.headerText),"$$scope"in l&&e(2,s=l.$$scope)},[r,o,s,i,a]}var ld=class extends Is{constructor(t){super();go(this,t,y1,_1,ro,{headerText:0},v1)}},cd=ld;function x1(n){oo(n,"svelte-1tlkntj",".addMoreButtonContainer.svelte-1tlkntj{display:flex;justify-content:flex-end;margin-top:0.4rem}.subDesc.svelte-1tlkntj{font-size:smaller;opacity:0.5;margin:0}.desc.svelte-1tlkntj{padding-right:1em}.mainDesc.svelte-1tlkntj{margin:0}table.svelte-1tlkntj{margin:auto}.controlElement.svelte-1tlkntj{text-align:center}.chart-modal.svelte-1tlkntj{overflow-y:auto}.modalColumn.svelte-1tlkntj{display:flex;gap:2em}.chartPreview.svelte-1tlkntj{width:30vw;display:flex;justify-content:center;align-items:center}")}function hd(n,t,e){let i=n.slice();return i[33]=t[e],i[34]=t,i[35]=e,i}function ud(n){let t,e,i,s,r,o,a,l,c,h,f;function g(){n[23].call(r,n[34],n[35])}function p(){n[24].call(c,n[34],n[35])}return{c(){t=Y("tr"),e=Y("td"),e.innerHTML=`<p class="mainDesc svelte-1tlkntj">Y Axis</p> 
              <p class="subDesc svelte-1tlkntj">Set Data Fields (Comma seperated)</p>`,i=It(),s=Y("td"),r=Y("input"),o=It(),a=Y("br"),l=It(),c=Y("input"),X(e,"class","desc svelte-1tlkntj"),X(r,"type","text"),X(r,"placeholder","Name"),X(c,"type","text"),X(c,"placeholder","1, -2, 11, 5"),Gt(c,"margin-top","3px"),X(s,"class","controlElement svelte-1tlkntj")},m(m,y){Xe(m,t,y),z(t,e),z(t,i),z(t,s),z(s,r),de(r,n[33].dataTitle),z(s,o),z(s,a),z(s,l),z(s,c),de(c,n[33].data),h||(f=[Jt(r,"input",g),Jt(c,"input",p)],h=!0)},p(m,y){n=m,y[0]&1024&&r.value!==n[33].dataTitle&&de(r,n[33].dataTitle),y[0]&1024&&c.value!==n[33].data&&de(c,n[33].data)},d(m){m&&Ie(t),h=!1,hn(f)}}}function w1(n){let t,e,i,s,r,o,a,l,c,h,f,g,p,m,y,S,M,C,F,D,I;return{c(){t=Y("hr"),e=It(),i=Y("table"),s=Y("tr"),r=Y("td"),r.innerHTML=`<p class="mainDesc svelte-1tlkntj">Line of Best Fit</p> 
            <p class="subDesc svelte-1tlkntj">Create a line of best fit</p>`,o=Y("td"),a=Y("input"),l=It(),c=Y("tr"),h=Y("td"),h.innerHTML=`<p class="mainDesc svelte-1tlkntj">Best Fit Line ID</p> 
            <p class="subDesc svelte-1tlkntj">The line ID used to create the line of best fit</p>`,f=Y("td"),g=Y("input"),p=Y("br"),m=It(),y=Y("tr"),S=Y("td"),S.innerHTML=`<p class="mainDesc svelte-1tlkntj">Line of Best Fit Title</p> 
            <p class="subDesc svelte-1tlkntj">The title for the line of best fit</p>`,M=Y("td"),C=Y("input"),F=Y("br"),X(r,"class","desc svelte-1tlkntj"),X(a,"type","checkbox"),X(a,"class","task-list-item-checkbox"),Gt(a,"width","16px"),Gt(a,"height","16px"),X(o,"class","controlElement svelte-1tlkntj"),X(h,"class","desc svelte-1tlkntj"),X(g,"type","text"),X(g,"placeholder","0"),Gt(g,"width","26px"),Gt(g,"height","32px"),X(f,"class","controlElement svelte-1tlkntj"),X(S,"class","desc svelte-1tlkntj"),X(C,"type","text"),X(C,"placeholder","Line of Best Fit"),Gt(C,"width","96px"),Gt(C,"height","32px"),X(M,"class","controlElement svelte-1tlkntj"),Gt(i,"width","100%"),X(i,"class","svelte-1tlkntj")},m($,N){Xe($,t,N),Xe($,e,N),Xe($,i,N),z(i,s),z(s,r),z(s,o),z(o,a),a.checked=n[6],z(i,l),z(i,c),z(c,h),z(c,f),z(f,g),de(g,n[8]),z(f,p),z(i,m),z(i,y),z(y,S),z(y,M),z(M,C),de(C,n[7]),z(M,F),D||(I=[Jt(a,"change",n[26]),Jt(g,"input",n[27]),Jt(C,"input",n[28])],D=!0)},p($,N){N[0]&64&&(a.checked=$[6]),N[0]&256&&g.value!==$[8]&&de(g,$[8]),N[0]&128&&C.value!==$[7]&&de(C,$[7])},d($){$&&Ie(t),$&&Ie(e),$&&Ie(i),D=!1,hn(I)}}}function k1(n){let t,e,i,s,r,o,a,l,c,h,f,g,p,m,y,S,M,C,F,D,I,$,N,G,U,it,lt,rt,Pt,zt,et,Et,St,re,ye,ot,Lt,Bt,Ut,Qt,k,v,x,R,E,O,Z,V,J,tt,xt,Ht,Ot,Yt,Xt,ue,ke,xe,Rn,un,Ln,je,$i,Fn,oi,$s,ji,ai,qe,In,$n,li,js,fn=n[10],ce=[];for(let bt=0;bt<fn.length;bt+=1)ce[bt]=ud(hd(n,fn,bt));return je=new cd({props:{headerText:"Line of Best Fit (Line chart only)",$$slots:{default:[w1]},$$scope:{ctx:n}}}),{c(){t=Y("div"),e=Y("h3"),e.textContent="Create a new Chart",i=It(),s=Y("div"),r=Y("div"),o=Y("table"),a=Y("tr"),l=Y("td"),l.innerHTML=`<p class="mainDesc svelte-1tlkntj">Chart Type</p> 
            <p class="subDesc svelte-1tlkntj">Choose a Chart Type</p>`,c=Y("td"),h=Y("select"),f=Y("option"),f.textContent="Bar",g=Y("option"),g.textContent="Line",p=Y("option"),p.textContent="Pie",m=Y("option"),m.textContent="Doughnut",y=Y("option"),y.textContent="Radar",S=Y("option"),S.textContent="Polar Area",M=It(),C=Y("tr"),F=Y("td"),F.innerHTML=`<p class="mainDesc svelte-1tlkntj">Smoothness</p> 
            <p class="subDesc svelte-1tlkntj">Changes the smoothness of the Chart</p>`,D=Y("td"),I=Y("input"),$=It(),N=Y("tr"),G=Y("td"),G.innerHTML=`<p class="mainDesc svelte-1tlkntj">Width</p> 
            <p class="subDesc svelte-1tlkntj">Changes the horizontal width</p>`,U=Y("td"),it=Y("input"),lt=It(),rt=Y("tr"),Pt=Y("td"),Pt.innerHTML=`<p class="mainDesc svelte-1tlkntj">Fill</p> 
            <p class="subDesc svelte-1tlkntj">Fill the underside of the Chart</p>`,zt=Y("td"),et=Y("input"),Et=It(),St=Y("tr"),re=Y("td"),re.innerHTML=`<p class="mainDesc svelte-1tlkntj">Distinct Colors</p> 
            <p class="subDesc svelte-1tlkntj">Use distinct Colors for each Label</p>`,ye=Y("td"),ot=Y("input"),Lt=It(),Bt=Y("tr"),Ut=Y("td"),Ut.innerHTML=`<p class="mainDesc svelte-1tlkntj">Start at Zero</p> 
            <p class="subDesc svelte-1tlkntj">Don&#39;t cut the graph at the bottom</p>`,Qt=Y("td"),k=Y("input"),v=It(),x=Y("hr"),R=It(),E=Y("table"),O=Y("tr"),Z=Y("td"),Z.innerHTML=`<p class="mainDesc svelte-1tlkntj">X Axis</p> 
            <p class="subDesc svelte-1tlkntj">Set Labels (Comma seperated)</p>`,V=It(),J=Y("td"),tt=Y("input"),xt=Y("br"),Ht=It(),Ot=Y("hr"),Yt=It(),Xt=Y("table");for(let bt=0;bt<ce.length;bt+=1)ce[bt].c();ue=It(),ke=Y("div"),xe=Y("button"),xe.textContent="Add more",Rn=It(),un=Y("hr"),Ln=It(),ad(je.$$.fragment),$i=It(),Fn=Y("div"),oi=Y("div"),$s=It(),ji=Y("hr"),ai=It(),qe=Y("div"),In=Y("button"),In.textContent="Insert Chart",X(l,"class","desc svelte-1tlkntj"),f.__value="bar",f.value=f.__value,g.__value="line",g.value=g.__value,p.__value="pie",p.value=p.__value,m.__value="doughnut",m.value=m.__value,y.__value="radar",y.value=y.__value,S.__value="polarArea",S.value=S.__value,X(h,"name","Chart Types"),X(h,"id","chart-types"),X(h,"class","dropdown"),n[0]===void 0&&Ls(()=>n[16].call(h)),X(c,"class","controlElement svelte-1tlkntj"),X(F,"class","desc svelte-1tlkntj"),X(I,"type","range"),X(I,"min","0"),X(I,"max","100"),X(I,"class","slider"),X(D,"class","controlElement svelte-1tlkntj"),X(G,"class","desc svelte-1tlkntj"),X(it,"type","range"),X(it,"min","20"),X(it,"max","100"),X(it,"class","slider"),X(U,"class","controlElement svelte-1tlkntj"),X(Pt,"class","desc svelte-1tlkntj"),X(et,"type","checkbox"),X(et,"class","task-list-item-checkbox"),Gt(et,"width","16px"),Gt(et,"height","16px"),X(zt,"class","controlElement svelte-1tlkntj"),X(re,"class","desc svelte-1tlkntj"),X(ot,"type","checkbox"),X(ot,"class","task-list-item-checkbox"),Gt(ot,"width","16px"),Gt(ot,"height","16px"),X(ye,"class","controlElement svelte-1tlkntj"),X(Ut,"class","desc svelte-1tlkntj"),X(k,"type","checkbox"),X(k,"class","task-list-item-checkbox"),Gt(k,"width","16px"),Gt(k,"height","16px"),X(Qt,"class","controlElement svelte-1tlkntj"),Gt(o,"width","100%"),X(o,"class","svelte-1tlkntj"),X(Z,"class","desc svelte-1tlkntj"),X(tt,"type","text"),X(tt,"placeholder","Monday, Tuesday, ..."),X(J,"class","controlElement svelte-1tlkntj"),Gt(E,"width","100%"),X(E,"class","svelte-1tlkntj"),X(ke,"class","addMoreButtonContainer svelte-1tlkntj"),Gt(Xt,"width","100%"),X(Xt,"class","svelte-1tlkntj"),X(oi,"id","preview"),X(Fn,"class","chartPreview svelte-1tlkntj"),X(s,"class","modalColumn svelte-1tlkntj"),X(t,"class","chart-modal svelte-1tlkntj"),X(In,"class","mod-cta"),Gt(qe,"display","flex"),Gt(qe,"justify-content","center"),Gt(qe,"align-items","center")},m(bt,te){Xe(bt,t,te),z(t,e),z(t,i),z(t,s),z(s,r),z(r,o),z(o,a),z(a,l),z(a,c),z(c,h),z(h,f),z(h,g),z(h,p),z(h,m),z(h,y),z(h,S),Cl(h,n[0]),z(o,M),z(o,C),z(C,F),z(C,D),z(D,I),de(I,n[1]),z(o,$),z(o,N),z(N,G),z(N,U),z(U,it),de(it,n[2]),z(o,lt),z(o,rt),z(rt,Pt),z(rt,zt),z(zt,et),et.checked=n[3],z(o,Et),z(o,St),z(St,re),z(St,ye),z(ye,ot),ot.checked=n[4],z(o,Lt),z(o,Bt),z(Bt,Ut),z(Bt,Qt),z(Qt,k),k.checked=n[5],z(r,v),z(r,x),z(r,R),z(r,E),z(E,O),z(O,Z),z(O,V),z(O,J),z(J,tt),de(tt,n[9]),z(J,xt),z(r,Ht),z(r,Ot),z(r,Yt),z(r,Xt);for(let dn=0;dn<ce.length;dn+=1)ce[dn].m(Xt,null);z(Xt,ue),z(Xt,ke),z(ke,xe),z(r,Rn),z(r,un),z(r,Ln),El(je,r,null),z(s,$i),z(s,Fn),z(Fn,oi),n[29](oi),z(t,$s),z(t,ji),Xe(bt,ai,te),Xe(bt,qe,te),z(qe,In),$n=!0,li||(js=[Jt(h,"change",n[16]),Jt(I,"change",n[17]),Jt(I,"input",n[17]),Jt(it,"change",n[18]),Jt(it,"input",n[18]),Jt(et,"change",n[19]),Jt(ot,"change",n[20]),Jt(k,"change",n[21]),Jt(tt,"input",n[22]),Jt(xe,"click",n[25]),Jt(In,"click",n[12])],li=!0)},p(bt,te){if(te[0]&1&&Cl(h,bt[0]),te[0]&2&&de(I,bt[1]),te[0]&4&&de(it,bt[2]),te[0]&8&&(et.checked=bt[3]),te[0]&16&&(ot.checked=bt[4]),te[0]&32&&(k.checked=bt[5]),te[0]&512&&tt.value!==bt[9]&&de(tt,bt[9]),te[0]&1024){fn=bt[10];let pe;for(pe=0;pe<fn.length;pe+=1){let zs=hd(bt,fn,pe);ce[pe]?ce[pe].p(zs,te):(ce[pe]=ud(zs),ce[pe].c(),ce[pe].m(Xt,ue))}for(;pe<ce.length;pe+=1)ce[pe].d(1);ce.length=fn.length}let dn={};te[0]&448|te[1]&32&&(dn.$$scope={dirty:te,ctx:bt}),je.$set(dn)},i(bt){$n||(Fs(je.$$.fragment,bt),$n=!0)},o(bt){fo(je.$$.fragment,bt),$n=!1},d(bt){bt&&Ie(t),nd(ce,bt),po(je),n[29](null),bt&&Ie(ai),bt&&Ie(qe),li=!1,hn(js)}}}function S1(n,t,e){let{editor:i}=t,{renderer:s}=t,r=Pl(),o="bar",a=null,l=20,c=80,h=!1,f=!1,g=!1,p=!1,m,y="0",S="",M=[{dataTitle:"",data:""}],C,F=null,D=(0,mo.debounce)(async(ot,Lt)=>{var Bt;a&&a.destroy(),(Bt=F.lastElementChild)===null||Bt===void 0||Bt.remove(),a=s.renderRaw(await s.datasetPrep((0,mo.parseYaml)(ot),Lt),Lt)},500,!0);function I(){let ot=i.getDoc(),Lt=ot.getCursor();a.destroy(),ot.replaceRange("```chart\n"+C+"\n```",Lt),r("close")}function $(){o=sd(this),e(0,o)}function N(){l=Ml(this.value),e(1,l)}function G(){c=Ml(this.value),e(2,c)}function U(){h=this.checked,e(3,h)}function it(){f=this.checked,e(4,f)}function lt(){g=this.checked,e(5,g)}function rt(){S=this.value,e(9,S)}function Pt(ot,Lt){ot[Lt].dataTitle=this.value,e(10,M)}function zt(ot,Lt){ot[Lt].data=this.value,e(10,M)}let et=()=>e(10,M=[...M,{data:"",dataTitle:""}]);function Et(){p=this.checked,e(6,p)}function St(){y=this.value,e(8,y)}function re(){m=this.value,e(7,m)}function ye(ot){lo[ot?"unshift":"push"](()=>{F=ot,e(11,F)})}return n.$$set=ot=>{"editor"in ot&&e(13,i=ot.editor),"renderer"in ot&&e(14,s=ot.renderer)},n.$$.update=()=>{if(n.$$.dirty[0]&2047){t:e(15,C=`type: ${o}
labels: [${S}]
series:
${M.map(ot=>`  - title: ${ot.dataTitle}
    data: [${ot.data}]`).join(`
`)}
tension: ${l/100}
width: ${c}%
labelColors: ${f}
fill: ${h}
beginAtZero: ${g}
bestFit: ${p}
bestFitTitle: ${m}
bestFitNumber: ${y}`)}if(n.$$.dirty[0]&34816){t:if(F)try{D(C,F)}catch(ot){cn(ot,F)}}},[o,l,c,h,f,g,p,m,y,S,M,F,I,i,s,C,$,N,G,U,it,lt,rt,Pt,zt,et,Et,St,re,ye]}var fd=class extends Is{constructor(t){super();go(this,t,S1,k1,ro,{editor:13,renderer:14},x1,[-1,-1])}},dd=fd;var bo=class extends pd.Modal{constructor(t,e,i,s){super(t);this.settings=i,this.view=e,this.renderer=s}onOpen(){let{contentEl:t,view:e,settings:i,renderer:s}=this;t.empty(),new dd({target:t,props:{editor:e.editor,renderer:s}}).$on("close",()=>this.close())}onClose(){let{contentEl:t}=this;t.empty()}};var gd=ze(require("obsidian")),md={chart:'<svg xmlns="http://www.w3.org/2000/svg" fill-opacity="0.0" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-pie-chart"><path d="M21.21 15.89A10 10 0 1 1 8 2.83"></path><path d="M22 12A10 10 0 0 0 12 2v10z"></path></svg>'},bd=()=>{Object.keys(md).forEach(n=>{(0,gd.addIcon)(n,md[n])})};var Ol=class extends $e.Plugin{constructor(){super(...arguments);this.postprocessor=async(t,e,i)=>{let s;try{s=await(0,$e.parseYaml)(t.replace(/	/g,"    "))}catch(a){cn(a,e);return}if(!s.id&&(!s||!s.type||!s.labels||!s.series)){cn("Missing type, labels or series",e);return}if(s.bestFit===!0&&s.type==="line"){if(s.bestFitNumber!=null)var r=s.series[Number(s.bestFitNumber)].data;else var r=s.series[0].data;let a=s.labels,l=0,c=0,h=0,f=0;for(let y=0;y<r.length;++y)l=l+r[y],c=c+a[y],h=h+r[y]*r[y],f=f+r[y]*a[y];let g=(r.length*f-c*l)/(r.length*h-l*l),p=(c-g*l)/r.length,m=[];for(let y=0;y<a.length;++y)m.push((a[y]-p)/g);if(s.bestFitTitle!=null&&s.bestFitTitle!="undefined")var o=String(s.bestFitTitle);else var o="Line of Best Fit";s.series.push({title:o,data:m})}await this.renderer.renderFromYaml(s,e,i)}}async loadSettings(){this.settings=Object.assign({},eo,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}async onload(){console.log("loading plugin: Charts"),await this.loadSettings(),bd(),this.renderer=new to(this),window.renderChart=this.renderer.renderRaw,this.addSettingTab(new xl(this.app,this)),this.addCommand({id:"creation-helper",name:"Insert new Chart",checkCallback:t=>{let e=this.app.workspace.activeLeaf;return e.view instanceof $e.MarkdownView?(t||new bo(this.app,e.view,this.settings,this.renderer).open(),!0):!1}}),this.addCommand({id:"chart-from-table-column",name:"Create Chart from Table (Column oriented Layout)",editorCheckCallback:(t,e,i)=>{let s=e.getSelection();return i instanceof $e.MarkdownView&&s.split(`
`).length>=3&&s.split("|").length>=2?(t||Ka(e,"columns"),!0):!1}}),this.addCommand({id:"chart-from-table-row",name:"Create Chart from Table (Row oriented Layout)",editorCheckCallback:(t,e,i)=>i instanceof $e.MarkdownView&&e.getSelection().split(`
`).length>=3&&e.getSelection().split("|").length>=2?(t||Ka(e,"rows"),!0):!1}),this.addCommand({id:"chart-to-svg",name:"Create Image from Chart",editorCheckCallback:(t,e,i)=>i instanceof $e.MarkdownView&&e.getSelection().startsWith("```chart")&&e.getSelection().endsWith("```")?(t||(new $e.Notice("Rendering Chart..."),Ju(e,this.app,this.renderer,i.file,this.settings)),!0):!1}),this.registerMarkdownCodeBlockProcessor("chart",this.postprocessor),this.registerMarkdownCodeBlockProcessor("advanced-chart",async(t,e)=>this.renderer.renderRaw(await JSON.parse(t),e)),this.registerEvent(this.app.workspace.on("editor-menu",(t,e,i)=>{i&&this.settings.contextMenu&&t.addItem(s=>{s.setTitle("Insert Chart").setIcon("chart").onClick(r=>{new bo(this.app,i,this.settings,this.renderer).open()})})}))}onunload(){console.log("unloading plugin: Charts")}};
/*
 * @license
 *
 * Copyright (c) 2011-2014, Christopher Jeffrey. (MIT Licensed)
 * https://github.com/chjj/marked
 *
 * Copyright (c) 2018-2021, Костя Третяк. (MIT Licensed)
 * https://github.com/ts-stack/markdown
 */
/*!
  * chartjs-adapter-moment v1.0.0
  * https://www.chartjs.org
  * (c) 2021 chartjs-adapter-moment Contributors
  * Released under the MIT license
  */
/*!
 * @kurkle/color v0.2.1
 * https://github.com/kurkle/color#readme
 * (c) 2022 Jukka Kurkela
 * Released under the MIT License
 */
/*!
 * Chart.js v3.9.1
 * https://www.chartjs.org
 * (c) 2022 Chart.js Contributors
 * Released under the MIT License
 */
/*!
 * chartjs-chart-sankey v0.12.0
 * https://github.com/kurkle/chartjs-chart-sankey#readme
 * (c) 2022 Jukka Kurkela
 * Released under the MIT license
 */
/*!
 * vanilla-picker v2.12.1
 * https://vanilla-picker.js.org
 *
 * Copyright 2017-2021 Andreas Borgen (https://github.com/Sphinxxxx), Adam Brooks (https://github.com/dissimulate)
 * Released under the ISC license.
 */
/*!
* chartjs-plugin-annotation v2.2.1
* https://www.chartjs.org/chartjs-plugin-annotation/index
 * (c) 2023 chartjs-plugin-annotation Contributors
 * Released under the MIT License
 */
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */
/**
 * @license
 *
 * Copyright (c) 2011-2014, Christopher Jeffrey. (MIT Licensed)
 * https://github.com/chjj/marked
 *
 * Copyright (c) 2018-2021, Костя Третяк. (MIT Licensed)
 * https://github.com/ts-stack/markdown
 */
/**
 * @license
 *
 * Copyright (c) 2018-2021, Костя Третяк. (MIT Licensed)
 * https://github.com/ts-stack/markdown
 */
/**
 * chroma.js - JavaScript library for color conversions
 *
 * Copyright (c) 2011-2019, Gregor Aisch
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 * list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 *
 * 3. The name Gregor Aisch may not be used to endorse or promote products
 * derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL GREGOR AISCH OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 * OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * -------------------------------------------------------
 *
 * chroma.js includes colors from colorbrewer2.org, which are released under
 * the following license:
 *
 * Copyright (c) 2002 Cynthia Brewer, Mark Harrower,
 * and The Pennsylvania State University.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific
 * language governing permissions and limitations under the License.
 *
 * ------------------------------------------------------
 *
 * Named colors are taken from X11 Color Names.
 * http://www.w3.org/TR/css3-color/#svg-color
 *
 * @preserve
 */

/* nosourcemap */