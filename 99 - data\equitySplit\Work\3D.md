---
category: 3D
level1-retexture-days: 2
level1-retexture-status: pending
level1-retexture-priority: high
level1-kadappa-days: 2
level1-kadappa-status: pending
level1-kadappa-priority: medium
level1-door-days: 1
level1-door-status: pending
level1-door-priority: medium
level1-kapde-days: 1
level1-kapde-status: pending
level1-kapde-priority: low
level1-objects-days: 3
level1-objects-status: pending
level1-objects-priority: medium
level2-maze-days: 14
level2-maze-status: pending
level2-maze-priority: high
level2-baoli-days: 7
level2-baoli-status: pending
level2-baoli-priority: high
level2-doll-days: 5
level2-doll-status: pending
level2-doll-priority: medium
level2-plants-days: 2
level2-plants-status: pending
level2-plants-priority: low
level2-spider-days: 2
level2-spider-status: ongoing
level2-spider-priority: medium
level3-wall-roof-floor-days: 3
level3-wall-roof-floor-status: pending
level3-wall-roof-floor-priority: high
level3-assets-place-days: 3
level3-assets-place-status: pending
level3-assets-place-priority: medium
level3-retexture-days: 7
level3-retexture-status: pending
level3-retexture-priority: high
ai-model-days: 7
ai-model-status: ongoing
ai-model-priority: high
rest-days: 7
rest-status: pending
rest-priority: low
created: 2025-06-16T00:00
updated: 2025-06-16T11:44
---

# 3D Tasks

## Level 1 Tasks

### Level 1 - Retexture
**Days:** 
```meta-bind
INPUT[number:level1-retexture-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):level1-retexture-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):level1-retexture-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level1-retexture-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level1-retexture-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: level1-retexture-status
    evaluate: false
    value: "completed"
```

---

### Level 1 - Kadappa
**Days:** 
```meta-bind
INPUT[number:level1-kadappa-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):level1-kadappa-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):level1-kadappa-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level1-kadappa-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level1-kadappa-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: level1-kadappa-status
    evaluate: false
    value: "completed"
```

---

### Level 1 - Door
**Days:** 
```meta-bind
INPUT[number:level1-door-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):level1-door-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):level1-door-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level1-door-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level1-door-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: level1-door-status
    evaluate: false
    value: "completed"
```

---

### Level 1 - Kapde
**Days:** 
```meta-bind
INPUT[number:level1-kapde-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):level1-kapde-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):level1-kapde-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level1-kapde-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level1-kapde-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: level1-kapde-status
    evaluate: false
    value: "completed"
```

---

### Level 1 - Objects
**Days:** 
```meta-bind
INPUT[number:level1-objects-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):level1-objects-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):level1-objects-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level1-objects-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level1-objects-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: level1-objects-status
    evaluate: false
    value: "completed"
```

## Level 2 Tasks

### Level 2 - Maze
**Days:**
```meta-bind
INPUT[number:level2-maze-days]
```
**Status:**
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):level2-maze-status]
```
**Priority:**
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):level2-maze-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level2-maze-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level2-maze-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: level2-maze-status
    evaluate: false
    value: "completed"
```

---

### Level 2 - Baoli
**Days:**
```meta-bind
INPUT[number:level2-baoli-days]
```
**Status:**
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):level2-baoli-status]
```
**Priority:**
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):level2-baoli-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level2-baoli-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level2-baoli-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: level2-baoli-status
    evaluate: false
    value: "completed"
```

---

### Level 2 - Doll
**Days:**
```meta-bind
INPUT[number:level2-doll-days]
```
**Status:**
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):level2-doll-status]
```
**Priority:**
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):level2-doll-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level2-doll-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level2-doll-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: level2-doll-status
    evaluate: false
    value: "completed"
```

---

### Level 2 - Plants
**Days:**
```meta-bind
INPUT[number:level2-plants-days]
```
**Status:**
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):level2-plants-status]
```
**Priority:**
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):level2-plants-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level2-plants-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level2-plants-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: level2-plants-status
    evaluate: false
    value: "completed"
```

---

### Level 2 - Spider
**Days:**
```meta-bind
INPUT[number:level2-spider-days]
```
**Status:**
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):level2-spider-status]
```
**Priority:**
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):level2-spider-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level2-spider-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level2-spider-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: level2-spider-status
    evaluate: false
    value: "completed"
```

## Level 3 Tasks

### Level 3 - Wall & Roof & Floor
**Days:**
```meta-bind
INPUT[number:level3-wall-roof-floor-days]
```
**Status:**
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):level3-wall-roof-floor-status]
```
**Priority:**
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):level3-wall-roof-floor-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level3-wall-roof-floor-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level3-wall-roof-floor-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: level3-wall-roof-floor-status
    evaluate: false
    value: "completed"
```

---

### Level 3 - Assets Place
**Days:**
```meta-bind
INPUT[number:level3-assets-place-days]
```
**Status:**
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):level3-assets-place-status]
```
**Priority:**
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):level3-assets-place-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level3-assets-place-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level3-assets-place-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: level3-assets-place-status
    evaluate: false
    value: "completed"
```

---

### Level 3 - Retexture
**Days:**
```meta-bind
INPUT[number:level3-retexture-days]
```
**Status:**
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):level3-retexture-status]
```
**Priority:**
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):level3-retexture-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level3-retexture-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: level3-retexture-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: level3-retexture-status
    evaluate: false
    value: "completed"
```

## Other 3D Tasks

### AI Model
**Days:**
```meta-bind
INPUT[number:ai-model-days]
```
**Status:**
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):ai-model-status]
```
**Priority:**
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):ai-model-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: ai-model-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: ai-model-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: ai-model-status
    evaluate: false
    value: "completed"
```

---

### Rest
**Days:**
```meta-bind
INPUT[number:rest-days]
```
**Status:**
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):rest-status]
```
**Priority:**
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):rest-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: rest-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: rest-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: rest-status
    evaluate: false
    value: "completed"
```
