
## 📝 (Last 7 Days)
```dataviewjs
// Add a spacer div to improve scrolling stability
dv.el("div", "", {attr: {style: "height: 20px;"}});

// --- Configuration ---
// DO NOT MODIFY THESE PATHS - They should match the ones in the equity calculation script above
const workLogFolderPath = '"99 - data/equitySplit/Founders"'; // <== CHECK PATH
const founderNames = ["Shashank", "Anshu<PERSON>", "Parth", "Dhaval", "Vedant"]; // <== CHECK NAMES

// --- Helper Function for Logging ---
function workLogDebug(message, ...args) {
    console.log(`[WorkLog] ${message}`, args.length > 0 ? args : "");
}

// --- Helper Functions ---
// Function to format date as YYYY-MM-DD
function formatDate(date) {
    try {
        const d = new Date(date);
        return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
    } catch (e) {
        return "Unknown Date";
    }
}

// Function to format date in a more readable format
function formatReadableDate(date) {
    try {
        const d = new Date(date);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        // Check if date is today
        if (formatDate(d) === formatDate(today)) {
            return "Today";
        }

        // Check if date is yesterday
        if (formatDate(d) === formatDate(yesterday)) {
            return "Yesterday";
        }

        // Otherwise return formatted date
        const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        return d.toLocaleDateString(undefined, options);
    } catch (e) {
        return "Unknown Date";
    }
}

// Function to get color for founder
function getFounderColor(name, alpha = 1) {
    const index = founderNames.indexOf(name);

    // Use the same colors as in other sections
    const colors = [
        `rgba(25, 118, 210, ${alpha})`,   // Darker Blue
        `rgba(198, 40, 40, ${alpha})`,    // Darker Red
        `rgba(46, 125, 50, ${alpha})`,    // Darker Green
        `rgba(239, 108, 0, ${alpha})`,    // Darker Orange
        `rgba(123, 31, 162, ${alpha})`,   // Darker Purple
        `rgba(0, 131, 143, ${alpha})`,    // Darker Teal
        `rgba(191, 54, 12, ${alpha})`,    // Darker Deep Orange
        `rgba(93, 64, 55, ${alpha})`,     // Darker Brown
        `rgba(48, 63, 159, ${alpha})`,    // Darker Indigo
        `rgba(158, 157, 36, ${alpha})`    // Darker Lime
    ];

    // Return the color or a default gray for unknown founders
    return index >= 0 ? colors[index % colors.length] : `rgba(100, 100, 100, ${alpha})`;
}

// --- Main Function ---
(function() {
    workLogDebug("Starting work log script...");

    try {
        // --- Load Contribution Files ---
        workLogDebug(`Processing files from folder: ${workLogFolderPath}`);
        const files = dv.pages(workLogFolderPath);
        workLogDebug(`Found ${files.length} files.`);

        if (files.length === 0) {
            dv.el("p", `ℹ️ No contribution files found.`);
            return;
        }

        // Get the last 7 days
        const today = new Date();
        const sevenDaysAgo = new Date(today);
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        // Group files by date
        const filesByDate = {};

        files.forEach(file => {
            try {
                // Get the file creation date (prioritizing metadata 'created' field)
                const fileDate = file.created || file.file?.ctime || new Date();

                // Skip files older than 7 days
                if (fileDate < sevenDaysAgo) return;

                // Format date as YYYY-MM-DD for grouping
                const dateKey = formatDate(fileDate);

                // Initialize array for this date if it doesn't exist
                if (!filesByDate[dateKey]) {
                    filesByDate[dateKey] = [];
                }

                // Add file to the appropriate date group
                filesByDate[dateKey].push(file);
            } catch (err) {
                workLogDebug(`Error processing file date: ${err.message}`);
            }
        });

        // Sort dates (newest first)
        const sortedDates = Object.keys(filesByDate).sort().reverse();

        // Create a container for all work logs
        const workLogsContainer = dv.el("div", "", {cls: "work-logs-container"});
        workLogsContainer.style.display = "flex";
        workLogsContainer.style.flexDirection = "column";
        workLogsContainer.style.gap = "25px";
        workLogsContainer.style.marginTop = "15px";
        workLogsContainer.style.marginBottom = "40px";

        // Create a block for each date
        sortedDates.forEach(dateKey => {
            try {
                const dateFiles = filesByDate[dateKey];

                // Create a container for this date's work log
                const dateContainer = document.createElement("div");
                dateContainer.style.backgroundColor = "#ffffff";
                dateContainer.style.borderRadius = "8px";
                dateContainer.style.padding = "20px";
                dateContainer.style.boxShadow = "0 4px 12px rgba(0,0,0,0.12)";
                dateContainer.style.border = "1px solid #e0e0e0";

                // Create a header for the date
                const dateHeader = document.createElement("div");
                dateHeader.textContent = formatReadableDate(new Date(dateKey));
                dateHeader.style.fontSize = "18px";
                dateHeader.style.fontWeight = "bold";
                dateHeader.style.marginBottom = "15px";
                dateHeader.style.paddingBottom = "10px";
                dateHeader.style.borderBottom = "2px solid #e3f2fd";
                dateHeader.style.color = "#1976D2";
                dateHeader.style.letterSpacing = "0.2px";
                dateContainer.appendChild(dateHeader);

                // Create a table for this date's entries
                const table = document.createElement("table");
                table.style.width = "100%";
                table.style.borderCollapse = "separate";
                table.style.borderSpacing = "0 8px";

                // Create table header
                const thead = document.createElement("thead");
                const headerRow = document.createElement("tr");

                // Create header cells
                const headers = ["Founder", "Work Description", "Hours"];
                const widths = ["25%", "55%", "20%"];

                headers.forEach((headerText, index) => {
                    const th = document.createElement("th");
                    th.textContent = headerText;
                    th.style.padding = "12px 15px";
                    th.style.textAlign = index === 2 ? "right" : "left";
                    th.style.color = "#1976D2";
                    th.style.fontWeight = "bold";
                    th.style.fontSize = "16px";
                    th.style.width = widths[index];
                    headerRow.appendChild(th);
                });

                thead.appendChild(headerRow);
                table.appendChild(thead);

                // Create table body
                const tbody = document.createElement("tbody");

                // Add rows for each file on this date
                dateFiles.forEach(file => {
                    try {
                        const row = document.createElement("tr");

                        // Safely get properties with fallbacks
                        const founderName = file.name || "Unknown";
                        const workDesc = file["work-description"] || "";
                        const hoursCount = parseFloat(file.count || 0);

                        // Founder name cell
                        const nameCell = document.createElement("td");
                        nameCell.style.padding = "12px 15px";
                        nameCell.style.fontWeight = "bold";
                        nameCell.style.fontSize = "16px";
                        nameCell.style.color = getFounderColor(founderName, 1).replace("rgba", "rgb").replace(", 1)", ")");
                        nameCell.textContent = founderName;

                        // Work description cell
                        const descCell = document.createElement("td");
                        descCell.style.padding = "12px 15px";
                        descCell.style.fontSize = "16px";
                        descCell.style.color = "#333333";
                        descCell.textContent = workDesc;

                        // Hours cell
                        const hoursCell = document.createElement("td");
                        hoursCell.style.padding = "12px 15px";
                        hoursCell.style.textAlign = "right";

                        // Create colored text for hours
                        const hoursText = document.createElement("span");
                        hoursText.textContent = hoursCount.toFixed(2);
                        hoursText.style.color = getFounderColor(founderName, 1).replace("rgba", "rgb").replace(", 1)", ")");
                        hoursText.style.fontWeight = "bold";
                        hoursText.style.fontSize = "16px";
                        hoursText.style.display = "inline-block";
                        hoursText.style.minWidth = "60px";
                        hoursText.style.textAlign = "right";

                        hoursCell.appendChild(hoursText);

                        // Add cells to row
                        row.appendChild(nameCell);
                        row.appendChild(descCell);
                        row.appendChild(hoursCell);

                        // Add row to table body
                        tbody.appendChild(row);
                    } catch (err) {
                        workLogDebug(`Error processing file: ${err.message}`);
                        // Continue to the next file
                    }
                });

                // Add a summary row with total hours for this date
                const totalHours = dateFiles.reduce((sum, file) => {
                    try {
                        return sum + parseFloat(file.count || 0);
                    } catch (e) {
                        return sum;
                    }
                }, 0);

                if (dateFiles.length > 1) { // Only show total if there's more than one entry
                    const totalRow = document.createElement("tr");
                    totalRow.style.borderTop = "1px solid #e0e0e0";
                    totalRow.style.backgroundColor = "#f8f9fa";

                    // Empty name cell
                    const emptyCell = document.createElement("td");
                    emptyCell.style.padding = "12px 15px";

                    // Total label cell
                    const totalLabelCell = document.createElement("td");
                    totalLabelCell.style.padding = "12px 15px";
                    totalLabelCell.style.fontWeight = "bold";
                    totalLabelCell.style.fontSize = "16px";
                    totalLabelCell.style.color = "#333333";
                    totalLabelCell.textContent = "Total for day:";
                    totalLabelCell.style.textAlign = "right";

                    // Total hours cell
                    const totalHoursCell = document.createElement("td");
                    totalHoursCell.style.padding = "12px 15px";
                    totalHoursCell.style.textAlign = "right";

                    // Create colored text for total hours
                    const totalHoursText = document.createElement("span");
                    totalHoursText.textContent = totalHours.toFixed(2);
                    totalHoursText.style.color = "#1976D2";
                    totalHoursText.style.fontWeight = "bold";
                    totalHoursText.style.fontSize = "16px";
                    totalHoursText.style.display = "inline-block";
                    totalHoursText.style.minWidth = "60px";
                    totalHoursText.style.textAlign = "right";

                    totalHoursCell.appendChild(totalHoursText);

                    // Add cells to total row
                    totalRow.appendChild(emptyCell);
                    totalRow.appendChild(totalLabelCell);
                    totalRow.appendChild(totalHoursCell);

                    // Add total row to table body
                    tbody.appendChild(totalRow);
                }

                table.appendChild(tbody);
                dateContainer.appendChild(table);

                // Add this date's container to the main container
                workLogsContainer.appendChild(dateContainer);
            } catch (err) {
                workLogDebug(`Error processing date block: ${err.message}`);
            }
        });

        // If no entries were found in the last 7 days
        if (sortedDates.length === 0) {
            const noEntriesMsg = document.createElement("div");
            noEntriesMsg.style.padding = "20px";
            noEntriesMsg.style.backgroundColor = "#f8f9fa";
            noEntriesMsg.style.borderRadius = "8px";
            noEntriesMsg.style.textAlign = "center";
            noEntriesMsg.style.color = "#666666";
            noEntriesMsg.style.fontSize = "16px";
            noEntriesMsg.textContent = "No work entries found in the last 7 days.";
            workLogsContainer.appendChild(noEntriesMsg);
        }

    } catch (error) {
        workLogDebug("ERROR:", error);
        dv.el("p", `❌ **ERROR:** ${error.message}. Check console for details.`);
    }
})();
```
