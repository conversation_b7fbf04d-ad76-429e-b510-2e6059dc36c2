---
created: 2025-04-21T00:17:50
updated: 2025-04-22T00:40:05
---
# <PERSON><PERSON><PERSON>t User Guide

## For Majdoors 👷

### Time Tracking
Your voice channel time is automatically tracked when you:
- Join a voice channel
- Are not deafened
- Have the "Majdoor" role

### Commands
- `/time` - Check your tracked time
- `/time @user` - Check another user's tracked time
- `/stats` - View global time statistics
- `/leaderboard` - See weekly rankings

### Important Notes
- Time tracking automatically pauses when you deafen yourself
- Time tracking resumes when you undeafen
- Stats reset daily at midnight
- Weekly leaderboard shows the last 7 days of activity

### Tips
- Keep your microphone undeafened to ensure proper time tracking
- Check the leaderboard regularly to see your ranking
- Use `/time` to verify your tracked hours

## For Thekedar 👨‍💼

### Bot Management Commands
- `/pause @user` - Manually pause time tracking for a user
- `/resume @user` - Manually resume time tracking for a user

### Bot Maintenance
To update or restart the bot, use these commands in the server terminal:

```bash
# Stop the bot
pm2 stop dihadi-bot
cd ..
rm 'Dihadi-Bot.zip'

#install the bot again
# Extract the zip file

unzip 'Dihadi-Bot.zip' -d bot
cd bot
# Install Node.js and npm if not already installed
sudo apt update
sudo apt install -y nodejs npm 
# Install build tools for SQLite3
sudo apt install -y build-essential python3
rm -rf node_modules
rm package-lock.json  # Optional, but recommended for a clean install
sudo apt update
sudo apt install -y build-essential python3
npm install
# Start the bot again
pm2 start dihadi-bot

```

### Troubleshooting
If users report tracking issues:
1. Check if they have the correct "Majdoor" role
2. Verify they aren't deafened
3. Use `/pause` and `/resume` to reset tracking if needed

### Support Channels
- Use the <#${BOT_COMMANDS_CHANNEL_ID}> channel for bot commands
- Time reports are sent to <#${TIME_REPORTS_CHANNEL_ID}>

For technical support or issues, please contact the bot administrator.
