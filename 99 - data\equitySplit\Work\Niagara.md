---
category: Niagara
smoke-days: 2
smoke-status: pending
smoke-priority: medium
filler-niagara-days: 2
filler-niagara-status: pending
filler-niagara-priority: medium
tea-prepared-days: 1
tea-prepared-status: pending
tea-prepared-priority: low
created: 2025-06-16T00:00
updated: 2025-06-16T00:00
---

# Niagara Tasks

## Smoke
**Days:** 
```meta-bind
INPUT[number:smoke-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):smoke-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):smoke-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: smoke-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: smoke-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: smoke-status
    evaluate: false
    value: "completed"
```

---

## Filler Niagara
**Days:** 
```meta-bind
INPUT[number:filler-niagara-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):filler-niagara-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):filler-niagara-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: filler-niagara-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: filler-niagara-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: filler-niagara-status
    evaluate: false
    value: "completed"
```

---

## Tea Prepared
**Days:** 
```meta-bind
INPUT[number:tea-prepared-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):tea-prepared-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):tea-prepared-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: tea-prepared-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: tea-prepared-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: tea-prepared-status
    evaluate: false
    value: "completed"
```
