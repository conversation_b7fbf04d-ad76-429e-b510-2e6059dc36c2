

```dataviewjs
// Add a spacer div to improve scrolling stability
dv.el("div", "", {attr: {style: "height: 20px;"}});

// --- Configuration ---
// vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// vvv   !!! PLEASE DOUBLE-CHECK THESE PATHS AND NAMES !!!                 vvv
const referenceFilePath = "99 - data/equitySplit/Equity Reference.md"; // <== CHECK PATH
const foundersFolderPath = '"99 - data/equitySplit/Founders"'; // <== CHECK PATH
const founderNames = ["Shashank", "Anshul", "Parth", "Dhaval", "Vedant"]; // <== CHECK NAMES
const useFileNameForFounder = false;
// ^^^   !!! PLEASE DOUBLE-CHECK THESE PATHS AND NAMES !!!                 ^^^
// ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

// --- Helper Function for Logging ---
function logDebug(message, ...args) {
    console.log(`[EquityCalc] ${message}`, args.length > 0 ? args : "");
    // dv.paragraph(`*Debug: ${message} ${args.length > 0 ? JSON.stringify(args) : ""}*`);
}

// --- Main Async Function Wrapper (to allow await for delay) ---
await (async () => {
    logDebug("Starting equity calculation script (async wrapper)...");

    // --- Add a small delay to ensure plugins are loaded ---
    await new Promise(resolve => setTimeout(resolve, 500)); // Increased delay to 500ms

    // --- Improved Charts Plugin Detection ---
    let chartsPluginAvailable = false;
    try {
        // Check if the Charts plugin is available by testing for the renderChart function
        chartsPluginAvailable = typeof window.renderChart === 'function';
        logDebug(`Charts plugin check: ${chartsPluginAvailable ? 'FOUND' : 'NOT FOUND'}`);
    } catch (error) {
        logDebug(`Error checking for Charts plugin: ${error.message}`);
        chartsPluginAvailable = false;
    }

    if (!chartsPluginAvailable) {
        dv.el("p", "⚠️ **Warning:** The 'Charts' plugin is not detected. Please ensure it's installed and enabled for chart visualization.");
    } else {
        logDebug("Obsidian Charts plugin detected successfully.");
    }

    // --- Main Calculation Logic ---
    try {
        // --- 1. Load Reference Data ---
        logDebug(`Attempting to load reference file: ${referenceFilePath}`);
        const referencePage = dv.page(referenceFilePath);

        if (!referencePage) {
            dv.el("p", `❌ **Error:** Cannot find the Equity Reference file at '${referenceFilePath}'.`);
            throw new Error("Reference file not found.");
        }
        logDebug("Reference file loaded successfully.");

        // --- 2. Initialize Scores, Raw Hours, and Base Values ---
        let scores = {};
        let rawHours = {};
        let baseValues = {};
        let initializationErrors = false;

        // Read Dhaval's transition date and old rate
        const dhavalTransitionDateStr = referencePage.DhavalTransitionDate;
        const dhavalOldRateStr = referencePage.DhavalOldRate;
        let dhavalTransitionDate = null;
        let dhavalOldRate = null;

        if (dhavalTransitionDateStr) {
            dhavalTransitionDate = new Date(dhavalTransitionDateStr);
            logDebug(`Dhaval transition date: ${dhavalTransitionDate}`);
        }
        if (dhavalOldRateStr) {
            dhavalOldRate = parseInt(dhavalOldRateStr, 10);
            logDebug(`Dhaval old rate: ${dhavalOldRate}`);
        }

        founderNames.forEach(name => {
            scores[name] = 0;
            rawHours[name] = 0;

            const baseValueStr = referencePage[name];
            logDebug(`Initializing ${name}: Found base value string: '${baseValueStr}'`);
            if (baseValueStr !== undefined && baseValueStr !== null) {
                const baseValue = parseInt(baseValueStr, 10);
                if (!isNaN(baseValue)) {
                    baseValues[name] = baseValue;
                } else {
                    dv.el("p", `⚠️ **Warning:** Invalid base value for '${name}' in reference ('${baseValueStr}'). Contributions ignored.`);
                    initializationErrors = true;
                    delete scores[name]; delete rawHours[name];
                }
            } else {
                dv.el("p", `⚠️ **Warning:** Founder '${name}' not found in reference ('${referenceFilePath}'). Contributions ignored.`);
                initializationErrors = true;
                delete scores[name]; delete rawHours[name];
            }
        });

        if (initializationErrors) { dv.el("p", "🛑 Check reference file warnings."); }
        logDebug("Base values parsed:", baseValues);
        logDebug("Initial scores:", scores);
        logDebug("Initial raw hours:", rawHours);

        // --- 3. Process Contribution Files ---
        logDebug(`Processing files from folder: ${foundersFolderPath}`);
        const files = dv.pages(foundersFolderPath);
        logDebug(`Found ${files.length} files.`);
        let processedFileCount = 0; let processingErrors = 0;
        if (files.length === 0) { dv.el("p", `ℹ️ No contribution files found.`); }

        files.forEach((item, index) => {
            const filePath = item.file.path; let founderName = useFileNameForFounder ? item.file.name : item.name;
            logDebug(`\nProcessing file ${index + 1}: ${filePath} -> Founder: '${founderName}'`);
            if (!founderName) { logDebug(` -> Skipping: No name.`); processingErrors++; return; }
            if (!baseValues.hasOwnProperty(founderName)) { logDebug(` -> Skipping: Not tracked/init error.`); if (!founderNames.includes(founderName)) { dv.el("p", `ℹ️ Skipping '${filePath}': Unexpected name '${founderName}'.`); } processingErrors++; return; }
            const countValue = item.count; logDebug(` -> Count: '${countValue}'`);
            if (countValue === undefined || countValue === null) { dv.el("p", `⚠️ Skipping '${filePath}': Missing 'count'.`); logDebug(` -> Skipping: Missing count.`); processingErrors++; return; }
            const numericCount = parseFloat(countValue); logDebug(` -> Parsed count: ${numericCount}`);
            if (isNaN(numericCount)) { dv.el("p", `⚠️ Skipping '${filePath}': Invalid 'count': '${countValue}'.`); logDebug(` -> Skipping: Invalid count.`); processingErrors++; return; }
            // Determine the appropriate base value for this contribution
            let baseValue = baseValues[founderName];

            // Special handling for Dhaval's date-based base rate
            if (founderName === "Dhaval" && dhavalTransitionDate && dhavalOldRate) {
                // Get the creation date from the file (prioritizing metadata 'created' field)
                const creationDate = item.created || item.file.ctime;
                if (creationDate) {
                    const contributionDate = new Date(creationDate);
                    // If contribution is before the transition date, use the old rate
                    if (contributionDate < dhavalTransitionDate) {
                        baseValue = dhavalOldRate;
                        logDebug(` -> Using Dhaval's old rate (${dhavalOldRate}) for contribution on ${contributionDate.toISOString().split('T')[0]}`);
                    } else {
                        logDebug(` -> Using Dhaval's new rate (${baseValues[founderName]}) for contribution on ${contributionDate.toISOString().split('T')[0]}`);
                    }
                }
            }

            const weightedContribution = baseValue * numericCount;
            scores[founderName] += weightedContribution; rawHours[founderName] += numericCount;
            logDebug(` -> Score+=${weightedContribution} (New: ${scores[founderName]}), Hours+=${numericCount} (New: ${rawHours[founderName]})`);
            processedFileCount++;
        });
        logDebug(`Finished processing. Processed ${processedFileCount} files with ${processingErrors} skips/errors.`);
        logDebug("Final scores:", scores); logDebug("Final raw hours:", rawHours);

        // --- 4. Calculate Total Score ---
        let grandTotalScore = Object.values(scores).reduce((sum, score) => (typeof score === 'number' && !isNaN(score) ? sum + score : sum), 0);
        logDebug(`Grand Total Score: ${grandTotalScore}`);

        // --- Function to get color for founder ---
        function getFounderColor(index, alpha = 1) {
            const colors = [
                `rgba(25, 118, 210, ${alpha})`,   // Darker Blue
                `rgba(198, 40, 40, ${alpha})`,    // Darker Red
                `rgba(46, 125, 50, ${alpha})`,    // Darker Green
                `rgba(239, 108, 0, ${alpha})`,    // Darker Orange
                `rgba(123, 31, 162, ${alpha})`,   // Darker Purple
                `rgba(0, 131, 143, ${alpha})`,    // Darker Teal
                `rgba(191, 54, 12, ${alpha})`,    // Darker Deep Orange
                `rgba(93, 64, 55, ${alpha})`,     // Darker Brown
                `rgba(48, 63, 159, ${alpha})`,    // Darker Indigo
                `rgba(158, 157, 36, ${alpha})`    // Darker Lime
            ];
            return colors[index % colors.length];
        }

        // --- 5. Prepare Data ---
        let chartLabels = []; let percentageData = []; let hoursData = [];
        const trackedFounders = founderNames.filter(name => baseValues.hasOwnProperty(name));
        trackedFounders.forEach(name => {
            chartLabels.push(name);
            let currentScore = scores[name] || 0;
            let currentHours = rawHours[name] || 0;
            let percentage = (grandTotalScore > 0 && typeof currentScore === 'number' && !isNaN(currentScore)) ? (currentScore / grandTotalScore) * 100 : 0;
            percentageData.push(percentage);
            hoursData.push(currentHours);
        });
        logDebug("Chart data ready - Labels:", chartLabels, "Percents:", percentageData, "Hours:", hoursData);

        // --- 6. Display Results ---
        dv.el("h3", "Equity Split (%) - Text");

        // Create a styled container for text results
        const textResultsContainer = dv.el("div", "", {cls: "equity-text-results"});
        textResultsContainer.style.backgroundColor = "#ffffff";
        textResultsContainer.style.borderRadius = "8px";
        textResultsContainer.style.padding = "20px 25px";
        textResultsContainer.style.marginTop = "15px";
        textResultsContainer.style.marginBottom = "25px";
        textResultsContainer.style.boxShadow = "0 4px 12px rgba(0,0,0,0.12)";
        textResultsContainer.style.fontSize = "16px";
        textResultsContainer.style.lineHeight = "1.6";
        textResultsContainer.style.border = "1px solid #e0e0e0";

        // Add a title to the container
        const textResultsTitle = document.createElement("div");
        textResultsTitle.textContent = "Equity Distribution";
        textResultsTitle.style.fontSize = "19px";
        textResultsTitle.style.fontWeight = "bold";
        textResultsTitle.style.marginBottom = "18px";
        textResultsTitle.style.paddingBottom = "12px";
        textResultsTitle.style.borderBottom = "2px solid #e3f2fd";
        textResultsTitle.style.color = "#1976D2";
        textResultsTitle.style.letterSpacing = "0.3px";
        textResultsContainer.appendChild(textResultsTitle);

        if (grandTotalScore === 0) {
            textResultsContainer.appendChild(document.createTextNode("Total weighted contribution is zero."));
        } else {
            // Create a table for better alignment
            const table = document.createElement("table");
            table.style.width = "100%";
            table.style.borderCollapse = "separate";
            table.style.borderSpacing = "0 8px";

            trackedFounders.forEach((name, index) => {
                const row = document.createElement("tr");

                // Name cell
                const nameCell = document.createElement("td");
                nameCell.style.padding = "12px 15px 12px 15px";
                nameCell.style.fontWeight = "bold";
                nameCell.style.width = "30%";
                nameCell.style.fontSize = "16px";
                nameCell.style.color = "#333333";
                nameCell.style.letterSpacing = "0.2px";
                nameCell.textContent = name;

                // Value cell
                const valueCell = document.createElement("td");
                valueCell.style.padding = "12px 15px";
                valueCell.style.textAlign = "right";
                valueCell.style.width = "30%";

                // Create colored text for the percentage
                const badge = document.createElement("span");
                badge.textContent = percentageData[index].toFixed(2) + "%";
                badge.style.color = getFounderColor(index, 1).replace("rgba", "rgb").replace(", 1)", ")");
                badge.style.fontWeight = "bold";
                badge.style.fontSize = "16px";
                badge.style.display = "inline-block";
                badge.style.minWidth = "80px";
                badge.style.textAlign = "right";

                valueCell.appendChild(badge);

                // Bar visualization cell
                const barCell = document.createElement("td");
                barCell.style.padding = "12px 15px";
                barCell.style.width = "40%";

                const barContainer = document.createElement("div");
                barContainer.style.width = "100%";
                barContainer.style.height = "12px";
                barContainer.style.backgroundColor = "#f0f0f0";
                barContainer.style.borderRadius = "6px";
                barContainer.style.overflow = "hidden";
                barContainer.style.boxShadow = "inset 0 1px 2px rgba(0,0,0,0.05)";
                barContainer.style.border = "1px solid #e0e0e0";

                const bar = document.createElement("div");
                bar.style.height = "100%";
                bar.style.width = `${percentageData[index]}%`;
                bar.style.backgroundColor = getFounderColor(index, 0.7);
                bar.style.borderRadius = "6px";

                barContainer.appendChild(bar);
                barCell.appendChild(barContainer);

                // Add cells to row
                row.appendChild(nameCell);
                row.appendChild(valueCell);
                row.appendChild(barCell);

                // Add row to table
                table.appendChild(row);

                // No separators as requested
            });

            textResultsContainer.appendChild(table);
        }

        // --- 7. Improved Chart Rendering using window.renderChart ---
        dv.el("h3", "Equity Split (%) - Pie Chart");

        if (grandTotalScore > 0 && trackedFounders.length > 0) {
            try {
                // Create container for the pie chart
                const pieChartContainer = dv.el("div", "", {cls: "equity-pie-chart"});

                // Add style for chart container to improve readability
                pieChartContainer.style.height = "500px"; // Increase height for better readability
                pieChartContainer.style.marginTop = "20px";
                pieChartContainer.style.marginBottom = "40px"; // Add bottom margin to improve scrolling
                pieChartContainer.style.backgroundColor = "#ffffff";
                pieChartContainer.style.borderRadius = "8px";
                pieChartContainer.style.padding = "20px";
                pieChartContainer.style.boxShadow = "0 4px 12px rgba(0,0,0,0.12)";
                pieChartContainer.style.border = "1px solid #e0e0e0";

                // Pie chart data structure
                const pieChartData = {
                    type: 'pie',
                    data: {
                        labels: chartLabels,
                        datasets: [{
                            label: 'Equity Split',
                            data: percentageData,
                            backgroundColor: [
                                'rgba(25, 118, 210, 0.8)',   // Darker Blue
                                'rgba(198, 40, 40, 0.8)',    // Darker Red
                                'rgba(46, 125, 50, 0.8)',    // Darker Green
                                'rgba(239, 108, 0, 0.8)',    // Darker Orange
                                'rgba(123, 31, 162, 0.8)',   // Darker Purple
                                'rgba(0, 131, 143, 0.8)',    // Darker Teal
                                'rgba(191, 54, 12, 0.8)',    // Darker Deep Orange
                                'rgba(93, 64, 55, 0.8)',     // Darker Brown
                                'rgba(48, 63, 159, 0.8)',    // Darker Indigo
                                'rgba(158, 157, 36, 0.8)'    // Darker Lime
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        layout: {
                            padding: {
                                top: 20,
                                right: 30,
                                bottom: 20,
                                left: 20
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: 'Equity Split (%)',
                                font: {
                                    size: 18,
                                    weight: 'bold'
                                },
                                padding: {
                                    top: 10,
                                    bottom: 20
                                }
                            },
                            legend: {
                                position: 'top',
                                labels: {
                                    padding: 20,
                                    font: {
                                        size: 14
                                    },
                                    usePointStyle: true,
                                    boxWidth: 10
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleFont: {
                                    size: 14
                                },
                                bodyFont: {
                                    size: 13
                                },
                                padding: 12,
                                cornerRadius: 6,
                                callbacks: {
                                    label: function(context) {
                                        return `${context.label}: ${context.raw.toFixed(2)}%`;
                                    }
                                }
                            }
                        }
                    }
                };

                // Render the pie chart using window.renderChart
                if (chartsPluginAvailable) {
                    window.renderChart(pieChartData, pieChartContainer);
                } else {
                    pieChartContainer.innerText = "Charts plugin not available";
                }
            } catch (chartError) {
                logDebug("Error rendering pie chart:", chartError);
                dv.el("p", `❌ Error rendering pie chart: ${chartError.message}. Check if Charts plugin is working.`);
            }
        } else {
            dv.el("p", "Cannot display pie chart (total contribution is zero or no valid founders).");
        }

        dv.el("h3", "Total Raw Hours Contributed - Bar Chart");

        if (trackedFounders.length > 0) {
            try {
                // Create container for the bar chart
                const barChartContainer = dv.el("div", "", {cls: "equity-bar-chart"});

                // Add style for chart container to improve readability
                barChartContainer.style.height = "500px"; // Increase height for better readability
                barChartContainer.style.marginTop = "20px";
                barChartContainer.style.marginBottom = "40px"; // Add bottom margin to improve scrolling
                barChartContainer.style.backgroundColor = "#ffffff";
                barChartContainer.style.borderRadius = "8px";
                barChartContainer.style.padding = "20px";
                barChartContainer.style.boxShadow = "0 4px 12px rgba(0,0,0,0.12)";
                barChartContainer.style.border = "1px solid #e0e0e0";

                // Bar chart data structure
                const barChartData = {
                    type: 'bar',
                    data: {
                        labels: chartLabels,
                        datasets: [{
                            label: 'Total Hours',
                            data: hoursData,
                            backgroundColor: [
                                'rgba(25, 118, 210, 0.7)',   // Darker Blue
                                'rgba(198, 40, 40, 0.7)',    // Darker Red
                                'rgba(46, 125, 50, 0.7)',    // Darker Green
                                'rgba(239, 108, 0, 0.7)',    // Darker Orange
                                'rgba(123, 31, 162, 0.7)',   // Darker Purple
                                'rgba(0, 131, 143, 0.7)',    // Darker Teal
                                'rgba(191, 54, 12, 0.7)',    // Darker Deep Orange
                                'rgba(93, 64, 55, 0.7)',     // Darker Brown
                                'rgba(48, 63, 159, 0.7)',    // Darker Indigo
                                'rgba(158, 157, 36, 0.7)'    // Darker Lime
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        layout: {
                            padding: {
                                top: 20,
                                right: 30,
                                bottom: 20,
                                left: 20
                            }
                        },
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: 'Founder',
                                    font: {
                                        size: 14,
                                        weight: 'bold'
                                    },
                                    padding: {top: 10, bottom: 10}
                                },
                                ticks: {
                                    font: {
                                        size: 12
                                    }
                                },
                                grid: {
                                    display: true,
                                    color: 'rgba(200, 200, 200, 0.3)'
                                }
                            },
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Total Hours',
                                    font: {
                                        size: 14,
                                        weight: 'bold'
                                    },
                                    padding: {top: 10, bottom: 10}
                                },
                                ticks: {
                                    font: {
                                        size: 12
                                    },
                                    padding: 10,
                                    stepSize: Math.ceil(Math.max(...hoursData) / 5)
                                },
                                grid: {
                                    display: true,
                                    color: 'rgba(200, 200, 200, 0.3)'
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: 'Total Raw Hours Contributed',
                                font: {
                                    size: 18,
                                    weight: 'bold'
                                },
                                padding: {
                                    top: 10,
                                    bottom: 20
                                }
                            },
                            legend: {
                                display: false
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleFont: {
                                    size: 14
                                },
                                bodyFont: {
                                    size: 13
                                },
                                padding: 12,
                                cornerRadius: 6,
                                callbacks: {
                                    label: function(context) {
                                        return `${context.label}: ${context.raw.toFixed(1)} hours`;
                                    }
                                }
                            }
                        }
                    }
                };

                // Render the bar chart using window.renderChart
                if (chartsPluginAvailable) {
                    window.renderChart(barChartData, barChartContainer);
                } else {
                    barChartContainer.innerText = "Charts plugin not available";
                }
            } catch (chartError) {
                logDebug("Error rendering bar chart:", chartError);
                dv.el("p", `❌ Error rendering bar chart: ${chartError.message}. Check if Charts plugin is working.`);
            }
        } else {
            dv.el("p", "Cannot display bar chart (no valid founders found).");
        }

        // --- Summary ---
        dv.paragraph("---");
        dv.el("h4", "Summary Data");

        // Create a styled container for summary data
        const summaryContainer = dv.el("div", "", {cls: "equity-summary"});
        summaryContainer.style.backgroundColor = "#ffffff";
        summaryContainer.style.borderRadius = "8px";
        summaryContainer.style.padding = "20px 25px";
        summaryContainer.style.marginTop = "15px";
        summaryContainer.style.marginBottom = "40px";
        summaryContainer.style.boxShadow = "0 4px 12px rgba(0,0,0,0.12)";
        summaryContainer.style.border = "1px solid #e0e0e0";

        // Add a title to the container
        const summaryTitle = document.createElement("div");
        summaryTitle.textContent = "Summary Data";
        summaryTitle.style.fontSize = "19px";
        summaryTitle.style.fontWeight = "bold";
        summaryTitle.style.marginBottom = "18px";
        summaryTitle.style.paddingBottom = "12px";
        summaryTitle.style.borderBottom = "2px solid #e3f2fd";
        summaryTitle.style.color = "#1976D2";
        summaryTitle.style.letterSpacing = "0.3px";
        summaryContainer.appendChild(summaryTitle);

        // Total weighted points
        const totalPointsDiv = document.createElement("div");
        totalPointsDiv.style.fontSize = "16px";
        totalPointsDiv.style.fontWeight = "bold";
        totalPointsDiv.style.marginBottom = "25px";
        totalPointsDiv.style.padding = "18px 20px";
        totalPointsDiv.style.backgroundColor = "#f1f8fe";
        totalPointsDiv.style.borderRadius = "8px";
        totalPointsDiv.style.border = "1px solid #c8e1fb";
        totalPointsDiv.style.color = "#333333";
        totalPointsDiv.style.display = "flex";
        totalPointsDiv.style.justifyContent = "space-between";
        totalPointsDiv.style.alignItems = "center";
        totalPointsDiv.style.boxShadow = "0 2px 4px rgba(0,0,0,0.04)";

        const pointsLabel = document.createElement("span");
        pointsLabel.textContent = "Total Weighted Points:";
        pointsLabel.style.fontSize = "17px";
        pointsLabel.style.letterSpacing = "0.2px";

        const pointsValue = document.createElement("span");
        pointsValue.textContent = grandTotalScore.toFixed(2);
        pointsValue.style.fontSize = "22px";
        pointsValue.style.color = "#1565C0";
        pointsValue.style.fontWeight = "bold";
        pointsValue.style.backgroundColor = "#ffffff";
        pointsValue.style.padding = "8px 16px";
        pointsValue.style.borderRadius = "6px";
        pointsValue.style.border = "1px solid #bbdefb";
        pointsValue.style.boxShadow = "0 2px 4px rgba(25, 118, 210, 0.1)";
        pointsValue.style.letterSpacing = "0.5px";

        totalPointsDiv.appendChild(pointsLabel);
        totalPointsDiv.appendChild(pointsValue);
        summaryContainer.appendChild(totalPointsDiv);

        // Create a two-column layout for scores and hours
        const columnsContainer = document.createElement("div");
        columnsContainer.style.display = "flex";
        columnsContainer.style.gap = "25px";
        columnsContainer.style.marginBottom = "15px";

        // Raw scores section
        const scoresSection = document.createElement("div");
        scoresSection.style.flex = "1";
        scoresSection.style.backgroundColor = "#f8f9fa";
        scoresSection.style.borderRadius = "8px";
        scoresSection.style.padding = "20px";
        scoresSection.style.border = "1px solid #e9ecef";
        scoresSection.style.boxShadow = "0 2px 4px rgba(0,0,0,0.04)";

        const scoresTitle = document.createElement("div");
        scoresTitle.textContent = "Raw Scores (Points)";
        scoresTitle.style.fontSize = "17px";
        scoresTitle.style.fontWeight = "bold";
        scoresTitle.style.marginBottom = "16px";
        scoresTitle.style.paddingBottom = "10px";
        scoresTitle.style.borderBottom = "1px solid #e9ecef";
        scoresTitle.style.color = "#333333";
        scoresTitle.style.letterSpacing = "0.2px";
        scoresSection.appendChild(scoresTitle);

        // Create a table for scores
        const scoresTable = document.createElement("table");
        scoresTable.style.width = "100%";
        scoresTable.style.borderCollapse = "separate";
        scoresTable.style.borderSpacing = "0 8px";

        trackedFounders.forEach((name, index) => {
            const row = document.createElement("tr");

            // Name cell
            const nameCell = document.createElement("td");
            nameCell.style.padding = "12px 15px 12px 15px";
            nameCell.style.fontWeight = "bold";
            nameCell.style.color = "#333333";
            nameCell.style.fontSize = "16px";
            nameCell.style.letterSpacing = "0.2px";
            nameCell.style.width = "50%";
            nameCell.textContent = name;

            // Value cell
            const valueCell = document.createElement("td");
            valueCell.style.padding = "12px 15px";
            valueCell.style.textAlign = "right";
            valueCell.style.width = "50%";

            // Create colored text for the score
            const scoreBadge = document.createElement("span");
            scoreBadge.textContent = (scores[name] || 0).toFixed(2);
            scoreBadge.style.color = getFounderColor(index, 1).replace("rgba", "rgb").replace(", 1)", ")");
            scoreBadge.style.fontWeight = "bold";
            scoreBadge.style.fontSize = "16px";
            scoreBadge.style.display = "inline-block";
            scoreBadge.style.minWidth = "80px";
            scoreBadge.style.textAlign = "right";

            valueCell.appendChild(scoreBadge);

            row.appendChild(nameCell);
            row.appendChild(valueCell);
            scoresTable.appendChild(row);

            // No separators as requested
        });

        scoresSection.appendChild(scoresTable);

        // Raw hours section
        const hoursSection = document.createElement("div");
        hoursSection.style.flex = "1";
        hoursSection.style.backgroundColor = "#f8f9fa";
        hoursSection.style.borderRadius = "8px";
        hoursSection.style.padding = "20px";
        hoursSection.style.border = "1px solid #e9ecef";
        hoursSection.style.boxShadow = "0 2px 4px rgba(0,0,0,0.04)";

        const hoursTitle = document.createElement("div");
        hoursTitle.textContent = "Raw Hours Contributed";
        hoursTitle.style.fontSize = "17px";
        hoursTitle.style.fontWeight = "bold";
        hoursTitle.style.marginBottom = "16px";
        hoursTitle.style.paddingBottom = "10px";
        hoursTitle.style.borderBottom = "1px solid #e9ecef";
        hoursTitle.style.color = "#333333";
        hoursTitle.style.letterSpacing = "0.2px";
        hoursSection.appendChild(hoursTitle);

        // Create a table for hours
        const hoursTable = document.createElement("table");
        hoursTable.style.width = "100%";
        hoursTable.style.borderCollapse = "separate";
        hoursTable.style.borderSpacing = "0 8px";

        trackedFounders.forEach((name, index) => {
            const row = document.createElement("tr");

            // Name cell
            const nameCell = document.createElement("td");
            nameCell.style.padding = "12px 15px 12px 15px";
            nameCell.style.fontWeight = "bold";
            nameCell.style.color = "#333333";
            nameCell.style.fontSize = "16px";
            nameCell.style.letterSpacing = "0.2px";
            nameCell.style.width = "50%";
            nameCell.textContent = name;

            // Value cell
            const valueCell = document.createElement("td");
            valueCell.style.padding = "12px 15px";
            valueCell.style.textAlign = "right";
            valueCell.style.width = "50%";

            // Create colored text for the hours
            const hoursBadge = document.createElement("span");
            hoursBadge.textContent = (rawHours[name] || 0).toFixed(2) + " hrs";
            hoursBadge.style.color = getFounderColor(index, 1).replace("rgba", "rgb").replace(", 1)", ")");
            hoursBadge.style.fontWeight = "bold";
            hoursBadge.style.fontSize = "16px";
            hoursBadge.style.display = "inline-block";
            hoursBadge.style.minWidth = "80px";
            hoursBadge.style.textAlign = "right";

            valueCell.appendChild(hoursBadge);

            row.appendChild(nameCell);
            row.appendChild(valueCell);
            hoursTable.appendChild(row);

            // No separators as requested
        });

        hoursSection.appendChild(hoursTable);

        // Add both sections to the columns container
        columnsContainer.appendChild(scoresSection);
        columnsContainer.appendChild(hoursSection);

        // Add columns container to the summary container
        summaryContainer.appendChild(columnsContainer);

    } catch (error) {
        logDebug("FATAL SCRIPT ERROR:", error);
        dv.el("p", `❌ **SCRIPT ERROR:** ${error.message}. Check console.`);
    }
})(); // Immediately invoke the async function

logDebug("Equity calculation script finished.");
```

<!-- Separator to improve scrolling stability -->

---

## 📈 Cumulative Hours Over Time
```dataviewjs
// Add a spacer div to improve scrolling stability
dv.el("div", "", {attr: {style: "height: 20px;"}});

// --- Configuration ---
// DO NOT MODIFY THESE PATHS - They should match the ones in the equity calculation script above
const referenceFilePath = "99 - data/equitySplit/Equity Reference.md"; // <== CHECK PATH
const foundersFolderPath = '"99 - data/equitySplit/Founders"'; // <== CHECK PATH
const founderNames = ["Shashank", "Anshul", "Parth", "Dhaval", "Vedant"]; // <== CHECK NAMES

// --- Helper Function for Logging ---
function timeChartLogDebug(message, ...args) {
    console.log(`[TimeChart] ${message}`, args.length > 0 ? args : "");
}

// --- Main Async Function Wrapper ---
await (async () => {
    timeChartLogDebug("Starting time chart script...");

    // --- Add a small delay to ensure plugins are loaded ---
    await new Promise(resolve => setTimeout(resolve, 500));

    // --- Check for Charts Plugin ---
    let chartsPluginAvailable = false;
    try {
        chartsPluginAvailable = typeof window.renderChart === 'function';
        timeChartLogDebug(`Charts plugin check: ${chartsPluginAvailable ? 'FOUND' : 'NOT FOUND'}`);
    } catch (error) {
        timeChartLogDebug(`Error checking for Charts plugin: ${error.message}`);
        chartsPluginAvailable = false;
    }

    if (!chartsPluginAvailable) {
        dv.el("p", "⚠️ **Warning:** The 'Charts' plugin is not detected. Please ensure it's installed and enabled for chart visualization.");
        return;
    }

    try {
        // --- Load Contribution Files ---
        timeChartLogDebug(`Processing files from folder: ${foundersFolderPath}`);
        const files = dv.pages(foundersFolderPath);
        timeChartLogDebug(`Found ${files.length} files.`);

        if (files.length === 0) {
            dv.el("p", `ℹ️ No contribution files found.`);
            return;
        }

        // --- Process Files and Extract Time Data ---
        // Create a data structure to hold time-series data
        const timeData = {};
        const cumulativeData = {};

        // Initialize data structures for each founder
        founderNames.forEach(name => {
            timeData[name] = [];
            cumulativeData[name] = [];
        });

        // Process each file and extract date and hours
        files.forEach(item => {
            const founderName = item.name;
            if (!founderNames.includes(founderName)) return;

            const countValue = parseFloat(item.count);
            if (isNaN(countValue)) return;

            // Get the creation date from the file (prioritizing metadata 'created' field)
            const creationDate = item.created || item.file.ctime;
            if (!creationDate) return;

            // Add data point
            timeData[founderName].push({
                date: creationDate,
                hours: countValue
            });
        });

        // Sort data points by date for each founder
        founderNames.forEach(name => {
            if (timeData[name].length === 0) return;

            // Sort by date
            timeData[name].sort((a, b) => a.date - b.date);

            // Calculate cumulative hours
            let cumulativeHours = 0;
            timeData[name].forEach(point => {
                cumulativeHours += point.hours;
                cumulativeData[name].push({
                    date: point.date,
                    hours: cumulativeHours
                });
            });
        });

        // --- Create Time Aggregation Functions ---
        // Function to format date as YYYY-MM-DD
        function formatDate(date) {
            const d = new Date(date);
            return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
        }

        // Function to format date as YYYY-MM (month)
        function formatMonth(date) {
            const d = new Date(date);
            return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`;
        }

        // Function to get week number
        function getWeekNumber(date) {
            const d = new Date(date);
            d.setHours(0, 0, 0, 0);
            d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
            const week1 = new Date(d.getFullYear(), 0, 4);
            return 1 + Math.round(((d - week1) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
        }

        // Function to format date as YYYY-WW (week)
        function formatWeek(date) {
            const d = new Date(date);
            const weekNum = getWeekNumber(d);
            return `${d.getFullYear()}-W${String(weekNum).padStart(2, '0')}`;
        }

        // --- Aggregate Data by Day, Week, and Month ---
        function aggregateDataByPeriod(periodFormatter) {
            const aggregatedData = {};
            const labels = new Set();

            // Initialize aggregated data structure
            founderNames.forEach(name => {
                aggregatedData[name] = {};
            });

            // Aggregate data points
            founderNames.forEach(name => {
                cumulativeData[name].forEach(point => {
                    const periodKey = periodFormatter(point.date);
                    labels.add(periodKey);

                    // Update the latest cumulative value for this period
                    aggregatedData[name][periodKey] = point.hours;
                });
            });

            // Convert to sorted arrays for charting
            const sortedLabels = Array.from(labels).sort();
            const datasets = [];

            // Create datasets for each founder
            founderNames.forEach((name, index) => {
                const data = sortedLabels.map(label => aggregatedData[name][label] || null);

                // Fill in null values with previous values to create a step chart effect
                let lastValue = 0;
                for (let i = 0; i < data.length; i++) {
                    if (data[i] === null) {
                        data[i] = lastValue;
                    } else {
                        lastValue = data[i];
                    }
                }

                datasets.push({
                    label: name,
                    data: data,
                    borderColor: getFounderColor(index),
                    backgroundColor: getFounderColor(index, 0.1),
                    borderWidth: 3,
                    tension: 0.2,
                    fill: false,
                    pointRadius: 4,
                    pointHoverRadius: 7,
                    pointBackgroundColor: getFounderColor(index),
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    lineTension: 0.4
                });
            });

            return { labels: sortedLabels, datasets };
        }

        // Function to get color for founder
        function getFounderColor(index, alpha = 1) {
            const colors = [
                `rgba(33, 150, 243, ${alpha})`,   // Bright Blue
                `rgba(233, 30, 99, ${alpha})`,    // Pink
                `rgba(76, 175, 80, ${alpha})`,    // Green
                `rgba(255, 152, 0, ${alpha})`,    // Orange
                `rgba(156, 39, 176, ${alpha})`,   // Purple
                `rgba(0, 188, 212, ${alpha})`,    // Cyan
                `rgba(255, 87, 34, ${alpha})`,    // Deep Orange
                `rgba(121, 85, 72, ${alpha})`,    // Brown
                `rgba(63, 81, 181, ${alpha})`,    // Indigo
                `rgba(205, 220, 57, ${alpha})`    // Lime
            ];
            return colors[index % colors.length];
        }

        // --- Create UI for Time Period Selection ---
        dv.el("h3", "Cumulative Hours Over Time");

        // Create container for period selection buttons
        const periodButtonsContainer = dv.el("div", "", {cls: "time-period-buttons"});

        // Add style for buttons and chart container
        const style = document.createElement("style");
        style.textContent = `
            .time-period-buttons {
                display: flex;
                gap: 15px;
                margin-bottom: 20px;
                justify-content: center;
            }
            .time-period-button {
                padding: 10px 22px;
                border: 1px solid #d0d0d0;
                border-radius: 6px;
                cursor: pointer;
                background-color: #f8f9fa;
                font-size: 14px;
                font-weight: 600;
                transition: all 0.2s ease;
                box-shadow: 0 2px 4px rgba(0,0,0,0.08);
                color: #555555;
            }
            .time-period-button:hover {
                background-color: #e9ecef;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                color: #333333;
            }
            .time-period-button.active {
                background-color: #1976D2;
                color: white;
                border-color: #1565C0;
                box-shadow: 0 2px 6px rgba(25, 118, 210, 0.4);
            }
            .cumulative-hours-chart {
                background-color: #ffffff;
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.12);
                border: 1px solid #e0e0e0;
            }
            .pagination-container {
                display: flex;
                justify-content: center;
                gap: 10px;
                margin-top: 15px;
            }
            .pagination-button {
                padding: 8px 16px;
                border: 1px solid #d0d0d0;
                border-radius: 6px;
                cursor: pointer;
                background-color: #f8f9fa;
                font-size: 14px;
                font-weight: 600;
                transition: all 0.2s ease;
                box-shadow: 0 2px 4px rgba(0,0,0,0.08);
                color: #555555;
            }
            .pagination-button:hover {
                background-color: #e9ecef;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                color: #333333;
            }
            .pagination-button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
            }
            .pagination-info {
                display: flex;
                align-items: center;
                font-size: 14px;
                color: #555555;
            }
        `;
        periodButtonsContainer.appendChild(style);

        // Create buttons for day, week, and month views
        const dayButton = document.createElement("button");
        dayButton.textContent = "Daily";
        dayButton.className = "time-period-button active";
        periodButtonsContainer.appendChild(dayButton);

        const weekButton = document.createElement("button");
        weekButton.textContent = "Weekly";
        weekButton.className = "time-period-button";
        periodButtonsContainer.appendChild(weekButton);

        const monthButton = document.createElement("button");
        monthButton.textContent = "Monthly";
        monthButton.className = "time-period-button";
        periodButtonsContainer.appendChild(monthButton);

        // Create container for the chart with improved height
        const chartContainer = dv.el("div", "", {cls: "cumulative-hours-chart"});

        // Add style for chart container to improve readability
        chartContainer.style.height = "500px"; // Increase height for better readability
        chartContainer.style.marginTop = "20px";
        chartContainer.style.marginBottom = "20px"; // Reduced margin to make room for pagination
        chartContainer.style.backgroundColor = "#ffffff";
        chartContainer.style.borderRadius = "8px";
        chartContainer.style.padding = "20px";
        chartContainer.style.boxShadow = "0 4px 12px rgba(0,0,0,0.12)";
        chartContainer.style.border = "1px solid #e0e0e0";

        // Create pagination container
        const paginationContainer = dv.el("div", "", {cls: "pagination-container"});
        paginationContainer.style.marginBottom = "40px"; // Add bottom margin for spacing

        // Create previous button
        const prevButton = document.createElement("button");
        prevButton.textContent = "← Previous 15";
        prevButton.className = "pagination-button";
        prevButton.disabled = true; // Initially disabled
        paginationContainer.appendChild(prevButton);

        // Create pagination info
        const paginationInfo = document.createElement("div");
        paginationInfo.className = "pagination-info";
        paginationContainer.appendChild(paginationInfo);

        // Create next button
        const nextButton = document.createElement("button");
        nextButton.textContent = "Next 15 →";
        nextButton.className = "pagination-button";
        nextButton.disabled = true; // Initially disabled
        paginationContainer.appendChild(nextButton);

        // Variables to track pagination state
        let currentPage = 0;
        let allLabels = [];
        let allDatasets = [];
        const labelsPerPage = 15;

        // Function to update chart based on selected period and page
        function updateChart(periodType, page = 0) {
            // Update button styles
            dayButton.className = "time-period-button" + (periodType === "day" ? " active" : "");
            weekButton.className = "time-period-button" + (periodType === "week" ? " active" : "");
            monthButton.className = "time-period-button" + (periodType === "month" ? " active" : "");

            // Aggregate data based on selected period
            let chartData;
            if (periodType === "day") {
                chartData = aggregateDataByPeriod(formatDate);
            } else if (periodType === "week") {
                chartData = aggregateDataByPeriod(formatWeek);
            } else if (periodType === "month") {
                chartData = aggregateDataByPeriod(formatMonth);
            }

            // Store all labels and datasets
            allLabels = chartData.labels;
            allDatasets = chartData.datasets;

            // Calculate total pages
            const totalPages = Math.ceil(allLabels.length / labelsPerPage);

            // Ensure current page is valid
            currentPage = Math.max(0, Math.min(page, totalPages - 1));

            // Calculate start and end indices for current page
            const startIdx = currentPage * labelsPerPage;
            const endIdx = Math.min(startIdx + labelsPerPage, allLabels.length);

            // Get labels for current page
            const pageLabels = allLabels.slice(startIdx, endIdx);

            // Get data for current page
            const pageDatasets = allDatasets.map(dataset => ({
                ...dataset,
                data: dataset.data.slice(startIdx, endIdx)
            }));

            // Update pagination buttons state
            prevButton.disabled = currentPage === 0;
            nextButton.disabled = currentPage >= totalPages - 1;

            // Update pagination info
            if (allLabels.length > 0) {
                paginationInfo.textContent = `Showing ${startIdx + 1}-${endIdx} of ${allLabels.length} dates`;
            } else {
                paginationInfo.textContent = "No data available";
            }

            // Create chart configuration
            const chartConfig = {
                type: 'line',
                data: {
                    labels: pageLabels,
                    datasets: pageDatasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    layout: {
                        padding: {
                            top: 20,
                            right: 30,
                            bottom: 20,
                            left: 20
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: periodType === "day" ? "Date" : (periodType === "week" ? "Week" : "Month"),
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                padding: {top: 10, bottom: 10}
                            },
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45,
                                font: {
                                    size: 12
                                }
                            },
                            grid: {
                                display: true,
                                color: 'rgba(200, 200, 200, 0.3)'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Cumulative Hours',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                padding: {top: 10, bottom: 10}
                            },
                            ticks: {
                                font: {
                                    size: 12
                                },
                                padding: 10,
                                stepSize: Math.ceil(Math.max(...chartData.datasets.map(ds =>
                                    Math.max(...ds.data.filter(val => val !== null)))) / 5)
                            },
                            grid: {
                                display: true,
                                color: 'rgba(200, 200, 200, 0.3)'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: `Cumulative Hours (${periodType === "day" ? "Daily" : (periodType === "week" ? "Weekly" : "Monthly")})`,
                            font: {
                                size: 18,
                                weight: 'bold'
                            },
                            padding: {
                                top: 10,
                                bottom: 20
                            }
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                padding: 20,
                                font: {
                                    size: 14
                                },
                                usePointStyle: true,
                                boxWidth: 10
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleFont: {
                                size: 14
                            },
                            bodyFont: {
                                size: 13
                            },
                            padding: 12,
                            cornerRadius: 6,
                            callbacks: {
                                label: function(context) {
                                    return `${context.dataset.label}: ${context.raw.toFixed(1)} hours`;
                                }
                            }
                        }
                    }
                }
            };

            // Clear previous chart
            chartContainer.innerHTML = '';

            // Render the chart
            window.renderChart(chartConfig, chartContainer);
        }

        // Track current period type
        let currentPeriodType = "day";

        // Add event listeners to period buttons
        dayButton.addEventListener("click", () => {
            currentPeriodType = "day";
            currentPage = 0; // Reset to first page when changing period type
            updateChart(currentPeriodType, currentPage);
        });

        weekButton.addEventListener("click", () => {
            currentPeriodType = "week";
            currentPage = 0; // Reset to first page when changing period type
            updateChart(currentPeriodType, currentPage);
        });

        monthButton.addEventListener("click", () => {
            currentPeriodType = "month";
            currentPage = 0; // Reset to first page when changing period type
            updateChart(currentPeriodType, currentPage);
        });

        // Add event listeners to pagination buttons
        prevButton.addEventListener("click", () => {
            if (currentPage > 0) {
                currentPage--;
                updateChart(currentPeriodType, currentPage);
            }
        });

        nextButton.addEventListener("click", () => {
            const totalPages = Math.ceil(allLabels.length / labelsPerPage);
            if (currentPage < totalPages - 1) {
                currentPage++;
                updateChart(currentPeriodType, currentPage);
            }
        });

        // Initialize with daily view
        updateChart("day", 0);

    } catch (error) {
        timeChartLogDebug("ERROR:", error);
        dv.el("p", `❌ **ERROR:** ${error.message}. Check console for details.`);
    }
})();
```

<!-- Separator to improve scrolling stability -->

---

## 📏 Daily Hours Logged (Non-Cumulative Line Chart)
```dataviewjs
// Add a spacer div to improve scrolling stability
dv.el("div", "", {attr: {style: "height: 20px;"}});

// --- Configuration ---
// DO NOT MODIFY THESE PATHS - They should match the ones in the equity calculation script above
const referenceFilePath = "99 - data/equitySplit/Equity Reference.md"; // <== CHECK PATH
const foundersFolderPath = '"99 - data/equitySplit/Founders"'; // <== CHECK PATH
const founderNames = ["Shashank", "Anshul", "Parth", "Dhaval", "Vedant"]; // <== CHECK NAMES

// --- Helper Function for Logging ---
function dailyLogDebug(message, ...args) {
    console.log(`[DailyChart] ${message}`, args.length > 0 ? args : "");
}

// --- Main Async Function Wrapper ---
await (async () => {
    dailyLogDebug("Starting daily hours chart script...");

    // --- Add a small delay to ensure plugins are loaded ---
    await new Promise(resolve => setTimeout(resolve, 500));

    // --- Check for Charts Plugin ---
    let chartsPluginAvailable = false;
    try {
        chartsPluginAvailable = typeof window.renderChart === 'function';
        dailyLogDebug(`Charts plugin check: ${chartsPluginAvailable ? 'FOUND' : 'NOT FOUND'}`);
    } catch (error) {
        dailyLogDebug(`Error checking for Charts plugin: ${error.message}`);
        chartsPluginAvailable = false;
    }

    if (!chartsPluginAvailable) {
        dv.el("p", "⚠️ **Warning:** The 'Charts' plugin is not detected. Please ensure it's installed and enabled for chart visualization.");
        return;
    }

    try {
        // --- Load Contribution Files ---
        dailyLogDebug(`Processing files from folder: ${foundersFolderPath}`);
        const files = dv.pages(foundersFolderPath);
        dailyLogDebug(`Found ${files.length} files.`);

        if (files.length === 0) {
            dv.el("p", `ℹ️ No contribution files found.`);
            return;
        }

        // --- Process Files and Extract Time Data ---
        // Create a data structure to hold time-series data
        const dailyData = {};

        // Initialize data structures for each founder
        founderNames.forEach(name => {
            dailyData[name] = {};
        });

        // Process each file and extract date and hours
        files.forEach(item => {
            const founderName = item.name;
            if (!founderNames.includes(founderName)) return;

            const countValue = parseFloat(item.count);
            if (isNaN(countValue)) return;

            // Get the creation date from the file (prioritizing metadata 'created' field)
            const creationDate = item.created || item.file.ctime;
            if (!creationDate) return;

            // Format date as YYYY-MM-DD
            const dateKey = formatDate(creationDate);

            // Skip entries from 2025-04-19 as requested
            if (dateKey === "2025-04-19") return;

            // Add hours to the appropriate date
            if (!dailyData[founderName][dateKey]) {
                dailyData[founderName][dateKey] = 0;
            }
            dailyData[founderName][dateKey] += countValue;
        });

        // --- Create Time Aggregation Functions ---
        // Function to format date as YYYY-MM-DD
        function formatDate(date) {
            const d = new Date(date);
            return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
        }

        // Function to format date as YYYY-MM (month)
        function formatMonth(date) {
            const d = new Date(date);
            return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`;
        }

        // Function to get week number
        function getWeekNumber(date) {
            const d = new Date(date);
            d.setHours(0, 0, 0, 0);
            d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
            const week1 = new Date(d.getFullYear(), 0, 4);
            return 1 + Math.round(((d - week1) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
        }

        // Function to format date as YYYY-WW (week)
        function formatWeek(date) {
            const d = new Date(date);
            const weekNum = getWeekNumber(d);
            return `${d.getFullYear()}-W${String(weekNum).padStart(2, '0')}`;
        }

        // --- Aggregate Data by Day, Week, and Month ---
        function aggregateDataByPeriod(periodFormatter) {
            const aggregatedData = {};
            const allDates = new Set();

            // If we're aggregating by day, we can use the dailyData directly
            if (periodFormatter === formatDate) {
                founderNames.forEach(name => {
                    Object.keys(dailyData[name]).forEach(date => {
                        allDates.add(date);
                    });
                });

                const sortedDates = Array.from(allDates).sort();
                const datasets = [];

                founderNames.forEach((name, index) => {
                    const data = sortedDates.map(date => dailyData[name][date] || 0);

                    datasets.push({
                        label: name,
                        data: data,
                        borderColor: getFounderColor(index),
                        backgroundColor: getFounderColor(index, 0.1),
                        borderWidth: 3,
                        tension: 0.2,
                        fill: false,
                        pointRadius: 4,
                        pointHoverRadius: 7,
                        pointBackgroundColor: getFounderColor(index),
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        lineTension: 0.4
                    });
                });

                return { labels: sortedDates, datasets };
            } else {
                // For week and month, we need to aggregate the daily data
                founderNames.forEach(name => {
                    aggregatedData[name] = {};

                    Object.entries(dailyData[name]).forEach(([dateStr, hours]) => {
                        const periodKey = periodFormatter(new Date(dateStr));
                        allDates.add(periodKey);

                        if (!aggregatedData[name][periodKey]) {
                            aggregatedData[name][periodKey] = 0;
                        }
                        aggregatedData[name][periodKey] += hours;
                    });
                });

                const sortedPeriods = Array.from(allDates).sort();
                const datasets = [];

                founderNames.forEach((name, index) => {
                    const data = sortedPeriods.map(period => aggregatedData[name][period] || 0);

                    datasets.push({
                        label: name,
                        data: data,
                        borderColor: getFounderColor(index),
                        backgroundColor: getFounderColor(index, 0.1),
                        borderWidth: 3,
                        tension: 0.2,
                        fill: false,
                        pointRadius: 4,
                        pointHoverRadius: 7,
                        pointBackgroundColor: getFounderColor(index),
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        lineTension: 0.4
                    });
                });

                return { labels: sortedPeriods, datasets };
            }
        }

        // Function to get color for founder
        function getFounderColor(index, alpha = 1) {
            const colors = [
                `rgba(33, 150, 243, ${alpha})`,   // Bright Blue
                `rgba(233, 30, 99, ${alpha})`,    // Pink
                `rgba(76, 175, 80, ${alpha})`,    // Green
                `rgba(255, 152, 0, ${alpha})`,    // Orange
                `rgba(156, 39, 176, ${alpha})`,   // Purple
                `rgba(0, 188, 212, ${alpha})`,    // Cyan
                `rgba(255, 87, 34, ${alpha})`,    // Deep Orange
                `rgba(121, 85, 72, ${alpha})`,    // Brown
                `rgba(63, 81, 181, ${alpha})`,    // Indigo
                `rgba(205, 220, 57, ${alpha})`    // Lime
            ];
            return colors[index % colors.length];
        }

        // --- Create UI for Time Period Selection ---
        dv.el("h3", "Daily Hours Logged (Non-Cumulative Line Chart)");

        // Create container for period selection buttons
        const periodButtonsContainer = dv.el("div", "", {cls: "daily-period-buttons"});

        // Add style for buttons and chart container
        const style = document.createElement("style");
        style.textContent = `
            .daily-period-buttons {
                display: flex;
                gap: 15px;
                margin-bottom: 20px;
                justify-content: center;
            }
            .daily-period-button {
                padding: 10px 22px;
                border: 1px solid #d0d0d0;
                border-radius: 6px;
                cursor: pointer;
                background-color: #f8f9fa;
                font-size: 14px;
                font-weight: 600;
                transition: all 0.2s ease;
                box-shadow: 0 2px 4px rgba(0,0,0,0.08);
                color: #555555;
            }
            .daily-period-button:hover {
                background-color: #e9ecef;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                color: #333333;
            }
            .daily-period-button.active {
                background-color: #1976D2;
                color: white;
                border-color: #1565C0;
                box-shadow: 0 2px 6px rgba(25, 118, 210, 0.4);
            }
            .daily-hours-chart {
                background-color: #ffffff;
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.12);
                border: 1px solid #e0e0e0;
                height: 500px;
                margin-top: 20px;
                margin-bottom: 20px;
            }
            .daily-pagination-container {
                display: flex;
                justify-content: center;
                gap: 10px;
                margin-top: 15px;
                margin-bottom: 40px;
            }
            .daily-pagination-button {
                padding: 8px 16px;
                border: 1px solid #d0d0d0;
                border-radius: 6px;
                cursor: pointer;
                background-color: #f8f9fa;
                font-size: 14px;
                font-weight: 600;
                transition: all 0.2s ease;
                box-shadow: 0 2px 4px rgba(0,0,0,0.08);
                color: #555555;
            }
            .daily-pagination-button:hover {
                background-color: #e9ecef;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                color: #333333;
            }
            .daily-pagination-button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
            }
            .daily-pagination-info {
                display: flex;
                align-items: center;
                font-size: 14px;
                color: #555555;
            }
        `;
        periodButtonsContainer.appendChild(style);

        // Create buttons for day, week, and month views
        const dayButton = document.createElement("button");
        dayButton.textContent = "Daily";
        dayButton.className = "daily-period-button active";
        periodButtonsContainer.appendChild(dayButton);

        const weekButton = document.createElement("button");
        weekButton.textContent = "Weekly";
        weekButton.className = "daily-period-button";
        periodButtonsContainer.appendChild(weekButton);

        const monthButton = document.createElement("button");
        monthButton.textContent = "Monthly";
        monthButton.className = "daily-period-button";
        periodButtonsContainer.appendChild(monthButton);

        // Create container for the chart
        const chartContainer = dv.el("div", "", {cls: "daily-hours-chart"});

        // Create pagination container
        const paginationContainer = dv.el("div", "", {cls: "daily-pagination-container"});

        // Create previous button
        const prevButton = document.createElement("button");
        prevButton.textContent = "← Previous 15";
        prevButton.className = "daily-pagination-button";
        prevButton.disabled = true; // Initially disabled
        paginationContainer.appendChild(prevButton);

        // Create pagination info
        const paginationInfo = document.createElement("div");
        paginationInfo.className = "daily-pagination-info";
        paginationContainer.appendChild(paginationInfo);

        // Create next button
        const nextButton = document.createElement("button");
        nextButton.textContent = "Next 15 →";
        nextButton.className = "daily-pagination-button";
        nextButton.disabled = true; // Initially disabled
        paginationContainer.appendChild(nextButton);

        // Variables to track pagination state
        let currentPage = 0;
        let allLabels = [];
        let allDatasets = [];
        const labelsPerPage = 15;

        // Function to update chart based on selected period and page
        function updateChart(periodType, page = 0) {
            // Update button styles
            dayButton.className = "daily-period-button" + (periodType === "day" ? " active" : "");
            weekButton.className = "daily-period-button" + (periodType === "week" ? " active" : "");
            monthButton.className = "daily-period-button" + (periodType === "month" ? " active" : "");

            // Aggregate data based on selected period
            let chartData;
            if (periodType === "day") {
                chartData = aggregateDataByPeriod(formatDate);
            } else if (periodType === "week") {
                chartData = aggregateDataByPeriod(formatWeek);
            } else if (periodType === "month") {
                chartData = aggregateDataByPeriod(formatMonth);
            }

            // Store all labels and datasets
            allLabels = chartData.labels;
            allDatasets = chartData.datasets;

            // Calculate total pages
            const totalPages = Math.ceil(allLabels.length / labelsPerPage);

            // Ensure current page is valid
            currentPage = Math.max(0, Math.min(page, totalPages - 1));

            // Calculate start and end indices for current page
            const startIdx = currentPage * labelsPerPage;
            const endIdx = Math.min(startIdx + labelsPerPage, allLabels.length);

            // Get labels for current page
            const pageLabels = allLabels.slice(startIdx, endIdx);

            // Get data for current page
            const pageDatasets = allDatasets.map(dataset => ({
                ...dataset,
                data: dataset.data.slice(startIdx, endIdx)
            }));

            // Update pagination buttons state
            prevButton.disabled = currentPage === 0;
            nextButton.disabled = currentPage >= totalPages - 1;

            // Update pagination info
            if (allLabels.length > 0) {
                paginationInfo.textContent = `Showing ${startIdx + 1}-${endIdx} of ${allLabels.length} dates`;
            } else {
                paginationInfo.textContent = "No data available";
            }

            // Create chart configuration
            const chartConfig = {
                type: 'line',
                data: {
                    labels: pageLabels,
                    datasets: pageDatasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    layout: {
                        padding: {
                            top: 20,
                            right: 30,
                            bottom: 20,
                            left: 20
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: periodType === "day" ? "Date" : (periodType === "week" ? "Week" : "Month"),
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                padding: {top: 10, bottom: 10}
                            },
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45,
                                font: {
                                    size: 12
                                }
                            },
                            grid: {
                                display: true,
                                color: 'rgba(200, 200, 200, 0.3)'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Hours Logged',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                padding: {top: 10, bottom: 10}
                            },
                            ticks: {
                                font: {
                                    size: 12
                                },
                                padding: 10,
                                // Calculate a reasonable step size based on the data
                                stepSize: Math.ceil(Math.max(...chartData.datasets.map(ds =>
                                    Math.max(...ds.data.filter(val => val !== null)))) / 5)
                            },
                            grid: {
                                display: true,
                                color: 'rgba(200, 200, 200, 0.3)'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: `Daily Hours Logged (${periodType === "day" ? "Daily" : (periodType === "week" ? "Weekly" : "Monthly")})`,
                            font: {
                                size: 18,
                                weight: 'bold'
                            },
                            padding: {
                                top: 10,
                                bottom: 20
                            }
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                padding: 20,
                                font: {
                                    size: 14
                                },
                                usePointStyle: true,
                                boxWidth: 10
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleFont: {
                                size: 14
                            },
                            bodyFont: {
                                size: 13
                            },
                            padding: 12,
                            cornerRadius: 6,
                            callbacks: {
                                label: function(context) {
                                    return `${context.dataset.label}: ${context.raw.toFixed(1)} hours`;
                                }
                            }
                        }
                    }
                }
            };

            // Clear previous chart
            chartContainer.innerHTML = '';

            // Render the chart
            window.renderChart(chartConfig, chartContainer);
        }

        // Track current period type
        let currentPeriodType = "day";

        // Add event listeners to period buttons
        dayButton.addEventListener("click", () => {
            currentPeriodType = "day";
            currentPage = 0; // Reset to first page when changing period type
            updateChart(currentPeriodType, currentPage);
        });

        weekButton.addEventListener("click", () => {
            currentPeriodType = "week";
            currentPage = 0; // Reset to first page when changing period type
            updateChart(currentPeriodType, currentPage);
        });

        monthButton.addEventListener("click", () => {
            currentPeriodType = "month";
            currentPage = 0; // Reset to first page when changing period type
            updateChart(currentPeriodType, currentPage);
        });

        // Add event listeners to pagination buttons
        prevButton.addEventListener("click", () => {
            if (currentPage > 0) {
                currentPage--;
                updateChart(currentPeriodType, currentPage);
            }
        });

        nextButton.addEventListener("click", () => {
            const totalPages = Math.ceil(allLabels.length / labelsPerPage);
            if (currentPage < totalPages - 1) {
                currentPage++;
                updateChart(currentPeriodType, currentPage);
            }
        });

        // Initialize with daily view
        updateChart("day", 0);

    } catch (error) {
        dailyLogDebug("ERROR:", error);
        dv.el("p", `❌ **ERROR:** ${error.message}. Check console for details.`);
    }
})();
```

