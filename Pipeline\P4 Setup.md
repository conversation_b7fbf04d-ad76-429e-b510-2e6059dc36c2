---
created: 2025-04-22T20:23:39
updated: 2025-04-22T22:02:51
---
---

## Perforce Helix Core Server (p4d) Installation on ARM64

### 1. Prerequisites

- An ARM64 Oracle Cloud instance (Ampere A1) running Linux.  
- **root** or **sudo** privileges.  
- Port **1666** will be used—ensure you’ve planned to open it in your OCI security list.

---

### 2. Download the Official ARM64 Package

From your home directory, fetch the **Helix Core Server** tarball and save it as `p4d.tgz`:

```bash
wget https://ftp.perforce.com/pub/perforce/r24.2/bin.linux26aarch64/helix-core-server.tgz \
  -O p4d.tgz
```  

---

### 3. Extract & Install Binary

1. **Extract** the archive:  
   ```bash
   tar -xvzf p4d.tgz
   ```
2. **Make it executable**:  
   ```bash
   chmod +x p4d
   chmod +x p4
   ```
3. **(Optional)** Move to a global path:  
   ```bash
   sudo mv p4d /usr/local/bin/
   sudo mv p4 /usr/local/bin/

   p4 -u super user
   p4 -u super passwd

   ```

---

### 4. Create Server Root & ServerID

```bash
mkdir -p ~/perforce/root
echo "p4d_main" > ~/perforce/root/server.id
```

---

### 5. Start p4d in Daemon Mode with Logging

```bash
p4d \
  -r ~/perforce/root \
  -p 0.0.0.0:1666 \
  -L ~/perforce/p4d.log \
  -d
```

Verify it’s listening:

```bash
sudo ss -tuln | grep 1666
# should show: LISTEN 0 5 0.0.0.0:1666 ...
```

---

### 6. Open Port 1666 in OCI Security List

1. In the OCI Console → **Networking → VCN → Subnets → Security Lists**  
2. Select the list attached to your instance’s subnet  
3. **Add Ingress Rule**:
   - **Source CIDR**: `0.0.0.0/0`  
   - **IP Protocol**: `TCP`  
   - **Destination Port**: `1666`  
4. **Reboot** your instance to ensure rules apply.

---

## 7. One‑off override on the command line

If you just want to test quickly, include the `-p` flag every time:

```bash
p4 -p localhost:1666 info
```

Or, from your Windows machine:

```cmd
p4 -p **************:1666 info
```

---

## 8. Set `P4PORT` correctly in your shell

### On Linux (your OCI box)

Edit your `~/.bashrc` (or `~/.profile`) and add:

```bash
export P4PORT=localhost:1666
```

Then reload:

```bash
source ~/.bashrc
```

Now a plain `p4 info` will connect to `localhost:1666`.

### On Windows (if you use the CLI there)

1. Open **System Properties** → **Environment Variables**.  
2. Under your **User variables**, click **New**:  
   - **Variable name:** `P4PORT`  
   - **Variable value:** `**************:1666`  
3. Click OK to save, then restart your shell or IDE.

---

## 9. Verify

Run:

```bash
p4 info
```

You should see:

```
Server address:  localhost:1666
Server version:  P4D/…
```

If it still says `perforce:1666`, double‑check you didn’t accidentally export `P4PORT=perforce:1666` somewhere (e.g. in `/etc/environment` or another profile script) and remove that.

Once `p4 info` points at the right host, you’ll be all set to sync your Unreal project!


That message means you’ve enabled `security=2` (require authentication) but haven’t yet given your Perforce user a password (or you’re not logged in). Let’s fix that:

---

## 10\. Make sure you’re using the right user  
If you created your super‑user as **super**, set:
```bash

export P4USER=super
export P4PORT=localhost:1666
source ~/.bashrc# or your public‑IP:1666
```

```shell
nano ~/perforce/root/config.txt
```

Now add this in the file

```nano ini
auth.allow.login=1
```


---
## 11\. Restart 

Check:
```bash
pkill p4d
p4d \
  -r ~/perforce/root \
  -p 0.0.0.0:1666 \
  -L ~/perforce/p4d.log \
  -d
```

```shell
echo $P4USER
echo $P4PORT
```
 

---

## 12\. Log in with that password  
```bash
p4 login 
```
Enter the password you just set. You should see:
```
User super logged in.
```

---

## 13\. Now create your depot  
```bash
p4 depot ue_depot
```
Fill in the prompts (Depot name, Owner, Type, Description).  

Once that succeeds, you’re authenticated and can create workspaces, submit files, etc.

---


