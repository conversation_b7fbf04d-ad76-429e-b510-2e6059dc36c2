---
created: 2024-07-14T16:38:02
updated: 2025-04-20T13:34:28
---

The "HOUSE" becomes a central location for <PERSON><PERSON><PERSON>'s investigation after the initial scenes in the hostel and the brief visit to the baoli.

**Instances where the player/A<PERSON><PERSON> goes to the house:**

- **Scene 4: INT. HOSTEL ROOM / EXT. HOUSE – DREAMLIKE TRANSITION**: After <PERSON><PERSON><PERSON> and his friend drink cannabis tea in the hostel room, a black screen transition leads to them suddenly **waking up again in the hostel**, followed by a cut to the location showing "HOUSE". A<PERSON><PERSON> states his intention to find clues about his brother at the house, believing his brother might have left some behind.
- **Scene 5: INT. HOUSE – NIGHT**: <PERSON><PERSON><PERSON> and his friend enter the dusty house filled with strange shadows. Their initial focus is the **brother's locked room**. The player finds the **key to the brother's room on top of the fridge in the kitchen**. Inside the brother's room, they find **old books about Morse code, encryption, and decryption**, as well as the **brother's password-locked PC**. The friend notices the **drawing-room light flickering**. A<PERSON><PERSON> observes **Morse code**, speculating it might be a message from his brother and potentially the PC's password. Upon attempting to enter a password (presumably the Morse code), the **power goes out**. Arjun decides to find a flashlight, mentioning one might be upstairs. The player also needs to use a **diya in the courtyard to illuminate the immediate surroundings**.
- **Scene 6: INT. BROTHER’S ROOM – NIGHT / HOSTEL ROOM TRANSITION**: Arjun is attacked by a ghost resembling his friend while in his brother's room at the house. This leads to a black screen and a sudden return to his hostel room.
- **Scene 7: INT. HOUSE - VARIOUS ROOMS - NIGHT**: Arjun inexplicably finds himself **back in the dark house**. The power is out, and Arjun can only turn on the light in one room at a time by manipulating the **MCB boxes**, with the other fuses blowing. He attempts to use a **"decryptor.exe" file on the brother's PC** with the alphanumeric characters from the email, but it yields gibberish. The **CCTV app on the PC starts running, and the ghost appears in the feed**. The power goes out again. The player (as Arjun) must **restore power to one room at a time by interacting with the MCB boxes while managing a "fear meter" and evading the ghost**. If the fear meter fills, the player loses inventory. Arjun has multiple encounters with the ghost during this sequence.
- **Scene 8: INT. HOUSE - DAD'S OLD ROOM - NIGHT**: Arjun finds an **old chest** in "Dad's old room" with a **three-digit number lock and a hint**: "Shift the first twice, the second once, and the third thrice". Nearby is **Arjun's old school ID card with a three-digit number**. Solving the puzzle opens the chest, revealing an **old history book**. Reading the book reveals that the **baoli was once a suicide hotspot with thousands of ghosts**. While returning to his brother's room, the ghost attacks Arjun again, causing him to faint.
- **Scene 10: INT. HOUSE**: Arjun wakes up fainting in his house again and checks about the baoli on the browser of the PC. While doing so, the ghost attacks him, and he faints again.

