---
category: Post Process
hallucination-days: 2
hallucination-status: pending
hallucination-priority: high
camera-days: 1.5
camera-status: ongoing
camera-priority: medium
attack-days: 1
attack-status: pending
attack-priority: medium
fear-days: 1
fear-status: pending
fear-priority: medium
created: 2025-06-16T00:00
updated: 2025-06-16T00:00
---

# Post Process Tasks

## Hallucination
**Days:** 
```meta-bind
INPUT[number:hallucination-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):hallucination-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):hallucination-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: hallucination-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: hallucination-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: hallucination-status
    evaluate: false
    value: "completed"
```

---

## Camera
**Days:** 
```meta-bind
INPUT[number:camera-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):camera-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):camera-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: camera-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: camera-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: camera-status
    evaluate: false
    value: "completed"
```

---

## Attack
**Days:** 
```meta-bind
INPUT[number:attack-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):attack-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):attack-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: attack-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: attack-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: attack-status
    evaluate: false
    value: "completed"
```

---

## Fear
**Days:** 
```meta-bind
INPUT[number:fear-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):fear-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):fear-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: fear-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: fear-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: fear-status
    evaluate: false
    value: "completed"
```
