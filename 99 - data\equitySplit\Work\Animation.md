---
category: Animation
cutscene-redo-days: 10
cutscene-redo-status: pending
cutscene-redo-priority: high
enemy-animation-days: 4
enemy-animation-status: pending
enemy-animation-priority: high
locomotion-half-speed-days: 1
locomotion-half-speed-status: ongoing
locomotion-half-speed-priority: medium
character-anim-days: 7
character-anim-status: pending
character-anim-priority: high
doll-days: 2
doll-status: pending
doll-priority: medium
spider-days: 4
spider-status: pending
spider-priority: medium
created: 2025-06-16T00:00
updated: 2025-06-16T00:00
---

# Animation Tasks

## Cutscene (Redo)
**Days:** 
```meta-bind
INPUT[number:cutscene-redo-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):cutscene-redo-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):cutscene-redo-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: cutscene-redo-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: cutscene-redo-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: cutscene-redo-status
    evaluate: false
    value: "completed"
```

---

## Enemy Animation (Motarica) Attack/ Jump scare
**Days:** 
```meta-bind
INPUT[number:enemy-animation-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):enemy-animation-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):enemy-animation-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: enemy-animation-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: enemy-animation-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: enemy-animation-status
    evaluate: false
    value: "completed"
```

---

## Locomotion Half Speed
**Days:** 
```meta-bind
INPUT[number:locomotion-half-speed-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):locomotion-half-speed-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):locomotion-half-speed-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: locomotion-half-speed-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: locomotion-half-speed-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: locomotion-half-speed-status
    evaluate: false
    value: "completed"
```

---

## Character Anim
**Days:** 
```meta-bind
INPUT[number:character-anim-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):character-anim-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):character-anim-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: character-anim-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: character-anim-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: character-anim-status
    evaluate: false
    value: "completed"
```

---

## Doll
**Days:** 
```meta-bind
INPUT[number:doll-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):doll-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):doll-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: doll-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: doll-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: doll-status
    evaluate: false
    value: "completed"
```

---

## Spider
**Days:** 
```meta-bind
INPUT[number:spider-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):spider-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):spider-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: spider-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: spider-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: spider-status
    evaluate: false
    value: "completed"
```
