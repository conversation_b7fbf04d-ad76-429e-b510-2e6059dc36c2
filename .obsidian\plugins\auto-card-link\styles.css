.markdown-reading-view .block-language-cardlink {
    margin: var(--p-spacing) 0;
}

.auto-card-link-container {
    container-type: inline-size;
    position: relative;
    overflow: hidden;
    user-select: none;

    --auto-card-link-button-width: calc(var(--icon-size) + var(--size-2-3));

    --auto-card-link-indent-size: 2.5em;

    &[data-auto-card-link-depth="0"] {
        margin-left: calc(var(--auto-card-link-indent-size) * 0);
    }
    &[data-auto-card-link-depth="1"] {
        margin-left: calc(var(--auto-card-link-indent-size) * 1);
    }
    &[data-auto-card-link-depth="2"] {
        margin-left: calc(var(--auto-card-link-indent-size) * 2);
    }
    &[data-auto-card-link-depth="3"] {
        margin-left: calc(var(--auto-card-link-indent-size) * 3);
    }
    &[data-auto-card-link-depth="4"] {
        margin-left: calc(var(--auto-card-link-indent-size) * 4);
    }
    &[data-auto-card-link-depth="5"] {
        margin-left: calc(var(--auto-card-link-indent-size) * 5);
    }
    &[data-auto-card-link-depth="6"] {
        margin-left: calc(var(--auto-card-link-indent-size) * 6);
    }
    &[data-auto-card-link-depth="7"] {
        margin-left: calc(var(--auto-card-link-indent-size) * 7);
    }
}

@container (max-width: 300px) {
    .auto-card-link-thumbnail {
        display: none;
    }
}

@container (max-width: 500px) {
    .auto-card-link-description {
        display: none;
    }
    .auto-card-link-thumbnail {
        max-width: 40% !important;
    }
    .auto-card-link-title {
        white-space: normal !important;
        --lh: 1.5em;
        line-height: var(--lh);
        height: calc(var(--lh) * 3);
    }
}

.auto-card-link-error-container {
    max-width: 780px;
    margin: 1em auto;
    border-radius: 8px;
    overflow: hidden;
    background-color: var(--background-modifier-error);
    padding: 10px;
    font-family: var(--font-text);

    &:hover {
        background: var(--background-modifier-error-hover);
    }
}

.auto-card-link-card {
    display: flex;
    flex-direction: row-reverse;
    height: 8em;
    transition: 20ms ease-in 0s;
    cursor: pointer;
    text-decoration: none;
    color: var(--link-external-color);
    background: var(--background-primary-alt);
    border: solid var(--border-width) var(--divider-color);
    border-radius: var(--radius-s);

    &:hover {
        background: var(--background-modifier-hover);
        border-color: var(--background-modifier-hover);
        text-decoration: none;
    }
}

.auto-card-link-main {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    justify-content: space-between;
    gap: 0.18em;
    padding: 0.5em 0.6em;
    overflow: hidden;
    text-align: left; /* necessary for ellipsis to work */
}

.auto-card-link-title {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &:hover {
        color: var(--link-external-color-hover)
    }

    /* Prevent clash with edit block button */
    .is-live-preview .auto-card-link-container:hover &,
    .is-live-preview .block-language-cardlink:has(+ .edit-block-button:hover) &  {
      margin-right: var(--auto-card-link-button-width);
    }
}

.auto-card-link-description {
    overflow: hidden;
    --lh: 1.4em;
    line-height: var(--lh);
    height: calc(var(--lh) * 3);
    color: var(--text-muted);
    font-size: var(--font-smallest);
}

.auto-card-link-host {
    font-size: var(--font-smallest);
    display: flex;
    flex-direction: row;
    align-items: center;

    & span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Prevent clash with copy url button */
    .auto-card-link-container:hover &,
    .is-mobile .auto-card-link-container & {
      margin-right: var(--auto-card-link-button-width);
    }

    &:hover {
        color: var(--link-external-color-hover)
    }
}


.auto-card-link-favicon {
    width: 16px !important;
    height: auto !important;
    margin: 0 0.5em 0 0 !important;
}

.auto-card-link-thumbnail {
    width: unset !important;
    border-radius: var(--radius-s) 0 0 var(--radius-s) !important;
    height: 100%;
    object-fit: cover;
    max-width: 50% !important;
    pointer-events: none;
}

.auto-card-link-copy-url {
    position: absolute;
    right: var(--size-2-2);
    bottom: var(--size-2-2);
    z-index: 1;

    /* Show copy button only on hover on desktop */
    .obsidian-app:not(.is-mobile) .auto-card-link-container:not(:hover) & {
      display: none;
    }
}
