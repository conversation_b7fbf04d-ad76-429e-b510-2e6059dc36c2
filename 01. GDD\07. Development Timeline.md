---
created: 2024-07-10T17:28:39
updated: 2025-04-21T21:01:24
---
# History
## Story One

### Summary
The game originally started with a different story that was much more ambitious and a lot bigger than the current one. Due to the limitations of time and investments. We were forced to change the story to fit our development timeline. The Original was about the MC who was at a verge of suicide is magically taken back in his past to change and understand all his traumas that led to his decision of committing suicide. The original was divided into 3 parts of the story, The first was about domestic violence. This introduced various different features and had the enemy as the abusive father, The father was a drunkard who the MC had to "Defeat" by watching glimpses of the abuse to end up saving his mother who died to his father's actions. The story had more depth and the current hostel level was solely made for this part of the story.

The Second part of the story included a school which showed the main character getting addicted to these substances due to his troubled upbringing and was more of a linear run with hallucinations and obstacles that were designed with the school environment in mind. This level was still in the books with more story related thinking. The Third part was totally speculation with more abstract with the theme of his failed relationships. One of the key features thought about this level was that the ghost could hum in a females voice. The environment and the characters involved in this part were not final.

### Reasoning for Abandon
After a re-evaluation of the story. The gaps in story was quite evident and were not fillable by the team. So we tried hiring a story writer while also thinking of going with the story that we currently have. The story writing part was not at par with the quality we wanted so we went forward with the new story we created.


## Story two

### Summary
The new story is about the MC ("Arjun"), who is a drug addict that is facing some mental issues that is fogging his perception of reality. He is a student that is into LSDs and spirituality. He has a friend that is with him pretty much all the time. The story starts from the hostel room where the MC gets an email from his brother that is asking arjun to "Save" him. To find the truth about this  email the friend and the arjun learns about the benefit of LSDs to enhance their thinking (Comparing to people in Silicon Valley). They go to his parental house to find more about the disappearance of his brother. This is where they face the "Enemy" while solving puzzles that were supposedly left by his brother. 

The game dives deep into the story also revealing that friend was in fact a hallucination and that Arjun is sick and can not differentiate reality anymore. The email that was reading "Save me" was no longer about saving his brother but about the diagnosis of his mental sickness. The end of the story shows the character waking up the next day and reading the same email again as "Save me".
This hints that he is stuck in a schizophrenic episode.  


### Progress of the Current Story
The new story's progress is divided in 5 categories:
1.  **3D:** 
	The 3D side of things include all levels and assets used in these levels. The progress shows a total completion of 70% with some optimization being half-way done. ==The minimum benchmark is set for a 2060, so the optimizations are set to be done majorly on the Baoli level== as it is set to be the most detailed and demanding on the machine.

2. **Programming:** 
	The Programming side of things are pretty much done with all systems working fine on its own. The end process will look to setup checkpoints and activation zones for these systems to work in conjunction with each other. ==The reason for these to be not blended from the get go is due to the story's limitation for the total gameplay shown in the demos presented at the events.== While there are still systems in production the total progress shows 80% done,
	while considering some issues that may arise with public testing.

3. **Animations:**
	The Animation sides of things are not promising with lack of proper skills into animations. ==The team has filled this role in, but the quality while learning is just not par with the quality of 3D or Programming.== With more funding, we can invest more time into better ways of animation than hand keyframing which will take a lot time to master. The face animations were done using the meta features provided by Unreal.

4. **UI:**
	==The UI is long way from being done, usually showing placeholders.== The computer UI is done and is only waiting for final touches that may be there with the change in things that may come with changes in Post Processing. A dialogue system is in place for all translations and wording matched to the audio. The menu also is half way thru and is waiting for some final touches that will be done in the end.

5. **Sound:**
	The Sound design is totally pending with only things like Voice lines for Animations and basic foley audio like footsteps & Jump/Land (on 1 surface) are implemented. The Audio system in terms of coding is pretty much there if there are no new things added to the audio list, but none of us were able to fill that role properly. ==The investment in terms development is set to be spent mostly in Sound Design.==


# Future development

The future development is set to have a majority of Sound design and Animations which currently rely on the investment. If investments are not available to us, we will ship with whatever we can develop by Q4 of 2025. ==We have multiple members investing more than 12 hours a day into this game with an estimate of 25 lakhs worth of hours spent with the industry standard per hour rate basis as of 19th April 2025.== With newer members to fill other roles, the game will be comfortably finished by the estimate timeline.



## **Timeline**

**August 2024 (Project Start and Early Development)**

- **No Logged Activity Before:** The project officially seems to have started around August 2024 as stated in the summary.
    
- **August (Likely late) - Project Setup and Basic Systems:**
    
    - Initial file structure and basic systems were likely set up, though commit logs do not show any commits in August.
        

**September 2024 (Early Progress & Basic Features)**

- **09-10-2024:** Commit for deleting a few folders (likely cleanup).
    
- **09-10-2024:** Commit for deleting unused files (cleanup).
    
- **09-10-2024:** Commit for adding security camera configurations.
    
- **09-10-2024:** Commit for adding the Rain Logic and temperature to the computer UI.
    
- **09-10-2024:** Commit for getting out of the bed v1.
    
- **09-10-2024:** Commit for adding a button to the bed.
    
- **09-10-2024:** Commit for Reset tweaked.
    
- **09-10-2024:** Commit for Out Bed.
    
- **25-09-2024:** Added some camera parameters.
    
- **25-09-2024:** Added the game's title screen.
    
- **25-09-2024:** Made the first iteration of a bed.
    
- **26-09-2024:** Added items to the UI.
    
- **26-09-2024:** Changed character's parameters to the default values.
    

**October 2024 (Early Development and Level Creation)**

- **10-10-2024:** Updated Singleplayer model and textures, indicating an early focus on the game's environment.
    
- **11-10-2024:** AI work begins with motors, indicating progress with characters.
    
- **12-10-2024:** Updates to motor physics.
    
- **13-10-2024:** Commit for AI related to motion matching.
    
- **14-10-2024:** Mics fixes.
    
- **14-10-2024:** Commit for moving Anim to the new actor.
    
- **14-10-2024:** Started working on interaction.
    
- **14-10-2024:** Some commits for AI and animation.
    
- **15-10-2024:** The AI's functionality and logic.
    
- **16-10-2024:** Started working on final AI.
    
- **16-10-2024:** Working for AI animation.
    
- **17-10-2024:** Animation fixes.
    
- **17-10-2024:** Added and fixed House Assets.
    
- **17-10-2024:** Added AI Move.
    
- **18-10-2024:** Added Windows Assets.
    
- **20-10-2024:** Added house assets.
    
- **21-10-2024:** Added UI for game interaction.
    
- **22-10-2024:** Added metahuman file for the main character.
    
- **22-10-2024:** Added remaining files.
    
- **22-10-2024:** Commit for adding the character's base.
    
- **22-10-2024:** Adding file to the basic material.
    
- **23-10-2024:** Added the house floor and temperature materials.
    
- **23-10-2024:** Added some new assets.
    
- **24-10-2024:** Added UI elements for interactions and character dialogue.
    
- **24-10-2024:** Added the character's sit-down animation.
    
- **25-10-2024:** Added the final level assets.
    
- **26-10-2024:** Added the final level assets.
    
- **27-10-2024:** Adding assets for level design.
    
- **29-10-2024:** Added base class.
    
- **29-10-2024:** Added level design for gravity puzzle.
    
- **30-10-2024:** Started working on the lights.
    
- **30-10-2024:** Added a flashlight.
    

**November 2024 (Feature Integration & Level Building)**

- **01-11-2024:** Adding lights to the level.
    
- **01-11-2024:** Added the final assets for the level.
    
- **02-11-2024:** Added the sequence for the door opening.
    
- **02-11-2024:** Updating audio.
    
- **03-11-2024:** More features related to interaction.
    
- **03-11-2024:** Fixes to the interaction for the character.
    
- **04-11-2024:** Added lights for the sequence.
    
- **04-11-2024:** Added the subsystem.
    
- **04-11-2024:** Added the latest dialogue.
    
- **05-11-2024:** The sound system for the character.
    
- **05-11-2024:** Added the environment to the room.
    
- **06-11-2024:** Added the latest sound for the level.
    
- **06-11-2024:** Added the current sound for the environment.
    
- **06-11-2024:** The basic layout for the level.
    
- **06-11-2024:** Added the dialogue system.
    
- **07-11-2024:** Added basic lighting to the environment.
    
- **07-11-2024:** Added the final assets for the environment.
    
- **08-11-2024:** Added the sound level for the first cutscene.
    
- **09-11-2024:** Adding the dialog system to the level.
    

**December 2024 - January 2025 (Level Finalization, Polish, UI)**

- **04-12-2024:** Commit of implementing the settings widget.
    
- **14-12-2024:** Implementation of the DLSS and FSR.
    
- **20-01-2025:** Added a dialog system for the character's interaction.
    
- **22-01-2025:** Fixes to the main menu.
    

**February - April 2025 (Testing, UI Polish, Level Building, Story adjustments)**

- **06-02-2025:** Adding sound to some items.
    
- **10-02-2025:** Added the door interaction and audio.
    
- **12-02-2025:** Added camera parameters for the game.
    
- **14-02-2025:** Update of the safe models.
    
- **15-02-2025:** Added environment assets.
    
- **17-02-2025:** Added collection level.
    
- **28-02-2025:** Added a UI for character interaction.
    
- **01-03-2025:** Added levels for the game.
    
- **06-03-2025:** added color adjustments and the textures.
    
- **08-03-2025:** Added animation and the assets.
    
- **14-03-2025:** Added assets for the level.
    
- **18-03-2025:** Added the UI for the inventory.
    
- **21-03-2025:** Added the HUD.
    
- **22-03-2025:** Added the text to the HUD and UI.
    
- **24-03-2025:** Added assets for the levels.
    
- **25-03-2025:** Fixed glitches.
    
- **26-03-2025:** Changed the interaction.
    
- **27-03-2025:** Adding level assets.
    
- **01-04-2025:** Added some level assets.
    
- **04-04-2025:** Changed some materials for the level.
    
- **05-04-2025:** Fixed the glitches.
    
- **06-04-2025:** Added interaction of character.
    
- **07-04-2025:** Added levels to the game.
    
- **08-04-2025:** Added character customization.
    
- **09-04-2025:** Fixed minor glitches.
    
- **10-04-2025:** Added dialogue for interaction.
    

