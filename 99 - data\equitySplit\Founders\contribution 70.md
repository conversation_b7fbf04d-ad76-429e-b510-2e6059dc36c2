---
name: <PERSON><PERSON><PERSON><PERSON>
work-description: Metahuman Workflow Plugin
count: 8
mode: ""
created: 2025-05-04T19:33:29
updated: 2025-05-04T21:05:25
---


## 1. Select Your Name 

```meta-bind 
INPUT[inlineSelect(option(Parth), option(Dhaval), option(Vedant), option(<PERSON>shu<PERSON>), option(<PERSON>hashank)):name]
```

## 2.Work Description

```meta-bind 
INPUT[textArea:work-description]
```

## 3.Number of Hours Worked

```meta-bind
INPUT[number:count]
```
------------------
```meta-bind-button
label: "+0.5 Hours"
id: "count-increment"
style: default
actions:
  - type: updateMetadata
    bindTarget: count
    evaluate: true
    value: x + 0.5
```

```meta-bind-button
label: "-0.5 Hours"
id: "count-decrement"
style: default
actions:
  - type: updateMetadata
    bindTarget: count
    evaluate: true
    # Prevent negative hours
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```

```meta-bind-button
label: "Reset Hours"
id: "count-reset"
style: default
actions:
  - type: updateMetadata
    bindTarget: count
    evaluate: false
    value: 0
```
---------------------
```meta-bind-button
label: ⚠️ Reset All Fields
icon: "reset"
tooltip: Clear all input fields above without submitting
id: "reset"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: name
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: work-description
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: mode # Resetting mode as well, remove if not needed
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: count
    evaluate: false
    value: "0"
```
---------------------------------
```meta-bind-button
label: ✅ Submit Contribution
icon: "checkmark"
tooltip: Save this contribution and reset the form
id: "submit"
style: primary
actions:
  # Step 1: Create the new contribution note using Templater
  - type: templaterCreateNote
    # --- CRITICAL: Ensure this template file exists and is EMPTY ---
    templateFile: '00. Work/Submit Work.md' # <== CHECK THIS PATH
    # --- Folder where contribution notes are saved ---
    folderPath: '99 - data/equitySplit/Founders' # <== CHECK THIS PATH
    # --- CORRECTED FILENAME GENERATION (Using YYYYMMDD_HHmmssSSS format) ---
    fileName: "contribution"
    openNote: false # Do not open the new note after creation
  # Step 2: Reset the input fields in THIS note after submission
  - type: updateMetadata
    bindTarget: name
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: work-description
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: mode # Resetting mode, remove if not needed
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: count
    evaluate: false
    value: "0"
```

