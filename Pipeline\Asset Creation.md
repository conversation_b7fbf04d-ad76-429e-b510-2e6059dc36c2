---
created: 2024-09-12T15:19:14
updated: 2025-04-20T13:04:59
---

### Crafting the Digital Asset

**In your preferred 3D modeling environment:**

1. **Sculpting the Finer Details (High Poly Model):** You begin by creating a highly detailed version of your asset. This digital sculpture captures all the intricate surfaces and forms that will define the object's visual complexity. Think of it as the ultimate blueprint for surface detail.

2. **Constructing the Primary Shape (LOD0):** From your high-poly masterpiece, you then create the base level of detail (LOD0). This is the version of the model that will be seen when the object is closest to the camera in Unreal Engine, balancing visual fidelity with performance efficiency.

3. **Unwrapping for Painting (UV Mapping LOD0):** Next, you carefully unwrap the surface of your LOD0 model, laying it out flat like pieces of a puzzle. This process creates a 2D space (the UV map) that allows you to precisely paint textures onto the 3D surface without distortion.

4. **Optimizing for Performance (Creating Low Poly LODs):** To ensure smooth performance in Unreal Engine, especially when the asset is further away, you generate lower-detail versions of the model (LODs). These simplified geometries reduce the number of polygons the engine needs to render, boosting efficiency without a noticeable visual impact at a distance.


### Painting the Surface (Texturing)

**Within your chosen texturing software:**

- **Applying Surface Properties:** Here, you bring your model to life by painting various material properties onto its UV map. This includes color information (Base Color), roughness (how matte or shiny the surface is), metallic properties (whether it behaves like a metal), and any other relevant material characteristics.
- **Generating Detail Maps:** You also bake high-frequency detail from your high-poly model into normal maps. These maps store information about surface orientation, allowing you to simulate intricate details on the lower-poly LODs without the computational cost of the high-poly geometry.
- **Preparing for Unreal (Export Settings - Refer to [[Texture Format]]):** Finally, you export your texture maps according to the specific format requirements for Unreal Engine, ensuring compatibility and optimal performance.

### Bringing it to Life in Unreal Engine

1. **Importing the Visual Data:** You seamlessly bring your "rbgr" (likely containing packed color, roughness, and potentially other material properties) and "nom" (normal map) texture files into your Unreal Engine project.

2. **Configuring the Surface Detail (Normal Map Settings):** To ensure Unreal Engine interprets your normal map correctly and renders the baked details accurately:

	- **Optimizing Storage (BC7 Compression):** You switch the compression setting for the "nom" texture to BC7. This format provides excellent quality with efficient memory usage, crucial for performance.
	- **Ensuring Accurate Detail Interpretation (Disabling sRGB):** You uncheck the "sRGB" box under the texture settings. Normal maps contain directional data, not color information, so applying sRGB color correction would distort the surface detail.

### Constructing the Visual Appearance (Unreal Material)

1. **Leveraging a Foundation (Creating Material Instance):** You create a Material Instance from your "MM_TwoTextures" master material. This creates a variation of the master material with editable parameters, allowing you to customize the look of individual assets without modifying the core logic.

2. **Connecting the Visual Information:** Within the Material Instance, you assign your imported "rgbr" and "nom" textures to their corresponding texture parameters. This links the painted surface properties and detailed surface information to the material, defining how your model will appear in the Unreal Engine environment.

