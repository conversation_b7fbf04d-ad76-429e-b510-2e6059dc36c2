---
category: Mechanics
tea-making-days: 4
tea-making-status: pending
tea-making-priority: high
lockpick-days: 4
lockpick-status: pending
lockpick-priority: high
ai-mechanics-days: 14
ai-mechanics-status: pending
ai-mechanics-priority: high
friend-ai-days: 5
friend-ai-status: pending
friend-ai-priority: high
fuse-light-days: 3
fuse-light-status: pending
fuse-light-priority: medium
morse-code-days: 1
morse-code-status: pending
morse-code-priority: low
computer-javascript-days: 2
computer-javascript-status: ongoing
computer-javascript-priority: medium
cabinet-days: 1
cabinet-status: pending
cabinet-priority: low
created: 2025-06-16T00:00
updated: 2025-06-16T00:00
---

# Mechanics Tasks

## Tea Making
**Days:** 
```meta-bind
INPUT[number:tea-making-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):tea-making-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):tea-making-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: tea-making-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: tea-making-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: tea-making-status
    evaluate: false
    value: "completed"
```

---

## Lockpick
**Days:** 
```meta-bind
INPUT[number:lockpick-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):lockpick-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):lockpick-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: lockpick-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: lockpick-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: lockpick-status
    evaluate: false
    value: "completed"
```

---

## AI Mechanics
**Days:** 
```meta-bind
INPUT[number:ai-mechanics-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):ai-mechanics-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):ai-mechanics-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: ai-mechanics-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: ai-mechanics-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: ai-mechanics-status
    evaluate: false
    value: "completed"
```

---

## Friend AI
**Days:** 
```meta-bind
INPUT[number:friend-ai-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):friend-ai-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):friend-ai-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: friend-ai-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: friend-ai-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: friend-ai-status
    evaluate: false
    value: "completed"
```

---

## Fuse Light
**Days:** 
```meta-bind
INPUT[number:fuse-light-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):fuse-light-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):fuse-light-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: fuse-light-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: fuse-light-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: fuse-light-status
    evaluate: false
    value: "completed"
```

---

## Morse Code
**Days:** 
```meta-bind
INPUT[number:morse-code-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):morse-code-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):morse-code-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: morse-code-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: morse-code-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: morse-code-status
    evaluate: false
    value: "completed"
```

---

## Computer JavaScript
**Days:** 
```meta-bind
INPUT[number:computer-javascript-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):computer-javascript-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):computer-javascript-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: computer-javascript-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: computer-javascript-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: computer-javascript-status
    evaluate: false
    value: "completed"
```

---

## Cabinet
**Days:** 
```meta-bind
INPUT[number:cabinet-days]
```
**Status:** 
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):cabinet-status]
```
**Priority:** 
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):cabinet-priority]
```

```meta-bind-button
label: "+0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: cabinet-days
    evaluate: true
    value: x + 0.5
```
```meta-bind-button
label: "-0.5"
style: default
actions:
  - type: updateMetadata
    bindTarget: cabinet-days
    evaluate: true
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```
```meta-bind-button
label: "✅ Complete"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: cabinet-status
    evaluate: false
    value: "completed"
```
