---
taskName: ""
category: ""
days: 1
status: pending
priority: medium
created: 2025-06-16T00:00
updated: 2025-06-16T00:00
---

# Add New Task

## Task Details

**Task Name:**
```meta-bind
INPUT[text:taskName]
```

**Category:**
```meta-bind
INPUT[inlineSelect(option(Animation), option(Mechanics), option(Niagara), option(Post Process), option(3D), option(UI), option(Lighting, Look and Feel), option(Optimization and Testing), option(Sound), option(Buffer)):category]
```

**Estimated Days:**
```meta-bind
INPUT[number:days]
```

**Status:**
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):status]
```

**Priority:**
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):priority]
```

## Actions

```meta-bind-button
label: "✅ Create Task"
icon: "plus"
tooltip: "Add this task to the selected category"
id: "create-task"
style: primary
actions:
  - type: updateMetadata
    bindTarget: updated
    evaluate: false
    value: "2025-06-16T12:00"
```

```meta-bind-button
label: "🔄 Reset Form"
icon: "reset"
tooltip: "Clear all fields"
id: "reset-form"
style: default
actions:
  - type: updateMetadata
    bindTarget: taskName
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: category
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: days
    evaluate: false
    value: 1
  - type: updateMetadata
    bindTarget: status
    evaluate: false
    value: "pending"
  - type: updateMetadata
    bindTarget: priority
    evaluate: false
    value: "medium"
```

## Instructions

1. **Fill in the task details** using the form fields above
2. **Click "✅ Create Task"** to save the task data to this file
3. **Manually add the task** to the appropriate category file in `99 - data/equitySplit/Work/`
4. **Use "🔄 Reset Form"** to clear all fields and add another task

### How to Add Task to Category File:

1. Open the appropriate category file (e.g., `Animation.md`, `Mechanics.md`, etc.)
2. Add the task properties to the YAML frontmatter:
   ```yaml
   task-name-days: [days]
   task-name-status: [status]
   task-name-priority: [priority]
   ```
3. Add the task section to the file body following the existing pattern
4. The task will automatically appear in the main project dashboard

**Example:** If you create a task called "New Feature" in Animation category:
- Add `new-feature-days: 3`, `new-feature-status: pending`, `new-feature-priority: high` to Animation.md frontmatter
- Add the task section with meta-bind controls to the file body
