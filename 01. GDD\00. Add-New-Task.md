---
taskName: "34"
category: Mechanics
days: 1
status: pending
priority: medium
created: 2025-06-16T00:00
updated: 2025-06-16T12:50
pendingTasks: []
---

# Add New Task

## Task Details

**Task Name:**
```meta-bind
INPUT[text:taskName]
```

**Category:**
```meta-bind
INPUT[inlineSelect(option(Animation), option(Mechanics), option(Niagara), option(PostProcess), option(3D), option(UI), option(Lighting), option(Optimization), option(Sound), option(Buffer)):category]
```

**Estimated Days:**
```meta-bind
INPUT[number:days]
```

**Status:**
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):status]
```

**Priority:**
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):priority]
```

## Actions

```meta-bind-button
label: "✅ Add to Pending List"
icon: "plus"
tooltip: "Add this task to the pending tasks list"
id: "create-task"
style: primary
actions:
  - type: updateMetadata
    bindTarget: updated
    evaluate: false
    value: "2025-06-16T12:00"
  - type: updateMetadata
    bindTarget: pendingTasks
    evaluate: true
    value: '[...x, {name: taskName, category: category, days: days, status: status, priority: priority, timestamp: Date.now()}]'
```

```meta-bind-button
label: "🔄 Reset Form"
icon: "reset"
tooltip: "Clear all fields"
id: "reset-form"
style: default
actions:
  - type: updateMetadata
    bindTarget: taskName
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: category
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: days
    evaluate: false
    value: 1
  - type: updateMetadata
    bindTarget: status
    evaluate: false
    value: "pending"
  - type: updateMetadata
    bindTarget: priority
    evaluate: false
    value: "medium"
```

## Pending Tasks Queue

Tasks added here will be queued for integration into category files:

```dataviewjs
// Get the current page data
const currentPage = dv.current();
const pendingTasks = currentPage.pendingTasks || [];

// Create main container
const container = dv.el("div", "", {cls: "pending-tasks-container"});
container.style.backgroundColor = "#f8f9fa";
container.style.border = "2px solid #0d47a1";
container.style.borderRadius = "8px";
container.style.padding = "20px";
container.style.marginTop = "20px";

if (pendingTasks.length === 0) {
    const emptyState = document.createElement("div");
    emptyState.textContent = "No pending tasks. Add a task using the form above.";
    emptyState.style.textAlign = "center";
    emptyState.style.padding = "20px";
    emptyState.style.color = "#666666";
    emptyState.style.fontStyle = "italic";
    container.appendChild(emptyState);
} else {
    // Title
    const title = document.createElement("h3");
    title.textContent = `${pendingTasks.length} Pending Task${pendingTasks.length > 1 ? 's' : ''}`;
    title.style.color = "#1a1a1a";
    title.style.marginBottom = "20px";
    container.appendChild(title);

    // Instructions
    const instructions = document.createElement("div");
    instructions.innerHTML = `<strong>Instructions:</strong> Copy the YAML for each task and add it to the appropriate category file's frontmatter.`;
    instructions.style.marginBottom = "20px";
    instructions.style.color = "#333333";
    instructions.style.padding = "10px";
    instructions.style.backgroundColor = "#e3f2fd";
    instructions.style.borderRadius = "4px";
    instructions.style.border = "1px solid #bbdefb";
    container.appendChild(instructions);

    // Process each pending task
    pendingTasks.forEach((task, index) => {
        const taskContainer = document.createElement("div");
        taskContainer.style.marginBottom = "20px";
        taskContainer.style.padding = "15px";
        taskContainer.style.backgroundColor = "#ffffff";
        taskContainer.style.border = "1px solid #e0e0e0";
        taskContainer.style.borderRadius = "6px";

        // Task header
        const taskHeader = document.createElement("div");
        taskHeader.style.display = "flex";
        taskHeader.style.justifyContent = "space-between";
        taskHeader.style.alignItems = "center";
        taskHeader.style.marginBottom = "10px";

        const taskTitle = document.createElement("h4");
        taskTitle.textContent = `${task.name} (${task.category})`;
        taskTitle.style.color = "#1a1a1a";
        taskTitle.style.margin = "0";

        const taskMeta = document.createElement("div");
        taskMeta.textContent = `${task.days} days • ${task.priority} priority`;
        taskMeta.style.fontSize = "12px";
        taskMeta.style.color = "#666666";

        taskHeader.appendChild(taskTitle);
        taskHeader.appendChild(taskMeta);

        // Convert task name to property key format
        const taskKey = task.name.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single
            .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens

        // Generate YAML
        const yamlProperties = `${taskKey}-days: ${task.days}
${taskKey}-status: ${task.status}
${taskKey}-priority: ${task.priority}`;

        // YAML display
        const yamlCode = document.createElement("pre");
        yamlCode.textContent = yamlProperties;
        yamlCode.style.backgroundColor = "#f5f5f5";
        yamlCode.style.color = "#1a1a1a";
        yamlCode.style.padding = "10px";
        yamlCode.style.borderRadius = "4px";
        yamlCode.style.border = "1px solid #d0d0d0";
        yamlCode.style.fontSize = "12px";
        yamlCode.style.margin = "10px 0";
        yamlCode.style.overflow = "auto";

        // Action buttons
        const buttonContainer = document.createElement("div");
        buttonContainer.style.display = "flex";
        buttonContainer.style.gap = "10px";
        buttonContainer.style.marginTop = "10px";

        // Copy button
        const copyBtn = document.createElement("button");
        copyBtn.textContent = "📋 Copy YAML";
        copyBtn.style.backgroundColor = "#1976D2";
        copyBtn.style.color = "white";
        copyBtn.style.border = "none";
        copyBtn.style.padding = "6px 12px";
        copyBtn.style.borderRadius = "4px";
        copyBtn.style.cursor = "pointer";
        copyBtn.style.fontSize = "12px";
        copyBtn.addEventListener("click", () => {
            navigator.clipboard.writeText(yamlProperties);
            copyBtn.textContent = "✅ Copied!";
            setTimeout(() => copyBtn.textContent = "📋 Copy YAML", 2000);
        });

        // Open category file button
        const openBtn = document.createElement("button");
        openBtn.textContent = `🔗 Open ${task.category}.md`;
        openBtn.style.backgroundColor = "#ff9800";
        openBtn.style.color = "white";
        openBtn.style.border = "none";
        openBtn.style.padding = "6px 12px";
        openBtn.style.borderRadius = "4px";
        openBtn.style.cursor = "pointer";
        openBtn.style.fontSize = "12px";
        openBtn.addEventListener("click", () => {
            const categoryFileName = task.category.replace(/\s+/g, '%20');
            window.open(`obsidian://open?vault=ProjectBaoli&file=99%20-%20data%2FequitySplit%2FWork%2F${categoryFileName}.md`);
        });

        buttonContainer.appendChild(copyBtn);
        buttonContainer.appendChild(openBtn);

        taskContainer.appendChild(taskHeader);
        taskContainer.appendChild(yamlCode);
        taskContainer.appendChild(buttonContainer);
        container.appendChild(taskContainer);
    });

    // Clear all button
    const clearAllBtn = document.createElement("button");
    clearAllBtn.textContent = "🗑️ Clear All Pending Tasks";
    clearAllBtn.style.backgroundColor = "#d32f2f";
    clearAllBtn.style.color = "white";
    clearAllBtn.style.border = "none";
    clearAllBtn.style.padding = "10px 20px";
    clearAllBtn.style.borderRadius = "6px";
    clearAllBtn.style.cursor = "pointer";
    clearAllBtn.style.marginTop = "20px";
    clearAllBtn.style.width = "100%";
    clearAllBtn.addEventListener("click", () => {
        if (confirm("Are you sure you want to clear all pending tasks?")) {
            // This would need to be implemented with meta-bind to clear the pendingTasks array
            alert("To clear pending tasks, manually edit the YAML frontmatter and set 'pendingTasks: []'");
        }
    });
    container.appendChild(clearAllBtn);
}
```
