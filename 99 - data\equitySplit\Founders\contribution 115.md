---
name: <PERSON><PERSON><PERSON>
work-description: Created a optimized browser plugin for unreal
count: 9
mode: ""
created: 2025-05-19T17:46
updated: 2025-05-19T18:41
---


## 1. Select Your Name 

```meta-bind 
INPUT[inlineSelect(option(Parth), option(Dhaval), option(Vedant), option(Anshu<PERSON>), option(<PERSON><PERSON>hank)):name]
```

## 2.Work Description

```meta-bind 
INPUT[textArea:work-description]
```

## 3.Number of Hours Worked

```meta-bind
INPUT[number:count]
```
------------------
```meta-bind-button
label: "+0.5 Hours"
id: "count-increment"
style: default
actions:
  - type: updateMetadata
    bindTarget: count
    evaluate: true
    value: x + 0.5
```

```meta-bind-button
label: "-0.5 Hours"
id: "count-decrement"
style: default
actions:
  - type: updateMetadata
    bindTarget: count
    evaluate: true
    # Prevent negative hours
    value: '{{#if (gt x 0)}} {{subtract x 0.5}} {{else}} 0 {{/if}}'
```

```meta-bind-button
label: "Reset Hours"
id: "count-reset"
style: default
actions:
  - type: updateMetadata
    bindTarget: count
    evaluate: false
    value: 0
```
---------------------
```meta-bind-button
label: ⚠️ Reset All Fields
icon: "reset"
tooltip: Clear all input fields above without submitting
id: "reset"
style: destructive
actions:
  - type: updateMetadata
    bindTarget: name
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: work-description
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: mode # Resetting mode as well, remove if not needed
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: count
    evaluate: false
    value: "0"
```
---------------------------------
```meta-bind-button
label: ✅ Submit Contribution
icon: "checkmark"
tooltip: Save this contribution and reset the form
id: "submit"
style: primary
actions:
  # Step 1: Create the new contribution note using Templater
  - type: templaterCreateNote
    # --- CRITICAL: Ensure this template file exists and is EMPTY ---
    templateFile: '00. Work/Submit Work.md' # <== CHECK THIS PATH
    # --- Folder where contribution notes are saved ---
    folderPath: '99 - data/equitySplit/Founders' # <== CHECK THIS PATH
    # --- CORRECTED FILENAME GENERATION (Using YYYYMMDD_HHmmssSSS format) ---
    fileName: "contribution"
    openNote: false # Do not open the new note after creation
  # Step 2: Reset the input fields in THIS note after submission
  - type: updateMetadata
    bindTarget: name
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: work-description
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: mode # Resetting mode, remove if not needed
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: count
    evaluate: false
    value: "0"
```

