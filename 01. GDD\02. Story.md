---
created: 2024-07-14T16:58:07
updated: 2025-04-20T13:32:48
---

The story of **<PERSON><PERSON>** centers around **<PERSON><PERSON><PERSON>**, a young man in a messy hostel room who is grappling with the mysterious disappearance of his brother and his own drug use. The narrative begins when <PERSON><PERSON><PERSON> receives a cryptic email with the subject line “SAVE ME” and a gibberish message, which he fears might be from his missing brother. Driven by worry, <PERSON><PERSON><PERSON> embarks on a nightmarish journey to uncover the truth.

His investigation leads him and his **hallucinated friend** to his dusty family home. There, they search for clues in his brother's locked room, finding books on Morse code and encryption, as well as his brother's locked PC. The player, as <PERSON><PERSON><PERSON>, must solve puzzles involving a date found behind a photograph, Morse code, and a number lock to progress.

A significant location is the **baoli (stepwell)**, a place associated with a "great day" A<PERSON><PERSON> remembers with his brother and later connected to a potential traumatic event. The game utilizes **psychological horror elements** through <PERSON><PERSON><PERSON>'s drug-induced hallucinations, particularly the presence of his friend who is later revealed to be a figment of his imagination. A **scary ghost** resembling his friend also appears, blurring the lines between reality and hallucination and triggering a "fear meter".

The mysterious email continues to change, providing cryptic warnings like "Do not expose yourself to drugs" and revealing details about a therapist appointment, suggesting <PERSON><PERSON><PERSON>'s mental state is central to the mystery. Flashbacks hint at a tragic incident involving Arjun's brother at the baoli. Arjun repeatedly transitions between his hostel room and his house, questioning what is real.

Ultimately, Arjun confronts the reality of his hallucinations and the likely tragic fate of his brother at the baoli. He resolves to stop using drugs, recognizing their role in his distorted perception. However, the reappearance of his hallucinated friend at the end suggests his struggle with reality is ongoing. The story explores themes of loss, guilt, addiction, and the fragile nature of perception within a suspenseful and unsettling atmosphere.
## **The game unfolds primarily across two key locations:**

- **Arjun's Hostel Room:** The story begins in Arjun's messy hostel room, where he first encounters the unsettling email and begins his investigation. This location serves as a recurring point, especially as Arjun experiences dreamlike transitions and shifts in reality.
- **Arjun's House:** As Arjun delves deeper into the mystery, he and his (possibly hallucinated) friend visit his family home, searching for clues left behind by his brother. The house is depicted as dusty and filled with strange shadows, contributing to the eerie atmosphere. Within the house, key areas of interest include his brother's locked room, which contains books on Morse code and encryption, and his brother's locked PC.
- **Baoli (Stepwell):** The baoli, an ancient stepwell, emerges as a significant location connected to a "great day" Arjun remembers with his brother and later as a place where the hallucinated friend suggests they go. Flashbacks and Arjun's internal thoughts suggest a traumatic event involving his brother might have occurred at the baoli, hinting at a deeper, possibly supernatural, connection.

## **Goal:**

The primary goal of Baoli is to immerse the player in a psychological thriller where the protagonist's fractured state of mind and potential supernatural occurrences create a constant sense of unease and dread. The player must navigate Arjun's unreliable reality, solve mysteries, and confront the potential horrors surrounding his brother's disappearance, all while questioning what is real and what is a figment of Arjun's troubled psyche.






