---
taskName: ""
category: ""
days: 1
status: pending
priority: medium
created: 2025-06-16T00:00
updated: 2025-06-16T00:00
---

# Add New Task

## Task Details

**Task Name:**
```meta-bind
INPUT[text:taskName]
```

**Category:**
```meta-bind
INPUT[inlineSelect(option(Animation), option(Mechanics), option(Niagara), option(Post Process), option(3D), option(UI), option(Lighting, Look and Feel), option(Optimization and Testing), option(Sound), option(Buffer)):category]
```

**Estimated Days:**
```meta-bind
INPUT[number:days]
```

**Status:**
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):status]
```

**Priority:**
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):priority]
```

## Actions

```meta-bind-button
label: "✅ Create Task"
icon: "plus"
tooltip: "Add this task to the selected category"
id: "create-task"
style: primary
actions:
  - type: updateMetadata
    bindTarget: created
    evaluate: false
    value: "{{date:YYYY-MM-DDTHH:mm}}"
  - type: updateMetadata
    bindTarget: updated
    evaluate: false
    value: "{{date:YYYY-MM-DDTHH:mm}}"
```

```meta-bind-button
label: "🔄 Reset Form"
icon: "reset"
tooltip: "Clear all fields"
id: "reset-form"
style: default
actions:
  - type: updateMetadata
    bindTarget: taskName
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: category
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: days
    evaluate: false
    value: 1
  - type: updateMetadata
    bindTarget: status
    evaluate: false
    value: "pending"
  - type: updateMetadata
    bindTarget: priority
    evaluate: false
    value: "medium"
```

## Instructions

1. Fill in the task details above
2. Click "Create Task" to add it to the project
3. The task will be added to the appropriate category file
4. Use "Reset Form" to clear all fields and add another task

**Note:** After creating a task, you'll need to manually add it to the appropriate category file in the `99 - data/equitySplit/Work/` folder following the same pattern as existing tasks.
