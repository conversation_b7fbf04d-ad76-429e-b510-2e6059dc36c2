{"main": {"id": "068c9a57d79c0dc5", "type": "split", "children": [{"id": "ee836c37d6e82b07", "type": "tabs", "children": [{"id": "bf33182543f6e923", "type": "leaf", "state": {"type": "markdown", "state": {"file": "01. GDD/00.Baoli.md", "mode": "preview", "source": false}, "icon": "lucide-file", "title": "00.<PERSON><PERSON>"}}]}], "direction": "vertical"}, "left": {"id": "12e40291654f2e34", "type": "split", "children": [{"id": "919a6e82aa9de3b4", "type": "tabs", "children": [{"id": "92b0a03073cd3d51", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "afcc5b318999abca", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "5f0469faaa33ab25", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 253.5}, "right": {"id": "0aedcfa8f24d77ca", "type": "split", "children": [{"id": "82c189b35e2687a6", "type": "tabs", "children": [{"id": "b9d369db59c2529f", "type": "leaf", "state": {"type": "backlink", "state": {"file": "00. Work/Equity.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for Equity"}}, {"id": "8e67a6a4d0bd9675", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "00. Work/Equity.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from Equity"}}, {"id": "3f9fb7b082f306b7", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "934be630ceac7723", "type": "leaf", "state": {"type": "outline", "state": {"file": "00. Work/Equity.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of Equity"}}], "currentTab": 3}], "direction": "horizontal", "width": 200, "collapsed": true}, "left-ribbon": {"hiddenItems": {"templater-obsidian:Templater": false, "obsidian-kanban:Create new board": false, "switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false, "hidden-folder-obsidian:Show Folders": false}}, "active": "bf33182543f6e923", "lastOpenFiles": ["01. GDD/00.Baoli 1.md", "01. GDD/00.Baoli.md", "99 - data/equitySplit/Work/PostProcess.md", "01. GDD/00. Add-New-Task.md", "01. GDD/Add-New-Task.md", "99 - data/equitySplit/Work/3D.md", "99 - data/equitySplit/Work/Mechanics.md", "01. GDD/01. Introduction.md", "99 - data/equitySplit/Work/Animation.md", "99 - data/equitySplit/Work/Add-New-Task.md", "99 - data/equitySplit/Work/Buffer.md", "99 - data/equitySplit/Work/Sound.md", "99 - data/equitySplit/Work/Optimization and Testing.md", "99 - data/equitySplit/Work/Lighting, Look and Feel.md", "99 - data/equitySplit/Work/UI.md", "99 - data/equitySplit/Work/Post Process.md", "99 - data/equitySplit/Work/Niagara.md", "99 - data/equitySplit/Work/task-locomotion-half-speed.md", "99 - data/equitySplit/Work/task-enemy-animation.md", "99 - data/equitySplit/Work/task-cutscene-redo.md", "99 - data/equitySplit/Work/tasks-data.md", "99 - data/equitySplit/Work/project-data.md", "99 - data/equitySplit/Work", "00. Work/Equity.md", "99 - data/equitySplit/Founders/contribution 180.md", "99 - data/equitySplit/Founders/contribution 181.md", "99 - data/equitySplit/Founders/contribution 182.md", "Pasted image 20250614150439.png", "Pasted image 20250514114035.png", "Media/2025-04-29 19-56-46.mp4", "Media/Pasted image 20250423152527.png", "Media/2025-04-22 14-31-47.mp4", "Media/2025-04-22 14-26-27.mp4", "Media/Pasted image 20241225161719.png", "Media/LevelClear.png", "Media/Screenshot 2024-09-12 161721.png", "Media/Screenshot 2024-09-12 161433.png", "Media", "07. Sound", "Images/Screenshot 2024-09-12 161433.png", "Images/Pasted image 20241225161719.png", "Images/LevelClear.png", "99 - data/equitySplit/Founders", "99 - data/equitySplit", "06. 3D Hero Assets", "02. Hostel Level", "Untitled.canvas"]}