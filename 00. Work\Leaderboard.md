

```dataviewjs
// Add a spacer div to improve scrolling stability
dv.el("div", "", {attr: {style: "height: 20px;"}});

// --- Configuration ---
// vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// vvv   !!! PLEASE DOUBLE-CHECK THESE PATHS AND NAMES !!!                 vvv
const referenceFilePath = "99 - data/equitySplit/Equity Reference.md"; // <== CHECK PATH
const foundersFolderPath = '"99 - data/equitySplit/Founders"'; // <== CHECK PATH
const founderNames = ["Shashank", "Anshul", "Parth", "Dhaval", "Vedant"]; // <== CHECK NAMES
// No dates excluded from calculations
// ^^^   !!! PLEASE DOUBLE-CHECK THESE PATHS AND NAMES !!!                 ^^^
// ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

// --- Helper Function for Logging ---
function leaderboardLogDebug(message, ...args) {
    console.log(`[Leaderboard] ${message}`, args.length > 0 ? args : "");
}

// --- Main Async Function Wrapper ---
await (async () => {
    leaderboardLogDebug("Starting leaderboard script...");

    // --- Add a small delay to ensure plugins are loaded ---
    await new Promise(resolve => setTimeout(resolve, 500));

    // --- Check for Charts Plugin ---
    let chartsPluginAvailable = false;
    try {
        chartsPluginAvailable = typeof window.renderChart === 'function';
        leaderboardLogDebug(`Charts plugin check: ${chartsPluginAvailable ? 'FOUND' : 'NOT FOUND'}`);
    } catch (error) {
        leaderboardLogDebug(`Error checking for Charts plugin: ${error.message}`);
        chartsPluginAvailable = false;
    }

    if (!chartsPluginAvailable) {
        dv.el("p", "⚠️ **Warning:** The 'Charts' plugin is not detected. Please ensure it's installed and enabled for chart visualization.");
    }

    try {
        // --- Load Contribution Files ---
        leaderboardLogDebug(`Processing files from folder: ${foundersFolderPath}`);
        const files = dv.pages(foundersFolderPath);
        leaderboardLogDebug(`Found ${files.length} files.`);

        if (files.length === 0) {
            dv.el("p", `ℹ️ No contribution files found.`);
            return;
        }

        // --- Process Files and Extract Time Data ---
        // Create a data structure to hold time-series data
        const dailyData = {};

        // Initialize data structures for each founder
        founderNames.forEach(name => {
            dailyData[name] = {};
        });

        // Process each file and extract date and hours
        files.forEach(item => {
            const founderName = item.name;
            if (!founderNames.includes(founderName)) return;

            const countValue = parseFloat(item.count);
            if (isNaN(countValue)) return;

            // Get the creation date from the file (prioritizing metadata 'created' field)
            const creationDate = item.created || item.file.ctime;
            if (!creationDate) return;

            // Format date as YYYY-MM-DD
            const dateKey = formatDate(creationDate);

            // Add hours to the appropriate date
            if (!dailyData[founderName][dateKey]) {
                dailyData[founderName][dateKey] = 0;
            }
            dailyData[founderName][dateKey] += countValue;
        });

        // --- Helper Functions ---
        // Function to format date as YYYY-MM-DD
        function formatDate(date) {
            const d = new Date(date);
            return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
        }

        // Function to get week number
        function getWeekNumber(date) {
            const d = new Date(date);
            d.setHours(0, 0, 0, 0);
            d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
            const week1 = new Date(d.getFullYear(), 0, 4);
            return 1 + Math.round(((d - week1) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
        }

        // Function to format date as YYYY-WW (week)
        function formatWeek(date) {
            const d = new Date(date);
            const weekNum = getWeekNumber(d);
            return `${d.getFullYear()}-W${String(weekNum).padStart(2, '0')}`;
        }

        // Function to get the start and end dates of a week
        function getWeekRange(weekStr) {
            const [year, weekNum] = weekStr.split('-W');
            const simple = new Date(year, 0, 1 + (parseInt(weekNum) - 1) * 7);
            const dow = simple.getDay();
            const startDate = new Date(simple);
            if (dow <= 4) {
                startDate.setDate(simple.getDate() - simple.getDay() + 1);
            } else {
                startDate.setDate(simple.getDate() + 8 - simple.getDay());
            }
            const endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + 6);
            return {
                start: formatDate(startDate),
                end: formatDate(endDate)
            };
        }

        // Function to get color for founder
        function getFounderColor(index, alpha = 1) {
            const colors = [
                `rgba(33, 150, 243, ${alpha})`,   // Bright Blue
                `rgba(233, 30, 99, ${alpha})`,    // Pink
                `rgba(76, 175, 80, ${alpha})`,    // Green
                `rgba(255, 152, 0, ${alpha})`,    // Orange
                `rgba(156, 39, 176, ${alpha})`,   // Purple
                `rgba(0, 188, 212, ${alpha})`,    // Cyan
                `rgba(255, 87, 34, ${alpha})`,    // Deep Orange
                `rgba(121, 85, 72, ${alpha})`,    // Brown
                `rgba(63, 81, 181, ${alpha})`,    // Indigo
                `rgba(205, 220, 57, ${alpha})`    // Lime
            ];
            return colors[index % colors.length];
        }

        // --- Aggregate Data by Week ---
        function aggregateDataByWeek() {
            const weeklyData = {};
            const allWeeks = new Set();

            // Initialize weekly data structure
            founderNames.forEach(name => {
                weeklyData[name] = {};
            });

            // Aggregate data points by week
            founderNames.forEach(name => {
                Object.entries(dailyData[name]).forEach(([dateStr, hours]) => {
                    const weekKey = formatWeek(new Date(dateStr));
                    allWeeks.add(weekKey);

                    if (!weeklyData[name][weekKey]) {
                        weeklyData[name][weekKey] = 0;
                    }
                    weeklyData[name][weekKey] += hours;
                });
            });

            return { weeklyData, allWeeks: Array.from(allWeeks).sort() };
        }

        // --- Get Last 7 Days Data ---
        const today = new Date();
        const sevenDaysAgo = new Date(today);
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6); // 6 days ago + today = 7 days

        // Format dates for display
        const startDateStr = formatDate(sevenDaysAgo);
        const endDateStr = formatDate(today);

        // Function to calculate hours for the last 7 days
        function calculateLast7DaysHours() {
            const last7DaysHours = {};

            // Initialize data structure
            founderNames.forEach(name => {
                last7DaysHours[name] = 0;
            });

            // Get the last 7 days as strings
            const last7Days = [];
            for (let i = 0; i <= 6; i++) { // 0 to 6 = 7 days
                const date = new Date(sevenDaysAgo);
                date.setDate(sevenDaysAgo.getDate() + i);
                last7Days.push(formatDate(date));
            }

            // Sum up hours for each founder over the last 7 days
            founderNames.forEach(name => {
                last7Days.forEach(day => {
                    last7DaysHours[name] += dailyData[name][day] || 0;
                });
            });

            return last7DaysHours;
        }

        // Calculate hours for the last 7 days
        const last7DaysHours = calculateLast7DaysHours();

        // For backward compatibility, still calculate weekly data
        const { weeklyData, allWeeks } = aggregateDataByWeek();

        // --- Create Leaderboard UI ---
        dv.el("h2", "🏆 Last 7 Days Leaderboard");

        // Create a styled container for the leaderboard
        const leaderboardContainer = dv.el("div", "", {cls: "weekly-leaderboard"});
        leaderboardContainer.style.backgroundColor = "#ffffff";
        leaderboardContainer.style.borderRadius = "10px";
        leaderboardContainer.style.padding = "25px";
        leaderboardContainer.style.marginTop = "15px";
        leaderboardContainer.style.marginBottom = "40px";
        leaderboardContainer.style.boxShadow = "0 6px 16px rgba(0,0,0,0.15)";
        leaderboardContainer.style.border = "1px solid #d0d0d0";

        // Add a title to the container
        const leaderboardTitle = document.createElement("div");

        // Format dates for better display
        const startDate = new Date(startDateStr);
        const endDate = new Date(endDateStr);
        const startOptions = { month: 'short', day: 'numeric' };
        const endOptions = { month: 'short', day: 'numeric', year: 'numeric' };
        const formattedStartDate = startDate.toLocaleDateString('en-US', startOptions);
        const formattedEndDate = endDate.toLocaleDateString('en-US', endOptions);

        leaderboardTitle.textContent = `${formattedStartDate} to ${formattedEndDate}`;
        leaderboardTitle.style.fontSize = "22px";
        leaderboardTitle.style.fontWeight = "bold";
        leaderboardTitle.style.marginBottom = "20px";
        leaderboardTitle.style.paddingBottom = "15px";
        leaderboardTitle.style.borderBottom = "2px solid #bbdefb";
        leaderboardTitle.style.color = "#0d47a1";
        leaderboardTitle.style.letterSpacing = "0.3px";
        leaderboardTitle.style.textShadow = "0 1px 1px rgba(0,0,0,0.05)";
        leaderboardContainer.appendChild(leaderboardTitle);

        // Get total hours for the last 7 days
        let totalHoursLast7Days = 0;

        founderNames.forEach(name => {
            totalHoursLast7Days += last7DaysHours[name];
        });

        // Sort founders by hours for the last 7 days
        const rankedFounders = founderNames
            .map(name => ({ name, hours: last7DaysHours[name] }))
            .sort((a, b) => b.hours - a.hours);

        // Create leaderboard table
        const leaderboardTable = document.createElement("table");
        leaderboardTable.style.width = "100%";
        leaderboardTable.style.borderCollapse = "separate";
        leaderboardTable.style.borderSpacing = "0 12px";

        // Create table header
        const tableHeader = document.createElement("thead");
        const headerRow = document.createElement("tr");

        const rankHeader = document.createElement("th");
        rankHeader.textContent = "Rank";
        rankHeader.style.padding = "12px 15px";
        rankHeader.style.textAlign = "center";
        rankHeader.style.color = "#0d47a1";
        rankHeader.style.fontWeight = "bold";
        rankHeader.style.fontSize = "16px";
        rankHeader.style.letterSpacing = "0.2px";
        rankHeader.style.borderBottom = "2px solid #e3f2fd";
        rankHeader.style.width = "15%";
        headerRow.appendChild(rankHeader);

        const founderHeader = document.createElement("th");
        founderHeader.textContent = "Founder";
        founderHeader.style.padding = "12px 15px";
        founderHeader.style.textAlign = "left";
        founderHeader.style.color = "#0d47a1";
        founderHeader.style.fontWeight = "bold";
        founderHeader.style.fontSize = "16px";
        founderHeader.style.letterSpacing = "0.2px";
        founderHeader.style.borderBottom = "2px solid #e3f2fd";
        founderHeader.style.width = "35%";
        headerRow.appendChild(founderHeader);

        const hoursHeader = document.createElement("th");
        hoursHeader.textContent = "Hours";
        hoursHeader.style.padding = "12px 15px";
        hoursHeader.style.textAlign = "right";
        hoursHeader.style.color = "#0d47a1";
        hoursHeader.style.fontWeight = "bold";
        hoursHeader.style.fontSize = "16px";
        hoursHeader.style.letterSpacing = "0.2px";
        hoursHeader.style.borderBottom = "2px solid #e3f2fd";
        hoursHeader.style.width = "20%";
        headerRow.appendChild(hoursHeader);

        const ratingHeader = document.createElement("th");
        ratingHeader.textContent = "Rating";
        ratingHeader.style.padding = "12px 15px";
        ratingHeader.style.textAlign = "center";
        ratingHeader.style.color = "#0d47a1";
        ratingHeader.style.fontWeight = "bold";
        ratingHeader.style.fontSize = "16px";
        ratingHeader.style.letterSpacing = "0.2px";
        ratingHeader.style.borderBottom = "2px solid #e3f2fd";
        ratingHeader.style.width = "30%";
        headerRow.appendChild(ratingHeader);

        tableHeader.appendChild(headerRow);
        leaderboardTable.appendChild(tableHeader);

        // Create table body
        const tableBody = document.createElement("tbody");

        // Function to get rating based on hours
        function getRating(hours) {
            if (hours >= 40) return { text: "Exceptional", color: "#2e7d32", stars: "★★★★★" }; // Dark green
            if (hours >= 30) return { text: "Outstanding", color: "#558b2f", stars: "★★★★☆" }; // Green
            if (hours >= 20) return { text: "Great", color: "#f9a825", stars: "★★★☆☆" }; // Yellow
            if (hours >= 10) return { text: "Good", color: "#ef6c00", stars: "★★☆☆☆" }; // Orange
            if (hours > 0) return { text: "Participating", color: "#d32f2f", stars: "★☆☆☆☆" }; // Red
            return { text: "No Activity", color: "#757575", stars: "☆☆☆☆☆" }; // Gray
        }

        // Add rows for each founder
        rankedFounders.forEach((founder, index) => {
            const row = document.createElement("tr");

            // Apply special styling for top 3
            if (index < 3) {
                row.style.backgroundColor = index === 0 ? "rgba(255, 215, 0, 0.1)" : // Gold
                                           index === 1 ? "rgba(192, 192, 192, 0.1)" : // Silver
                                           "rgba(205, 127, 50, 0.1)"; // Bronze
                row.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
                row.style.borderRadius = "8px";
            }

            // Rank cell with medal for top 3
            const rankCell = document.createElement("td");
            rankCell.style.padding = "15px";
            rankCell.style.textAlign = "center";
            rankCell.style.fontWeight = "bold";
            rankCell.style.fontSize = "18px";

            if (index === 0) {
                rankCell.innerHTML = "🥇";
                rankCell.style.fontSize = "24px";
            } else if (index === 1) {
                rankCell.innerHTML = "🥈";
                rankCell.style.fontSize = "24px";
            } else if (index === 2) {
                rankCell.innerHTML = "🥉";
                rankCell.style.fontSize = "24px";
            } else {
                rankCell.textContent = `${index + 1}`;
                rankCell.style.color = "#555555";
            }

            row.appendChild(rankCell);

            // Founder name cell
            const nameCell = document.createElement("td");
            nameCell.style.padding = "15px";
            nameCell.style.fontWeight = "bold";
            nameCell.style.fontSize = "16px";

            // Create colored text for the founder name
            const founderNameSpan = document.createElement("span");
            founderNameSpan.textContent = founder.name;
            founderNameSpan.style.color = "#000000"; // Black text for founder names
            founderNameSpan.style.fontSize = "18px";

            nameCell.appendChild(founderNameSpan);
            row.appendChild(nameCell);

            // Hours cell
            const hoursCell = document.createElement("td");
            hoursCell.style.padding = "15px";
            hoursCell.style.textAlign = "right";
            hoursCell.style.fontWeight = "bold";
            hoursCell.style.fontSize = "18px";
            hoursCell.style.color = "#000000"; // Black text for hours
            hoursCell.textContent = founder.hours.toFixed(1);
            row.appendChild(hoursCell);

            // Rating cell
            const ratingCell = document.createElement("td");
            ratingCell.style.padding = "15px";
            ratingCell.style.textAlign = "center";

            const rating = getRating(founder.hours);

            const ratingSpan = document.createElement("span");
            ratingSpan.textContent = rating.text;
            ratingSpan.style.color = rating.color;
            ratingSpan.style.fontWeight = "bold";
            ratingSpan.style.fontSize = "16px";
            ratingSpan.style.display = "block";

            const starsSpan = document.createElement("span");
            starsSpan.textContent = rating.stars;
            starsSpan.style.color = rating.color;
            starsSpan.style.fontSize = "20px";
            starsSpan.style.letterSpacing = "2px";

            ratingCell.appendChild(ratingSpan);
            ratingCell.appendChild(starsSpan);
            row.appendChild(ratingCell);

            tableBody.appendChild(row);
        });

        // Add total row
        const totalRow = document.createElement("tr");
        totalRow.style.borderTop = "1px solid #e0e0e0";
        totalRow.style.backgroundColor = "#000000";
        totalRow.style.color = "#ffffff";
        totalRow.style.fontWeight = "bold";

        const totalLabelCell = document.createElement("td");
        totalLabelCell.textContent = "Total";
        totalLabelCell.style.padding = "15px";
        totalLabelCell.style.textAlign = "right";
        totalLabelCell.colSpan = "2";
        totalLabelCell.style.fontSize = "16px";
        totalRow.appendChild(totalLabelCell);

        const totalHoursCell = document.createElement("td");
        totalHoursCell.textContent = totalHoursLast7Days.toFixed(1);
        totalHoursCell.style.padding = "15px";
        totalHoursCell.style.textAlign = "right";
        totalHoursCell.style.fontSize = "18px";
        totalRow.appendChild(totalHoursCell);

        const emptyCell = document.createElement("td");
        totalRow.appendChild(emptyCell);

        tableBody.appendChild(totalRow);
        leaderboardTable.appendChild(tableBody);
        leaderboardContainer.appendChild(leaderboardTable);

        // --- Create Last 7 Days Chart ---
        if (chartsPluginAvailable) {
            dv.el("h2", "📊 Last 7 Days Hours Comparison");

            // Create container for the chart
            const chartContainer = dv.el("div", "", {cls: "weekly-hours-chart"});
            chartContainer.style.height = "500px";
            chartContainer.style.marginTop = "20px";
            chartContainer.style.marginBottom = "40px";
            chartContainer.style.backgroundColor = "#ffffff";
            chartContainer.style.borderRadius = "10px";
            chartContainer.style.padding = "25px";
            chartContainer.style.boxShadow = "0 6px 16px rgba(0,0,0,0.15)";
            chartContainer.style.border = "1px solid #d0d0d0";

            // Prepare data for the chart
            const chartData = {
                labels: rankedFounders.map(f => f.name),
                datasets: [{
                    label: 'Hours Last 7 Days',
                    data: rankedFounders.map(f => f.hours),
                    backgroundColor: rankedFounders.map(f =>
                        getFounderColor(founderNames.indexOf(f.name), 0.7)
                    ),
                    borderColor: rankedFounders.map(f =>
                        getFounderColor(founderNames.indexOf(f.name), 1)
                    ),
                    borderWidth: 1
                }]
            };

            // Chart configuration
            const chartConfig = {
                type: 'bar',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    layout: {
                        padding: {
                            top: 20,
                            right: 30,
                            bottom: 20,
                            left: 20
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Founder',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                padding: {top: 10, bottom: 10}
                            },
                            ticks: {
                                font: {
                                    size: 12
                                }
                            },
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Hours',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                padding: {top: 10, bottom: 10}
                            },
                            ticks: {
                                font: {
                                    size: 12
                                },
                                padding: 10,
                                stepSize: Math.ceil(Math.max(...rankedFounders.map(f => f.hours)) / 5)
                            },
                            grid: {
                                display: true,
                                color: 'rgba(200, 200, 200, 0.3)'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: `Hours (${formattedStartDate} to ${formattedEndDate})`,
                            font: {
                                size: 18,
                                weight: 'bold'
                            },
                            padding: {
                                top: 10,
                                bottom: 20
                            }
                        },
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleFont: {
                                size: 14
                            },
                            bodyFont: {
                                size: 13
                            },
                            padding: 12,
                            cornerRadius: 6,
                            callbacks: {
                                label: function(context) {
                                    return `${context.dataset.label}: ${context.raw.toFixed(1)} hours`;
                                }
                            }
                        }
                    }
                }
            };

            // Render the chart
            window.renderChart(chartConfig, chartContainer);
        }

        // --- Create Last 7 Days Table ---
        dv.el("h2", "📅 Last 7 Days Activity");

        // Create a styled container for the daily activity
        const dailyContainer = dv.el("div", "", {cls: "daily-activity"});
        dailyContainer.style.backgroundColor = "#ffffff";
        dailyContainer.style.borderRadius = "10px";
        dailyContainer.style.padding = "25px";
        dailyContainer.style.marginTop = "15px";
        dailyContainer.style.marginBottom = "40px";
        dailyContainer.style.boxShadow = "0 6px 16px rgba(0,0,0,0.15)";
        dailyContainer.style.border = "1px solid #d0d0d0";

        // Add a title to the container
        const dailyTitle = document.createElement("div");
        dailyTitle.textContent = `Daily Hours (${formattedStartDate} to ${formattedEndDate})`;
        dailyTitle.style.fontSize = "22px";
        dailyTitle.style.fontWeight = "bold";
        dailyTitle.style.marginBottom = "20px";
        dailyTitle.style.paddingBottom = "15px";
        dailyTitle.style.borderBottom = "2px solid #bbdefb";
        dailyTitle.style.color = "#0d47a1";
        dailyTitle.style.letterSpacing = "0.3px";
        dailyTitle.style.textShadow = "0 1px 1px rgba(0,0,0,0.05)";
        dailyContainer.appendChild(dailyTitle);

        // Use the same last 7 days range we calculated earlier
        const last7Days = [];
        for (let i = 0; i <= 6; i++) {
            const date = new Date(sevenDaysAgo);
            date.setDate(sevenDaysAgo.getDate() + i);
            last7Days.push(formatDate(date));
        }

        // Create a container for the daily tables
        const dailyTablesContainer = document.createElement("div");
        dailyTablesContainer.style.display = "flex";
        dailyTablesContainer.style.flexWrap = "wrap";
        dailyTablesContainer.style.gap = "20px";
        dailyTablesContainer.style.justifyContent = "space-between";
        dailyContainer.appendChild(dailyTablesContainer);

        // Create a table for each day
        last7Days.forEach(day => {
            // Create a container for each day
            const dayContainer = document.createElement("div");
            dayContainer.style.flex = "1 1 calc(33.333% - 20px)";
            dayContainer.style.minWidth = "300px";
            dayContainer.style.backgroundColor = "#f5f9ff";
            dayContainer.style.borderRadius = "8px";
            dayContainer.style.padding = "15px";
            dayContainer.style.border = "1px solid #d4e5f9";
            dayContainer.style.boxShadow = "0 3px 6px rgba(0,0,0,0.08)";

            // Format the date for display
            const displayDate = new Date(day);
            const dateOptions = { weekday: 'long', month: 'short', day: 'numeric' };
            const formattedDate = displayDate.toLocaleDateString('en-US', dateOptions);

            // Add day title
            const dayTitle = document.createElement("div");
            dayTitle.textContent = formattedDate;
            dayTitle.style.fontSize = "16px";
            dayTitle.style.fontWeight = "bold";
            dayTitle.style.marginBottom = "10px";
            dayTitle.style.paddingBottom = "8px";
            dayTitle.style.borderBottom = "1px solid #bbdefb";
            dayTitle.style.color = "#0d47a1";
            dayContainer.appendChild(dayTitle);

            // Create table for this day
            const dayTable = document.createElement("table");
            dayTable.style.width = "100%";
            dayTable.style.borderCollapse = "collapse";

            // Get hours for each founder for this day
            const dayHours = {};
            let totalDayHours = 0;

            founderNames.forEach(name => {
                dayHours[name] = dailyData[name][day] || 0;
                totalDayHours += dayHours[name];
            });

            // Sort founders by hours for this day
            const sortedFounders = founderNames
                .map(name => ({ name, hours: dayHours[name] }))
                .sort((a, b) => b.hours - a.hours);

            // Add rows for each founder
            sortedFounders.forEach(founder => {
                if (founder.hours > 0) {
                    const row = document.createElement("tr");

                    // Founder name cell
                    const nameCell = document.createElement("td");
                    nameCell.style.padding = "8px 5px";
                    nameCell.style.fontWeight = "bold";
                    nameCell.style.color = "#000000"; // Black text for founder names
                    nameCell.textContent = founder.name;
                    row.appendChild(nameCell);

                    // Hours cell
                    const hoursCell = document.createElement("td");
                    hoursCell.style.padding = "8px 5px";
                    hoursCell.style.textAlign = "right";
                    hoursCell.style.fontWeight = "bold";
                    hoursCell.style.color = "#000000"; // Black text for hours
                    hoursCell.textContent = founder.hours.toFixed(1);
                    row.appendChild(hoursCell);

                    dayTable.appendChild(row);
                }
            });

            // If no hours logged for this day
            if (totalDayHours === 0) {
                const emptyRow = document.createElement("tr");
                const emptyCell = document.createElement("td");
                emptyCell.colSpan = "2";
                emptyCell.style.padding = "10px 5px";
                emptyCell.style.textAlign = "center";
                emptyCell.style.fontStyle = "italic";
                emptyCell.style.color = "#757575";
                emptyCell.textContent = "No activity";
                emptyRow.appendChild(emptyCell);
                dayTable.appendChild(emptyRow);
            } else {
                // Add total row
                const totalRow = document.createElement("tr");
                totalRow.style.borderTop = "1px solid #d4e5f9";

                const totalLabelCell = document.createElement("td");
                totalLabelCell.textContent = "Total";
                totalLabelCell.style.padding = "8px 5px";
                totalLabelCell.style.fontWeight = "bold";
                totalLabelCell.style.color = "#000000"; // Black text for total label
                totalRow.appendChild(totalLabelCell);

                const totalHoursCell = document.createElement("td");
                totalHoursCell.textContent = totalDayHours.toFixed(1);
                totalHoursCell.style.padding = "8px 5px";
                totalHoursCell.style.textAlign = "right";
                totalHoursCell.style.fontWeight = "bold";
                totalHoursCell.style.color = "#000000"; // Black text for total hours
                totalRow.appendChild(totalHoursCell);

                dayTable.appendChild(totalRow);
            }

            dayContainer.appendChild(dayTable);
            dailyTablesContainer.appendChild(dayContainer);
        });

    } catch (error) {
        leaderboardLogDebug("ERROR:", error);
        dv.el("p", `❌ **ERROR:** ${error.message}. Check console for details.`);
    }
})();
```
