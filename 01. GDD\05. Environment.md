---
created: 2024-07-04T17:49:36
updated: 2025-04-20T12:58:13
---

These environments serve as the primary settings for the unfolding mystery and <PERSON><PERSON><PERSON>'s increasingly disturbed mental state. The **hostel room** represents his current, chaotic reality influenced by drug use, while the **house** holds memories and secrets related to his missing brother. The **baoli** emerges as a significant location tied to a potentially traumatic past event.

- **Hostel Room**: This is the initial setting where the story begins. It is described as **old and messy**. Key items in the hostel room include a bedside table from which <PERSON><PERSON><PERSON> takes his glasses, a table in the corner with a **smiling photo of <PERSON><PERSON><PERSON> and his brother**, and a **PC** that <PERSON><PERSON><PERSON> interacts with. <PERSON><PERSON><PERSON> habitually rolls and smokes joints in this room. The door of the hostel room bursts open when his friend arrives. The narrative also transitions back to the hostel room after A<PERSON><PERSON> experiences unsettling events at the house. A<PERSON><PERSON> wakes up in his hostel room after fainting.

- **<PERSON><PERSON> (Stepwell)**: A<PERSON><PERSON> and his friend travel to a **bao<PERSON> (stepwell) later that night**. The baoli is described as being in the **dark** with an **ancient gate**. Behind the gate, a **closed-off ganja plant** is visible. Later, an old history book found in the house reveals that the **bao<PERSON> was once a suicide hotspot**, suggesting a place associated with death and potentially thousands of ghosts. Arjun also experiences a **flashback/memory sequence at the baoli** where he witnesses his brother falling and dying.

- **House**: After a dreamlike transition, Arjun and his friend find themselves at the **"HOUSE"**. The inside of the house is described as having **dust and strange shadows in every corner**. They first search the **brother's room**, which is **locked**. This room contains **old books about <PERSON> code, encryption, and decryption**, as well as the **brother's password-locked PC**. The **kitchen** of the house is another location where the **key to the brother's room is found on top of the fridge**. The **drawing-room** is mentioned as having a **flickering light**. Arjun later finds an **old chest in "Dad's old room"**. This chest has a three-digit number lock with a hint, and Arjun's old school ID card with a three-digit number is on a nearby table. The house also has **MCB boxes** that Arjun needs to manipulate to turn on the lights. The **immediate surroundings** of the house include a **courtyard with a diya**.
