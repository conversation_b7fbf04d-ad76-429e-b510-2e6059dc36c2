---
created: 2025-04-20T14:30:33
updated: 2025-04-20T14:42:34
---

### **Creating Game-Ready Assets from Scanned Models**

When working with scanned 3D models, the following pipeline should be followed to create optimized, game-ready assets:

---

#### ✅**1. Topology Optimization**

Start by optimizing the scanned mesh for use in the game engine:

- **Check the topology:** If the scanned mesh is too high-poly or has poor edge flow, try decimating or retopologizing it.

- **Manual Remodeling (if needed):** If the mesh is unusable even after optimization, you'll need to remodel the asset from scratch, maintaining visual accuracy.
---

### 🧭**2. UV Unwrapping**

Once the mesh is ready:

- Unwrap UVs based on the scanned texture layout.

- Try to match the UVs with the original scan to get the most out of the provided texture maps.

---

### 🎨 **3. Texture Setup & Cleanup**

- Bring your asset into **Marmoset Toolbag**, **Substance Painter**, or any preferred texturing software.

- Bake the high-poly scanned mesh onto your optimized or remodeled mesh.

- Use the original scan's **Albedo**, **Normal**, and **Roughness** maps as input during baking.


---

#### 🧪**4. Map Evaluation & Editing**

- **Normal & Roughness Maps:** Often, these maps from scanned assets can be noisy or unusable. Assess them carefully. If they don't hold up, you’ll need to recreate them using proper baking workflows or generate procedural maps.

- **Albedo (Base Color) Map:** You must edit or completely remake this map. Scanned products often contain real-world branding, which we **cannot use directly** due to copyright and licensing issues.


---

#### 🖌️**5. Modifying Texture Maps**

- You can edit the albedo and other maps directly within the texturing software or use an external image editing tool like **Photoshop**.

-Either:

1. Remove/Replace branding or product names** from the base color texture, or

2. Repaint the base color to create a unique look that retains the scanned detail but avoids any IP conflicts.**


---

By following this workflow, you ensure that assets derived from real-world scans are optimized for performance, cleanly textured, and legally safe for commercial use in the game.
