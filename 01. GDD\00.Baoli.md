---
created: 2025-06-13T18:05
updated: 2025-06-16T12:06
---

# Baoli Game Development - Dynamic Project Manager

This interactive project management system tracks all development tasks, allows real-time updates, and calculates project completion dates.

```dataviewjs
// Add a spacer div to improve scrolling stability
dv.el("div", "", {attr: {style: "height: 20px;"}});

// --- Configuration ---
const workFolderPath = '"99 - data/equitySplit/Work"';
const categories = ["Animation", "Mechanics", "UI", "Niagara", "Post Process", "3D", "Lighting, Look and Feel", "Optimization and Testing", "Sound", "Buffer"];

// --- Helper Function for Logging ---
function projectLogDebug(message, ...args) {
    console.log(`[ProjectManager] ${message}`, args.length > 0 ? args : "");
}

// --- Main Function ---
(function() {
    projectLogDebug("Starting project manager...");

    try {
        // --- Load Category Data Files ---
        projectLogDebug(`Processing files from folder: ${workFolderPath}`);
        const workFiles = dv.pages(workFolderPath);
        projectLogDebug(`Found ${workFiles.length} files.`);

        if (workFiles.length === 0) {
            dv.el("p", `ℹ️ No work files found in ${workFolderPath}.`);
            return;
        }

        // --- Parse Tasks Data from Category Files ---
        const tasksData = {};
        let totalRemainingDays = 0;
        let totalOngoingDays = 0;

        categories.forEach(category => {
            const categoryFile = workFiles.find(file => file.file.name === category);
            if (categoryFile) {
                projectLogDebug(`Processing category: ${category}`);
                tasksData[category] = [];

                // Parse all properties that end with -days, -status, -priority
                const properties = Object.keys(categoryFile);
                const taskNames = new Set();

                // Extract unique task names
                properties.forEach(prop => {
                    if (prop.endsWith('-days') || prop.endsWith('-status') || prop.endsWith('-priority')) {
                        const taskKey = prop.replace(/-days$|-status$|-priority$/, '');
                        taskNames.add(taskKey);
                    }
                });

                // Build task objects
                taskNames.forEach(taskKey => {
                    const days = categoryFile[`${taskKey}-days`] || 0;
                    const status = categoryFile[`${taskKey}-status`] || 'pending';
                    const priority = categoryFile[`${taskKey}-priority`] || 'medium';

                    // Only include tasks that are not completed
                    if (status !== 'completed') {
                        const taskName = taskKey.split('-').map(word =>
                            word.charAt(0).toUpperCase() + word.slice(1)
                        ).join(' ');

                        tasksData[category].push({
                            name: taskName,
                            key: taskKey,
                            days: parseFloat(days) || 0,
                            status: status,
                            priority: priority
                        });

                        // Add to totals
                        if (status === 'ongoing') {
                            totalOngoingDays += parseFloat(days) || 0;
                        } else {
                            totalRemainingDays += parseFloat(days) || 0;
                        }
                    }
                });

                projectLogDebug(`Category ${category} has ${tasksData[category].length} active tasks`);
            }
        });

        // --- Calculate Project Statistics ---
        const workingDaysRemaining = totalRemainingDays + totalOngoingDays;

        // Calculate project completion dates for different work rates
        const startDate = new Date("2025-06-16");

        // Normal pace: 1 day of work per day (5 working days per week)
        const normalCalendarDays = Math.ceil(workingDaysRemaining * 7 / 5);
        const normalCompletionDate = new Date(startDate);
        normalCompletionDate.setDate(normalCompletionDate.getDate() + normalCalendarDays);

        // Fast pace: 2 days of work per day (10 working days per week)
        const fastCalendarDays = Math.ceil((workingDaysRemaining / 2) * 7 / 5);
        const fastCompletionDate = new Date(startDate);
        fastCompletionDate.setDate(fastCompletionDate.getDate() + fastCalendarDays);

        // Very fast pace: 3 days of work per day (15 working days per week)
        const veryFastCalendarDays = Math.ceil((workingDaysRemaining / 3) * 7 / 5);
        const veryFastCompletionDate = new Date(startDate);
        veryFastCompletionDate.setDate(veryFastCompletionDate.getDate() + veryFastCalendarDays);

        // --- Create Main Container ---
        const mainContainer = dv.el("div", "", {cls: "project-manager-container"});
        mainContainer.style.backgroundColor = "#ffffff";
        mainContainer.style.borderRadius = "10px";
        mainContainer.style.padding = "25px";
        mainContainer.style.boxShadow = "0 6px 16px rgba(0,0,0,0.15)";
        mainContainer.style.border = "1px solid #d0d0d0";
        mainContainer.style.marginTop = "15px";
        mainContainer.style.marginBottom = "40px";

        // --- Create Title ---
        const title = document.createElement("div");
        title.textContent = "Baoli Game Development - Project Manager";
        title.style.fontSize = "24px";
        title.style.fontWeight = "bold";
        title.style.marginBottom = "25px";
        title.style.paddingBottom = "15px";
        title.style.borderBottom = "2px solid #bbdefb";
        title.style.color = "#0d47a1";
        title.style.letterSpacing = "0.3px";
        title.style.textShadow = "0 1px 1px rgba(0,0,0,0.05)";
        mainContainer.appendChild(title);

        // --- Create Project Overview ---
        const overviewContainer = document.createElement("div");
        overviewContainer.style.display = "grid";
        overviewContainer.style.gridTemplateColumns = "1fr 1fr";
        overviewContainer.style.gridTemplateRows = "1fr 1fr";
        overviewContainer.style.gap = "20px";
        overviewContainer.style.marginBottom = "30px";
        overviewContainer.style.maxWidth = "600px";
        overviewContainer.style.margin = "0 auto 30px auto";

        // Function to create overview card
        function createOverviewCard(title, value, color, subtitle = "") {
            const card = document.createElement("div");
            card.style.backgroundColor = color;
            card.style.borderRadius = "8px";
            card.style.padding = "20px";
            card.style.textAlign = "center";
            card.style.color = "#ffffff";
            card.style.boxShadow = "0 3px 6px rgba(0,0,0,0.1)";

            const cardTitle = document.createElement("div");
            cardTitle.textContent = title;
            cardTitle.style.fontSize = "14px";
            cardTitle.style.fontWeight = "bold";
            cardTitle.style.marginBottom = "10px";
            cardTitle.style.opacity = "0.9";

            const cardValue = document.createElement("div");
            cardValue.textContent = value;
            cardValue.style.fontSize = "28px";
            cardValue.style.fontWeight = "bold";
            cardValue.style.marginBottom = subtitle ? "5px" : "0";

            card.appendChild(cardTitle);
            card.appendChild(cardValue);

            if (subtitle) {
                const cardSubtitle = document.createElement("div");
                cardSubtitle.textContent = subtitle;
                cardSubtitle.style.fontSize = "12px";
                cardSubtitle.style.opacity = "0.8";
                card.appendChild(cardSubtitle);
            }

            return card;
        }

        // Create overview cards - show remaining days and completion dates for different work rates
        overviewContainer.appendChild(createOverviewCard("Remaining Days", workingDaysRemaining, "#c62828", "Total work days"));
        overviewContainer.appendChild(createOverviewCard("Normal Pace", normalCompletionDate.toLocaleDateString(), "#7b1fa2", "1 day work/day"));
        overviewContainer.appendChild(createOverviewCard("Fast Pace", fastCompletionDate.toLocaleDateString(), "#1976D2", "2 days work/day"));
        overviewContainer.appendChild(createOverviewCard("Very Fast Pace", veryFastCompletionDate.toLocaleDateString(), "#2e7d32", "3 days work/day"));

        mainContainer.appendChild(overviewContainer);

        // --- Create Add New Task Button ---
        const addTaskContainer = document.createElement("div");
        addTaskContainer.style.marginBottom = "30px";
        addTaskContainer.style.textAlign = "center";

        const addTaskButton = document.createElement("button");
        addTaskButton.textContent = "+ Add New Task";
        addTaskButton.style.backgroundColor = "#0d47a1";
        addTaskButton.style.color = "#ffffff";
        addTaskButton.style.border = "none";
        addTaskButton.style.borderRadius = "8px";
        addTaskButton.style.padding = "15px 30px";
        addTaskButton.style.fontSize = "16px";
        addTaskButton.style.fontWeight = "bold";
        addTaskButton.style.cursor = "pointer";
        addTaskButton.style.boxShadow = "0 3px 6px rgba(0,0,0,0.2)";
        addTaskButton.style.transition = "all 0.2s ease";

        addTaskButton.addEventListener("mouseover", () => {
            addTaskButton.style.backgroundColor = "#083378";
            addTaskButton.style.transform = "translateY(-2px)";
            addTaskButton.style.boxShadow = "0 5px 10px rgba(0,0,0,0.25)";
        });

        addTaskButton.addEventListener("mouseout", () => {
            addTaskButton.style.backgroundColor = "#0d47a1";
            addTaskButton.style.transform = "translateY(0)";
            addTaskButton.style.boxShadow = "0 3px 6px rgba(0,0,0,0.2)";
        });

        addTaskButton.addEventListener("click", () => {
            // Open the Add New Task file
            window.open("obsidian://open?vault=ProjectBaoli&file=01.%20GDD%2FAdd-New-Task.md");
        });

        addTaskContainer.appendChild(addTaskButton);
        mainContainer.appendChild(addTaskContainer);

        // --- Create Categories Section ---
        const categoriesContainer = document.createElement("div");
        categoriesContainer.style.marginBottom = "30px";

        const categoriesTitle = document.createElement("div");
        categoriesTitle.textContent = "Project Categories";
        categoriesTitle.style.fontSize = "20px";
        categoriesTitle.style.fontWeight = "bold";
        categoriesTitle.style.marginBottom = "20px";
        categoriesTitle.style.color = "#0d47a1";
        categoriesTitle.style.letterSpacing = "0.3px";
        categoriesContainer.appendChild(categoriesTitle);

        // Function to get status color
        function getStatusColor(status) {
            switch(status) {
                case "completed": return "#2e7d32";
                case "ongoing": return "#f57c00";
                case "pending": return "#c62828";
                default: return "#666666";
            }
        }

        // Function to get priority color
        function getPriorityColor(priority) {
            switch(priority) {
                case "high": return "#d32f2f";
                case "medium": return "#f57c00";
                case "low": return "#388e3c";
                default: return "#666666";
            }
        }

        // Function to create category card
        function createCategoryCard(categoryName, tasks) {
            const categoryCard = document.createElement("div");
            categoryCard.style.backgroundColor = "#f8f9fa";
            categoryCard.style.borderRadius = "8px";
            categoryCard.style.padding = "20px";
            categoryCard.style.marginBottom = "20px";
            categoryCard.style.border = "1px solid #e0e0e0";
            categoryCard.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";

            // Category header
            const categoryHeader = document.createElement("div");
            categoryHeader.style.display = "flex";
            categoryHeader.style.justifyContent = "space-between";
            categoryHeader.style.alignItems = "center";
            categoryHeader.style.marginBottom = "15px";
            categoryHeader.style.paddingBottom = "10px";
            categoryHeader.style.borderBottom = "1px solid #e0e0e0";

            const categoryTitle = document.createElement("div");
            categoryTitle.textContent = categoryName;
            categoryTitle.style.fontSize = "18px";
            categoryTitle.style.fontWeight = "bold";
            categoryTitle.style.color = "#0d47a1";

            // Calculate category stats (only remaining tasks)
            let categoryOngoing = 0;
            let categoryPending = 0;

            tasks.forEach(task => {
                if (task.status === "ongoing") {
                    categoryOngoing += task.days;
                } else {
                    categoryPending += task.days;
                }
            });

            const categoryTotal = categoryOngoing + categoryPending;
            const categoryStats = document.createElement("div");
            categoryStats.style.fontSize = "14px";
            categoryStats.style.color = "#666666";
            categoryStats.textContent = `${categoryTotal} days remaining • ${categoryOngoing} ongoing • ${categoryPending} pending`;

            categoryHeader.appendChild(categoryTitle);
            categoryHeader.appendChild(categoryStats);
            categoryCard.appendChild(categoryHeader);

            // Category progress bar (only showing ongoing vs pending)
            const categoryProgressContainer = document.createElement("div");
            categoryProgressContainer.style.width = "100%";
            categoryProgressContainer.style.height = "8px";
            categoryProgressContainer.style.backgroundColor = "#e0e0e0";
            categoryProgressContainer.style.borderRadius = "4px";
            categoryProgressContainer.style.overflow = "hidden";
            categoryProgressContainer.style.marginBottom = "15px";

            if (categoryTotal > 0) {
                const categoryOngoingProgress = (categoryOngoing / categoryTotal) * 100;

                const categoryOngoingBar = document.createElement("div");
                categoryOngoingBar.style.width = `${categoryOngoingProgress}%`;
                categoryOngoingBar.style.height = "100%";
                categoryOngoingBar.style.backgroundColor = "#f57c00";
                categoryOngoingBar.style.float = "left";

                categoryProgressContainer.appendChild(categoryOngoingBar);
            }

            categoryCard.appendChild(categoryProgressContainer);

            // Tasks list
            const tasksContainer = document.createElement("div");
            tasksContainer.style.display = "grid";
            tasksContainer.style.gap = "10px";

            tasks.forEach((task, index) => {
                const taskRow = document.createElement("div");
                taskRow.style.display = "flex";
                taskRow.style.alignItems = "center";
                taskRow.style.justifyContent = "space-between";
                taskRow.style.padding = "10px 15px";
                taskRow.style.backgroundColor = "#ffffff";
                taskRow.style.borderRadius = "6px";
                taskRow.style.border = "1px solid #e0e0e0";
                taskRow.style.transition = "all 0.2s ease";

                // Task info section
                const taskInfo = document.createElement("div");
                taskInfo.style.display = "flex";
                taskInfo.style.alignItems = "center";
                taskInfo.style.gap = "15px";
                taskInfo.style.flex = "1";

                // Status indicator
                const statusIndicator = document.createElement("div");
                statusIndicator.style.width = "12px";
                statusIndicator.style.height = "12px";
                statusIndicator.style.borderRadius = "50%";
                statusIndicator.style.backgroundColor = getStatusColor(task.status);
                statusIndicator.style.flexShrink = "0";

                // Task name
                const taskName = document.createElement("div");
                taskName.textContent = task.name;
                taskName.style.fontSize = "14px";
                taskName.style.fontWeight = "500";
                taskName.style.color = "#333333";
                taskName.style.flex = "1";

                // Status badge (for ongoing tasks)
                const statusBadge = document.createElement("div");
                if (task.status === "ongoing") {
                    statusBadge.textContent = "ONGOING";
                    statusBadge.style.fontSize = "10px";
                    statusBadge.style.fontWeight = "bold";
                    statusBadge.style.padding = "2px 6px";
                    statusBadge.style.borderRadius = "3px";
                    statusBadge.style.backgroundColor = "#f57c00";
                    statusBadge.style.color = "#ffffff";
                    statusBadge.style.flexShrink = "0";
                    statusBadge.style.marginRight = "5px";
                }

                // Priority badge
                const priorityBadge = document.createElement("div");
                priorityBadge.textContent = task.priority.toUpperCase();
                priorityBadge.style.fontSize = "10px";
                priorityBadge.style.fontWeight = "bold";
                priorityBadge.style.padding = "2px 6px";
                priorityBadge.style.borderRadius = "3px";
                priorityBadge.style.backgroundColor = getPriorityColor(task.priority);
                priorityBadge.style.color = "#ffffff";
                priorityBadge.style.flexShrink = "0";

                taskInfo.appendChild(statusIndicator);
                taskInfo.appendChild(taskName);
                if (task.status === "ongoing") {
                    taskInfo.appendChild(statusBadge);
                }
                taskInfo.appendChild(priorityBadge);

                // Task controls section
                const taskControls = document.createElement("div");
                taskControls.style.display = "flex";
                taskControls.style.alignItems = "center";
                taskControls.style.gap = "10px";

                // Days display and edit link
                const daysDisplay = document.createElement("div");
                daysDisplay.textContent = `${task.days} days`;
                daysDisplay.style.fontSize = "12px";
                daysDisplay.style.fontWeight = "bold";
                daysDisplay.style.color = "#0d47a1";
                daysDisplay.style.padding = "5px 10px";
                daysDisplay.style.backgroundColor = "#e3f2fd";
                daysDisplay.style.borderRadius = "4px";
                daysDisplay.style.minWidth = "60px";
                daysDisplay.style.textAlign = "center";

                // Edit button
                const editButton = document.createElement("button");
                editButton.textContent = "Edit";
                editButton.style.padding = "5px 10px";
                editButton.style.border = "1px solid #0d47a1";
                editButton.style.borderRadius = "4px";
                editButton.style.fontSize = "12px";
                editButton.style.backgroundColor = "#1976D2";
                editButton.style.color = "#ffffff";
                editButton.style.fontWeight = "500";
                editButton.style.cursor = "pointer";
                editButton.style.transition = "all 0.2s ease";

                editButton.addEventListener("mouseover", () => {
                    editButton.style.backgroundColor = "#1565C0";
                });

                editButton.addEventListener("mouseout", () => {
                    editButton.style.backgroundColor = "#1976D2";
                });

                editButton.addEventListener("click", () => {
                    // Open the category file for editing
                    const categoryFileName = categoryName.replace(/\s+/g, '');
                    window.open(`obsidian://open?vault=ProjectBaoli&file=99%20-%20data%2FequitySplit%2FWork%2F${categoryFileName}.md`);
                });

                taskControls.appendChild(daysDisplay);
                taskControls.appendChild(editButton);

                taskRow.appendChild(taskInfo);
                taskRow.appendChild(taskControls);

                // Add hover effect
                taskRow.addEventListener("mouseover", () => {
                    taskRow.style.backgroundColor = "#f5f5f5";
                    taskRow.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
                });

                taskRow.addEventListener("mouseout", () => {
                    taskRow.style.backgroundColor = "#ffffff";
                    taskRow.style.boxShadow = "none";
                });

                tasksContainer.appendChild(taskRow);
            });

            categoryCard.appendChild(tasksContainer);
            return categoryCard;
        }

        // Create category cards
        categories.forEach(category => {
            const tasks = tasksData[category] || [];
            if (tasks.length > 0) {
                const categoryCard = createCategoryCard(category, tasks);
                categoriesContainer.appendChild(categoryCard);
            }
        });

        mainContainer.appendChild(categoriesContainer);

    } catch (error) {
        projectLogDebug("ERROR:", error);
        dv.el("p", `❌ **ERROR:** ${error.message}. Check console for details.`);
    }
})();
```

## How to Use This Project Manager

1. **View Remaining Work**: The top cards show only remaining days and estimated completion date.
2. **Monitor Categories**: Each category shows remaining tasks with ongoing vs pending breakdown.
3. **Edit Tasks**: Click the "Edit" button next to any task to open its category file and modify days, status, or priority.
4. **Add New Tasks**: Click the "+ Add New Task" button to open the task creation form.
5. **Mark Tasks Complete**: Use the "✅ Complete" button in category files to remove completed tasks.

The system automatically calculates:
- Project completion date based on remaining work only
- Working days vs calendar days (assumes 5-day work week)
- Real-time updates from Obsidian properties in category files

**Note**: All changes are saved directly to the markdown files using Obsidian's meta-bind plugin. Completed tasks are automatically hidden from the display.
