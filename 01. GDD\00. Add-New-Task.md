---
taskName: ""
category: ""
days: 1
status: pending
priority: medium
created: 2025-06-16T00:00
updated: 2025-06-16T00:00
---

# Add New Task

## Task Details

**Task Name:**
```meta-bind
INPUT[text:taskName]
```

**Category:**
```meta-bind
INPUT[inlineSelect(option(Animation), option(Mechanics), option(Niagara), option(Post Process), option(3D), option(UI), option(Lighting, Look and Feel), option(Optimization and Testing), option(Sound), option(Buffer)):category]
```

**Estimated Days:**
```meta-bind
INPUT[number:days]
```

**Status:**
```meta-bind
INPUT[inlineSelect(option(pending), option(ongoing)):status]
```

**Priority:**
```meta-bind
INPUT[inlineSelect(option(high), option(medium), option(low)):priority]
```

## Actions

```meta-bind-button
label: "✅ Create Task"
icon: "plus"
tooltip: "Add this task to the selected category"
id: "create-task"
style: primary
actions:
  - type: updateMetadata
    bindTarget: updated
    evaluate: false
    value: "2025-06-16T12:00"
```

```meta-bind-button
label: "🔄 Reset Form"
icon: "reset"
tooltip: "Clear all fields"
id: "reset-form"
style: default
actions:
  - type: updateMetadata
    bindTarget: taskName
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: category
    evaluate: false
    value: ""
  - type: updateMetadata
    bindTarget: days
    evaluate: false
    value: 1
  - type: updateMetadata
    bindTarget: status
    evaluate: false
    value: "pending"
  - type: updateMetadata
    bindTarget: priority
    evaluate: false
    value: "medium"
```

## Auto-Generated Code

After filling out the form above, the code below will be automatically generated for you to copy into the appropriate category file:

```dataviewjs
// Get the current page data
const currentPage = dv.current();
const taskName = currentPage.taskName || "";
const category = currentPage.category || "";
const days = currentPage.days || 1;
const status = currentPage.status || "pending";
const priority = currentPage.priority || "medium";

if (taskName && category) {
    // Convert task name to property key format
    const taskKey = taskName.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
    
    // Generate YAML frontmatter properties
    const yamlProperties = `${taskKey}-days: ${days}
${taskKey}-status: ${status}
${taskKey}-priority: ${priority}`;
    
    // Create container
    const container = dv.el("div", "", {cls: "task-generator-container"});
    container.style.backgroundColor = "#f8f9fa";
    container.style.border = "2px solid #0d47a1";
    container.style.borderRadius = "8px";
    container.style.padding = "20px";
    container.style.marginTop = "20px";
    
    // Title
    const title = document.createElement("h3");
    title.textContent = `Generated Code for "${taskName}" in ${category}`;
    title.style.color = "#1a1a1a";
    title.style.marginBottom = "15px";
    container.appendChild(title);
    
    // Instructions
    const instructions = document.createElement("p");
    instructions.innerHTML = `<strong>Instructions:</strong><br>
    1. Open <code>99 - data/equitySplit/Work/${category}.md</code><br>
    2. Add the YAML properties to the frontmatter<br>
    3. The task will automatically appear in the main dashboard`;
    instructions.style.marginBottom = "15px";
    instructions.style.color = "#333333";
    container.appendChild(instructions);
    
    // YAML Section
    const yamlTitle = document.createElement("h4");
    yamlTitle.textContent = "Add to YAML Frontmatter:";
    yamlTitle.style.color = "#1a1a1a";
    container.appendChild(yamlTitle);
    
    const yamlCode = document.createElement("pre");
    yamlCode.textContent = yamlProperties;
    yamlCode.style.backgroundColor = "#e3f2fd";
    yamlCode.style.color = "#1a1a1a";
    yamlCode.style.padding = "10px";
    yamlCode.style.borderRadius = "4px";
    yamlCode.style.border = "1px solid #bbdefb";
    yamlCode.style.fontSize = "12px";
    yamlCode.style.overflow = "auto";
    container.appendChild(yamlCode);
    
    // Copy YAML button
    const copyYamlBtn = document.createElement("button");
    copyYamlBtn.textContent = "📋 Copy YAML";
    copyYamlBtn.style.backgroundColor = "#1976D2";
    copyYamlBtn.style.color = "white";
    copyYamlBtn.style.border = "none";
    copyYamlBtn.style.padding = "8px 16px";
    copyYamlBtn.style.borderRadius = "4px";
    copyYamlBtn.style.cursor = "pointer";
    copyYamlBtn.style.marginBottom = "20px";
    copyYamlBtn.addEventListener("click", () => {
        navigator.clipboard.writeText(yamlProperties);
        copyYamlBtn.textContent = "✅ Copied!";
        setTimeout(() => copyYamlBtn.textContent = "📋 Copy YAML", 2000);
    });
    container.appendChild(copyYamlBtn);
    
    // Quick link to category file
    const quickLink = document.createElement("div");
    quickLink.style.marginTop = "15px";
    quickLink.style.padding = "10px";
    quickLink.style.backgroundColor = "#fff3e0";
    quickLink.style.borderRadius = "4px";
    quickLink.style.border = "1px solid #ffcc02";
    
    const linkBtn = document.createElement("button");
    linkBtn.textContent = `🔗 Open ${category}.md`;
    linkBtn.style.backgroundColor = "#ff9800";
    linkBtn.style.color = "white";
    linkBtn.style.border = "none";
    linkBtn.style.padding = "8px 16px";
    linkBtn.style.borderRadius = "4px";
    linkBtn.style.cursor = "pointer";
    linkBtn.addEventListener("click", () => {
        const categoryFileName = category.replace(/\s+/g, '%20');
        window.open(`obsidian://open?vault=ProjectBaoli&file=99%20-%20data%2FequitySplit%2FWork%2F${categoryFileName}.md`);
    });
    quickLink.appendChild(linkBtn);
    container.appendChild(quickLink);
    
} else {
    const placeholder = dv.el("div", "👆 Fill out the form above to generate task code", {cls: "placeholder"});
    placeholder.style.textAlign = "center";
    placeholder.style.padding = "20px";
    placeholder.style.color = "#555555";
    placeholder.style.fontStyle = "italic";
}
```
